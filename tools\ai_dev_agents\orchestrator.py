"""
AI Development Workflow Orchestrator

Main orchestrator for coordinating all AI development agents.
"""

import os
import json
from typing import Any, Dict, List, Optional
from datetime import datetime
from pathlib import Path

from .base_agent import WorkflowContext, AgentResult, AgentChain
from .business_analyzer import BusinessAnalyzerAgent
from .domain_modeler import DomainModelerAgent
from .requirements_generator import RequirementsGeneratorAgent
from .prompt_builder import Prompt<PERSON><PERSON>erAgent
from .config_manager import ConfigManager


class AIDevWorkflowOrchestrator:
    """Main orchestrator for AI development workflow."""
    
    def __init__(self, llm=None, verbose: bool = False, config_path: Optional[str] = None,
                 model_preset: Optional[str] = None):
        """
        Initialize the orchestrator.

        Args:
            llm: Pre-configured LLM instance. If None, will create from config.
            verbose: Enable verbose logging.
            config_path: Path to configuration file.
            model_preset: Model preset to use from config.
        """
        self.verbose = verbose
        self.logger = logging.getLogger("orchestrator")

        # Initialize configuration manager
        self.config_manager = ConfigManager(config_path)

        # Setup logging from config
        system_config = self.config_manager.get_system_config()
        log_level = system_config.get("log_level", "INFO")
        if verbose or log_level == "DEBUG":
            logging.basicConfig(level=getattr(logging, log_level))

        # Initialize LLM
        if llm is None:
            self.llm = self.config_manager.create_llm(model_preset)
            if self.llm is None:
                self.logger.warning("Failed to create LLM from config. Using None (mock mode).")
        else:
            self.llm = llm

        # Initialize agents
        self.agents = {
            "business_analyzer": BusinessAnalyzerAgent(self.llm, verbose),
            "domain_modeler": DomainModelerAgent(self.llm, verbose),
            "requirements_generator": RequirementsGeneratorAgent(self.llm, verbose),
            "prompt_builder": PromptBuilderAgent(self.llm, verbose)
        }

        # Workflow results
        self.results: Dict[str, AgentResult] = {}
        self.context: Optional[WorkflowContext] = None
    
    def create_context(self, project_root: str, project_rules: str = "") -> WorkflowContext:
        """Create workflow context with project information."""
        # Get project configuration
        project_config = self.config_manager.get_project_config()

        # Detect existing modules
        existing_modules = self._detect_existing_modules(project_root)

        # Merge with configured existing modules
        configured_modules = project_config.get("existing_modules", [])
        all_modules = list(set(existing_modules + configured_modules))

        # Get tech stack from config
        tech_stack = project_config.get("tech_stack", [
            "FastAPI", "SQLAlchemy", "Pydantic", "PostgreSQL",
            "Alembic", "Pytest", "Python 3.11+"
        ])

        # Get architecture style from config
        architecture_style = project_config.get("architecture_style",
                                               "Domain-Driven Design (DDD) with 4-layer architecture")

        # Load project rules if not provided
        if not project_rules:
            project_rules = self._load_project_rules(project_root)

        return WorkflowContext(
            project_root=project_root,
            project_rules=project_rules,
            existing_modules=all_modules,
            tech_stack=tech_stack,
            architecture_style=architecture_style,
            intermediate_results={}
        )
    
    def execute_full_workflow(
        self, 
        prd_content: str, 
        project_root: str,
        output_dir: str = "output",
        selected_modules: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Execute the complete AI development workflow."""
        try:
            # Create context
            self.context = self.create_context(project_root)
            
            # Ensure output directory exists
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
            
            # Step 1: Business Analysis
            print("🔍 Step 1: Analyzing business requirements...")
            business_result = self._execute_business_analysis(prd_content)
            if not business_result.success:
                return self._create_error_result("Business analysis failed", business_result.errors)
            
            # Step 2: Domain Modeling
            print("🏗️ Step 2: Creating domain models...")
            domain_result = self._execute_domain_modeling(business_result.data)
            if not domain_result.success:
                return self._create_error_result("Domain modeling failed", domain_result.errors)
            
            # Step 3: Identify modules to process
            available_modules = self._extract_modules_from_domain_model(domain_result.data)
            if selected_modules is None:
                selected_modules = self._interactive_module_selection(available_modules)
            
            # Step 4: Generate requirements and prompts for each module
            module_results = {}
            for module_name in selected_modules:
                print(f"📋 Step 3: Generating requirements for {module_name}...")
                
                # Filter domain model for this module
                module_domain_model = self._filter_domain_model_for_module(
                    domain_result.data, module_name
                )
                
                # Generate requirements
                req_result = self._execute_requirements_generation(module_domain_model)
                if not req_result.success:
                    print(f"⚠️ Warning: Requirements generation failed for {module_name}")
                    continue
                
                print(f"🤖 Step 4: Building AI prompt for {module_name}...")
                
                # Build AI prompt
                prompt_result = self._execute_prompt_building(req_result.data)
                if not prompt_result.success:
                    print(f"⚠️ Warning: Prompt building failed for {module_name}")
                    continue
                
                # Save results
                module_results[module_name] = {
                    "requirements": req_result.data,
                    "prompt": prompt_result.data,
                    "files": self._save_module_results(
                        module_name, req_result.data, prompt_result.data, output_path
                    )
                }
                
                print(f"✅ Module {module_name} completed successfully!")
            
            # Save workflow summary
            summary = self._create_workflow_summary(
                business_result, domain_result, module_results
            )
            summary_file = output_path / "workflow_summary.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n🎉 Workflow completed! Results saved to {output_dir}/")
            print(f"📊 Summary: {summary_file}")
            
            return {
                "success": True,
                "business_analysis": business_result.data,
                "domain_model": domain_result.data,
                "modules": module_results,
                "summary_file": str(summary_file),
                "output_directory": output_dir
            }
            
        except Exception as e:
            return self._create_error_result("Workflow execution failed", [str(e)])
    
    def _execute_business_analysis(self, prd_content: str) -> AgentResult:
        """Execute business analysis step."""
        agent = self.agents["business_analyzer"]
        input_data = {"prd_content": prd_content}
        return agent.execute(input_data, self.context)
    
    def _execute_domain_modeling(self, business_analysis: Dict[str, Any]) -> AgentResult:
        """Execute domain modeling step."""
        agent = self.agents["domain_modeler"]
        return agent.execute(business_analysis, self.context)
    
    def _execute_requirements_generation(self, domain_model: Dict[str, Any]) -> AgentResult:
        """Execute requirements generation step."""
        agent = self.agents["requirements_generator"]
        return agent.execute(domain_model, self.context)
    
    def _execute_prompt_building(self, requirements: Dict[str, Any]) -> AgentResult:
        """Execute prompt building step."""
        agent = self.agents["prompt_builder"]
        return agent.execute(requirements, self.context)
    
    def _detect_existing_modules(self, project_root: str) -> List[str]:
        """Detect existing modules in the project."""
        modules = []
        modules_path = Path(project_root) / "modules"
        
        if modules_path.exists():
            for item in modules_path.iterdir():
                if item.is_dir() and not item.name.startswith('.'):
                    modules.append(item.name)
        
        return sorted(modules)
    
    def _load_project_rules(self, project_root: str) -> str:
        """Load project rules from .roo/rules/rules.md if exists."""
        rules_file = Path(project_root) / ".roo" / "rules" / "rules.md"
        
        if rules_file.exists():
            try:
                with open(rules_file, 'r', encoding='utf-8') as f:
                    return f.read()
            except Exception:
                pass
        
        # Return default rules if file not found
        return """
# Default Project Rules

- Use DDD 4-layer architecture
- UUID primary keys for all entities
- English documentation and comments
- PEP 8 code style
- Type hints required
- Test coverage > 80%
"""
    
    def _extract_modules_from_domain_model(self, domain_model: Dict[str, Any]) -> List[str]:
        """Extract module names from domain model."""
        modules = []
        
        # Extract from bounded contexts
        for context in domain_model.get("bounded_contexts", []):
            context_name = context.get("name", "").lower().replace(" ", "_")
            if context_name and context_name not in modules:
                modules.append(context_name)
        
        # Extract from aggregates
        for aggregate in domain_model.get("aggregates", []):
            aggregate_name = aggregate.get("name", "").lower().replace(" ", "_")
            if aggregate_name and aggregate_name not in modules:
                modules.append(aggregate_name)
        
        return modules
    
    def _interactive_module_selection(self, available_modules: List[str]) -> List[str]:
        """Interactive module selection (fallback to all modules if no interaction)."""
        if not available_modules:
            return []
        
        print(f"\n📦 Available modules: {', '.join(available_modules)}")
        print("💡 Tip: You can select specific modules or process all")
        
        # For non-interactive environments, return all modules
        return available_modules
    
    def _filter_domain_model_for_module(
        self, domain_model: Dict[str, Any], module_name: str
    ) -> Dict[str, Any]:
        """Filter domain model to include only relevant parts for a specific module."""
        filtered = {}
        
        # Filter bounded contexts
        filtered["bounded_contexts"] = [
            ctx for ctx in domain_model.get("bounded_contexts", [])
            if ctx.get("name", "").lower().replace(" ", "_") == module_name
        ]
        
        # Filter aggregates
        filtered["aggregates"] = [
            agg for agg in domain_model.get("aggregates", [])
            if agg.get("name", "").lower().replace(" ", "_") == module_name or
               agg.get("bounded_context", "").lower().replace(" ", "_") == module_name
        ]
        
        # Filter entities
        relevant_aggregates = {agg.get("name") for agg in filtered["aggregates"]}
        filtered["domain_entities"] = [
            entity for entity in domain_model.get("domain_entities", [])
            if entity.get("aggregate") in relevant_aggregates
        ]
        
        # Filter other components similarly
        for key in ["value_objects", "domain_services", "repositories", "domain_events"]:
            filtered[key] = domain_model.get(key, [])
        
        return filtered
    
    def _save_module_results(
        self, 
        module_name: str, 
        requirements: Dict[str, Any], 
        prompt_data: Dict[str, Any],
        output_path: Path
    ) -> Dict[str, str]:
        """Save module results to files."""
        files = {}
        
        # Save requirements
        req_file = output_path / f"{module_name}_requirements.json"
        with open(req_file, 'w', encoding='utf-8') as f:
            json.dump(requirements, f, indent=2, ensure_ascii=False, default=str)
        files["requirements"] = str(req_file)
        
        # Save AI prompt
        prompt_file = output_path / f"{module_name}_ai_prompt.md"
        with open(prompt_file, 'w', encoding='utf-8') as f:
            f.write(prompt_data.get("prompt_content", ""))
        files["prompt"] = str(prompt_file)
        
        return files
    
    def _create_workflow_summary(
        self,
        business_result: AgentResult,
        domain_result: AgentResult,
        module_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create workflow execution summary."""
        return {
            "execution_timestamp": datetime.now().isoformat(),
            "workflow_status": "completed",
            "business_analysis": {
                "success": business_result.success,
                "entities_count": len(business_result.data.get("core_entities", [])),
                "requirements_count": len(business_result.data.get("functional_requirements", [])),
                "user_stories_count": len(business_result.data.get("user_stories", []))
            },
            "domain_modeling": {
                "success": domain_result.success,
                "bounded_contexts_count": len(domain_result.data.get("bounded_contexts", [])),
                "aggregates_count": len(domain_result.data.get("aggregates", [])),
                "entities_count": len(domain_result.data.get("domain_entities", []))
            },
            "modules_processed": list(module_results.keys()),
            "total_modules": len(module_results),
            "output_files": {
                module: result["files"] for module, result in module_results.items()
            }
        }
    
    def _create_error_result(self, message: str, errors: List[str]) -> Dict[str, Any]:
        """Create error result."""
        return {
            "success": False,
            "error": message,
            "details": errors,
            "timestamp": datetime.now().isoformat()
        }
