"""
Command Line Interface for AI Development Agents

Provides CLI access to the AI development workflow.
"""

import argparse
import sys
import json
from pathlib import Path
from typing import List, Optional

from .orchestrator import AIDevWorkflowOrchestrator


def load_prd_content(prd_path: str) -> str:
    """Load PRD content from file."""
    try:
        with open(prd_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"❌ Error: PRD file not found: {prd_path}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error reading PRD file: {e}")
        sys.exit(1)


def parse_module_list(modules_str: str) -> List[str]:
    """Parse comma-separated module list."""
    if not modules_str:
        return []
    return [m.strip() for m in modules_str.split(',') if m.strip()]


def setup_llm():
    """Setup LLM for agents (placeholder for now)."""
    # TODO: Add LLM configuration when langchain is available
    print("⚠️ Warning: LLM not configured. Agents will run in mock mode.")
    return None


def cmd_full_workflow(args):
    """Execute full workflow command."""
    print("🚀 Starting AI Development Workflow...")
    print(f"📄 PRD File: {args.prd_file}")
    print(f"📁 Output Directory: {args.output}")
    
    # Load PRD content
    prd_content = load_prd_content(args.prd_file)
    print(f"📊 PRD Content Length: {len(prd_content)} characters")
    
    # Parse selected modules
    selected_modules = None
    if args.modules:
        selected_modules = parse_module_list(args.modules)
        print(f"🎯 Selected Modules: {', '.join(selected_modules)}")
    
    # Setup LLM
    llm = setup_llm()
    
    # Create orchestrator
    orchestrator = AIDevWorkflowOrchestrator(llm=llm, verbose=args.verbose)
    
    # Execute workflow
    result = orchestrator.execute_full_workflow(
        prd_content=prd_content,
        project_root=args.project_root,
        output_dir=args.output,
        selected_modules=selected_modules
    )
    
    # Handle results
    if result.get("success"):
        print("\n✅ Workflow completed successfully!")
        print(f"📁 Results saved to: {result['output_directory']}")
        print(f"📊 Summary: {result['summary_file']}")
        
        # Print module summary
        modules = result.get("modules", {})
        if modules:
            print(f"\n📦 Processed Modules ({len(modules)}):")
            for module_name, module_data in modules.items():
                files = module_data.get("files", {})
                print(f"  • {module_name}")
                if "requirements" in files:
                    print(f"    - Requirements: {files['requirements']}")
                if "prompt" in files:
                    print(f"    - AI Prompt: {files['prompt']}")
    else:
        print(f"\n❌ Workflow failed: {result.get('error', 'Unknown error')}")
        if result.get("details"):
            for detail in result["details"]:
                print(f"   - {detail}")
        sys.exit(1)


def cmd_business_analysis(args):
    """Execute business analysis command."""
    print("🔍 Running Business Analysis...")
    
    # Load PRD content
    prd_content = load_prd_content(args.prd_file)
    
    # Setup LLM and orchestrator
    llm = setup_llm()
    orchestrator = AIDevWorkflowOrchestrator(llm=llm, verbose=args.verbose)
    
    # Create context
    context = orchestrator.create_context(args.project_root)
    
    # Execute business analysis
    result = orchestrator._execute_business_analysis(prd_content)
    
    # Save results
    if result.success:
        output_file = args.output or "business_analysis.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result.data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ Business analysis completed!")
        print(f"📁 Results saved to: {output_file}")
        print(f"📊 Entities: {len(result.data.get('core_entities', []))}")
        print(f"📊 Requirements: {len(result.data.get('functional_requirements', []))}")
        print(f"📊 User Stories: {len(result.data.get('user_stories', []))}")
    else:
        print(f"❌ Business analysis failed: {', '.join(result.errors)}")
        sys.exit(1)


def cmd_domain_modeling(args):
    """Execute domain modeling command."""
    print("🏗️ Running Domain Modeling...")
    
    # Load business analysis
    try:
        with open(args.business_analysis, 'r', encoding='utf-8') as f:
            business_data = json.load(f)
    except Exception as e:
        print(f"❌ Error loading business analysis: {e}")
        sys.exit(1)
    
    # Setup LLM and orchestrator
    llm = setup_llm()
    orchestrator = AIDevWorkflowOrchestrator(llm=llm, verbose=args.verbose)
    
    # Create context
    context = orchestrator.create_context(args.project_root)
    
    # Execute domain modeling
    result = orchestrator._execute_domain_modeling(business_data)
    
    # Save results
    if result.success:
        output_file = args.output or "domain_model.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result.data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ Domain modeling completed!")
        print(f"📁 Results saved to: {output_file}")
        print(f"📊 Bounded Contexts: {len(result.data.get('bounded_contexts', []))}")
        print(f"📊 Aggregates: {len(result.data.get('aggregates', []))}")
        print(f"📊 Entities: {len(result.data.get('domain_entities', []))}")
    else:
        print(f"❌ Domain modeling failed: {', '.join(result.errors)}")
        sys.exit(1)


def cmd_generate_requirements(args):
    """Execute requirements generation command."""
    print("📋 Generating Technical Requirements...")
    
    # Load domain model
    try:
        with open(args.domain_model, 'r', encoding='utf-8') as f:
            domain_data = json.load(f)
    except Exception as e:
        print(f"❌ Error loading domain model: {e}")
        sys.exit(1)
    
    # Filter for specific module if provided
    if args.module:
        print(f"🎯 Filtering for module: {args.module}")
        # TODO: Implement module filtering logic
    
    # Setup LLM and orchestrator
    llm = setup_llm()
    orchestrator = AIDevWorkflowOrchestrator(llm=llm, verbose=args.verbose)
    
    # Create context
    context = orchestrator.create_context(args.project_root)
    
    # Execute requirements generation
    result = orchestrator._execute_requirements_generation(domain_data)
    
    # Save results
    if result.success:
        output_file = args.output or f"{args.module or 'module'}_requirements.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result.data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ Requirements generation completed!")
        print(f"📁 Results saved to: {output_file}")
        
        # Print summary
        data = result.data
        print(f"📊 User Stories: {len(data.get('user_stories', []))}")
        print(f"📊 API Endpoints: {len(data.get('api_design', {}).get('endpoints', []))}")
        print(f"📊 Database Tables: {len(data.get('data_models', {}).get('tables', []))}")
    else:
        print(f"❌ Requirements generation failed: {', '.join(result.errors)}")
        sys.exit(1)


def cmd_build_prompt(args):
    """Execute prompt building command."""
    print("🤖 Building AI Development Prompt...")
    
    # Load requirements
    try:
        with open(args.requirements, 'r', encoding='utf-8') as f:
            requirements_data = json.load(f)
    except Exception as e:
        print(f"❌ Error loading requirements: {e}")
        sys.exit(1)
    
    # Setup LLM and orchestrator
    llm = setup_llm()
    orchestrator = AIDevWorkflowOrchestrator(llm=llm, verbose=args.verbose)
    
    # Create context
    context = orchestrator.create_context(args.project_root)
    
    # Execute prompt building
    result = orchestrator._execute_prompt_building(requirements_data)
    
    # Save results
    if result.success:
        module_name = result.data.get("module_name", "module")
        output_file = args.output or f"{module_name}_ai_prompt.md"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result.data.get("prompt_content", ""))
        
        print(f"✅ AI prompt building completed!")
        print(f"📁 Results saved to: {output_file}")
        print(f"📊 Word Count: {result.data.get('word_count', 0)}")
        print(f"📊 Estimated Tokens: {result.data.get('estimated_tokens', 0)}")
    else:
        print(f"❌ Prompt building failed: {', '.join(result.errors)}")
        sys.exit(1)


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="AI Development Workflow - Intelligent code generation from PRD to AI prompts",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Full workflow
  python -m tools.ai_dev_agents.cli workflow design/mcp-market-prd.txt
  
  # Step by step
  python -m tools.ai_dev_agents.cli business-analysis design/mcp-market-prd.txt
  python -m tools.ai_dev_agents.cli domain-modeling business_analysis.json
  python -m tools.ai_dev_agents.cli generate-requirements domain_model.json --module mcp_server
  python -m tools.ai_dev_agents.cli build-prompt mcp_server_requirements.json
"""
    )
    
    # Global arguments
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    parser.add_argument("--project-root", default=".", help="Project root directory (default: current directory)")
    
    # Subcommands
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Full workflow command
    workflow_parser = subparsers.add_parser("workflow", help="Execute full workflow")
    workflow_parser.add_argument("prd_file", help="Path to PRD file")
    workflow_parser.add_argument("--output", "-o", default="output", help="Output directory")
    workflow_parser.add_argument("--modules", "-m", help="Comma-separated list of modules to process")
    
    # Business analysis command
    business_parser = subparsers.add_parser("business-analysis", help="Analyze business requirements")
    business_parser.add_argument("prd_file", help="Path to PRD file")
    business_parser.add_argument("--output", "-o", help="Output file path")
    
    # Domain modeling command
    domain_parser = subparsers.add_parser("domain-modeling", help="Create domain models")
    domain_parser.add_argument("business_analysis", help="Path to business analysis JSON file")
    domain_parser.add_argument("--output", "-o", help="Output file path")
    
    # Requirements generation command
    req_parser = subparsers.add_parser("generate-requirements", help="Generate technical requirements")
    req_parser.add_argument("domain_model", help="Path to domain model JSON file")
    req_parser.add_argument("--module", help="Specific module to generate requirements for")
    req_parser.add_argument("--output", "-o", help="Output file path")
    
    # Prompt building command
    prompt_parser = subparsers.add_parser("build-prompt", help="Build AI development prompt")
    prompt_parser.add_argument("requirements", help="Path to requirements JSON file")
    prompt_parser.add_argument("--output", "-o", help="Output file path")
    
    # Parse arguments
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # Execute command
    try:
        if args.command == "workflow":
            cmd_full_workflow(args)
        elif args.command == "business-analysis":
            cmd_business_analysis(args)
        elif args.command == "domain-modeling":
            cmd_domain_modeling(args)
        elif args.command == "generate-requirements":
            cmd_generate_requirements(args)
        elif args.command == "build-prompt":
            cmd_build_prompt(args)
        else:
            print(f"❌ Unknown command: {args.command}")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
