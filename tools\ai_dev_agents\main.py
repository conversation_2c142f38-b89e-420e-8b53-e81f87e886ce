#!/usr/bin/env python3
"""
AI Development Agents - Main Entry Point

This is the main entry point for the AI Development Agents toolkit.
"""

import sys
import argparse
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent))

from utils.cli import main as cli_main


def main():
    """Main entry point for the AI Development Agents toolkit."""
    parser = argparse.ArgumentParser(
        description="AI Development Agents - Intelligent workflow automation for software development",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py analyze --prd-path ./docs/prd.md
  python main.py workflow --prd-path ./docs/prd.md --output-dir ./output
  python main.py --help

For more information, visit: https://github.com/your-repo/ai-dev-agents
        """
    )
    
    # Add version argument
    parser.add_argument(
        '--version', 
        action='version', 
        version='AI Development Agents v1.0.0'
    )
    
    # Delegate to CLI main function
    cli_main()


if __name__ == "__main__":
    main()
