{"modules": [{"name": "mcp_server", "bounded_context": {"name": "server_catalog", "description": "Manages MCP server discovery, search, and public catalog functionality", "core_responsibility": "Primary responsibility: server catalog and discovery operations", "entities": [{"name": "MCPServer", "description": "Represents an MCP server in the marketplace", "attributes": ["name", "description", "author", "license_type", "programming_language", "supported_os", "github_url", "http_connection_url", "quality_score", "security_score", "license_score"], "business_methods": ["validate", "publish", "archive", "update_metadata"], "invariants": ["Server name must be unique", "Server must have at least one tool definition"]}, {"name": "ServerTool", "description": "Represents a tool provided by an MCP server", "attributes": ["name", "description", "example_prompts", "schema"], "business_methods": ["validate_schema"], "invariants": ["Tool name must be unique within server"]}, {"name": "ServerCategory", "description": "Represents a category for organizing MCP servers", "attributes": ["name", "description", "server_count"], "business_methods": ["update_count"], "invariants": ["Category name must be unique"]}], "repositories": [{"name": "MCPServerRepository", "entity_name": "MCPServer", "methods": ["find_by_id(id: UUID) -> Optional[MCPServer]", "save(mcp_server: MCPServer) -> MCPServer", "delete(id: UUID) -> bool", "find_all() -> List[MCPServer]", "find_by_category(category: str) -> List[MCPServer]", "search(query: str) -> List[MCPServer]"]}, {"name": "ServerToolRepository", "entity_name": "ServerTool", "methods": ["find_by_id(id: UUID) -> Optional[ServerTool]", "save(server_tool: ServerTool) -> ServerTool", "delete(id: UUID) -> bool", "find_all() -> List[ServerTool]"]}, {"name": "ServerCategoryRepository", "entity_name": "ServerCategory", "methods": ["find_by_id(id: UUID) -> Optional[ServerCategory]", "save(server_category: ServerCategory) -> ServerCategory", "delete(id: UUID) -> bool", "find_all() -> List[ServerCategory]"]}], "use_cases": ["List all MCP servers with pagination", "Search servers by keywords", "Filter servers by category", "View server details", "Browse servers by popularity"], "external_dependencies": ["GitHub API"]}, "dependencies": ["<PERSON><PERSON><PERSON>", "pydantic", "sqlalchemy"], "integration_points": ["User module for ownership"], "suggested_file_structure": {"domain": ["mcp_server_models.py", "server_tool_models.py", "server_category_models.py", "mcp_server_repositories.py", "server_tool_repositories.py", "server_category_repositories.py"], "application": ["server_catalog_service.py"], "infrastructure": ["mcp_server_orm.py", "server_tool_orm.py", "server_category_orm.py", "mcp_server_repositories.py", "server_tool_repositories.py", "server_category_repositories.py"], "interfaces": ["server_catalog_api.py", "server_catalog_schemas.py"]}}, {"name": "server_submission", "bounded_context": {"name": "server_submission", "description": "Handles server submission, review, and lifecycle management", "core_responsibility": "Primary responsibility: server submission and management operations", "entities": [{"name": "ServerSubmission", "description": "Represents a server submission for review", "attributes": ["server_data", "submitter_id", "status", "review_notes", "submitted_at", "reviewed_at"], "business_methods": ["approve", "reject", "request_changes"], "invariants": ["Submission must have valid server data", "Only pending submissions can be reviewed"]}], "repositories": [{"name": "ServerSubmissionRepository", "entity_name": "ServerSubmission", "methods": ["find_by_id(id: UUID) -> Optional[ServerSubmission]", "save(server_submission: ServerSubmission) -> ServerSubmission", "delete(id: UUID) -> bool", "find_all() -> List[ServerSubmission]"]}], "use_cases": ["Submit new server for review", "Review server submissions", "Update server information", "Manage server lifecycle"], "external_dependencies": []}, "dependencies": ["<PERSON><PERSON><PERSON>", "pydantic", "sqlalchemy", "modules.user"], "integration_points": ["User module for submitters", "MCP Server module for published servers"], "suggested_file_structure": {"domain": ["server_submission_models.py", "server_submission_repositories.py"], "application": ["server_submission_service.py"], "infrastructure": ["server_submission_orm.py", "server_submission_repositories.py"], "interfaces": ["server_submission_api.py", "server_submission_schemas.py"]}}]}