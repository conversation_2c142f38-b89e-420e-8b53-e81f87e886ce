# AI辅助开发流程操作指南

## 🎯 概述

这是一套完整的AI辅助业务功能开发流程，帮助您从产品需求文档（PRD）高效地生成符合项目架构标准的代码。

## 📋 前置条件

1. **项目环境**：确保您在项目根目录 `ai4se-mcp-hub`
2. **Python环境**：激活项目的虚拟环境
3. **PRD文档**：准备好完整的产品需求文档（Markdown格式）

## 🚀 具体操作步骤

### 方式一：一键完整流程（推荐新手）

```bash
# 在项目根目录运行
python -m scripts.ai_dev_workflow.cli workflow design/mcp-market-prd.txt
```

**交互式操作**：
1. 系统会自动分析PRD文档
2. 识别所有业务模块
3. 显示模块列表供您选择
4. 输入 `all` 选择所有模块，或输入 `1,3,5` 选择特定模块
5. 系统自动生成所有需求文档和AI提示词

### 方式二：分步骤执行（推荐有经验用户）

#### 第1步：分析PRD文档
```bash
python -m scripts.ai_dev_workflow.cli analyze-prd design/mcp-market-prd.txt -o prd_analysis.json
```
**输出**：`prd_analysis.json` - 结构化的业务需求信息

#### 第2步：识别领域模块
```bash
python -m scripts.ai_dev_workflow.cli identify-domains prd_analysis.json -o domain_analysis.json
```
**输出**：`domain_analysis.json` - DDD领域模块设计

#### 第3步：生成开发需求（针对特定模块）
```bash
python -m scripts.ai_dev_workflow.cli generate-req domain_analysis.json mcp_server -o mcp_server_requirements.md
```
**输出**：`mcp_server_requirements.md` - 详细的技术开发需求

#### 第4步：构建AI提示词
```bash
python -m scripts.ai_dev_workflow.cli build-prompt mcp_server_requirements.md -o mcp_server_ai_prompt.md
```
**输出**：`mcp_server_ai_prompt.md` - 包含完整项目上下文的AI开发提示词

## 🤖 使用AI提示词进行代码生成

### 步骤1：复制AI提示词
```bash
# 查看生成的AI提示词
cat mcp_server_ai_prompt.md
```

### 步骤2：与AI Agent交互
将提示词内容提供给您的AI助手，并说明：

```
请根据以下提示词和需求，为我实现mcp_server模块。请严格按照DDD架构和项目规范进行开发。

[粘贴AI提示词内容]

请从实现计划开始，然后逐步实现每个层次的代码。
```

### 步骤3：按DDD层次实现
AI会按照以下顺序实现：
1. **Domain层** - 实体模型和仓库接口
2. **Infrastructure层** - ORM模型和仓库实现
3. **Application层** - 应用服务和用例
4. **Interface层** - API端点和数据模式

### 步骤4：验证和测试
每完成一个层次：
```bash
# 运行代码质量检查
inv ci

# 运行测试
pytest tests/mcp_server/
```

## 📁 输出文件说明

### 分析文件
- `prd_analysis.json` - PRD文档的结构化分析结果
- `domain_analysis.json` - DDD领域模块识别结果

### 开发文档
- `{模块名}_requirements.md` - 详细的技术开发需求
  - 业务概览和用户故事
  - 领域模型设计
  - API端点规范
  - 数据库设计
  - 测试用例规范

### AI提示词
- `{模块名}_ai_prompt.md` - AI开发提示词
  - 完整的项目上下文
  - 架构约束和实现指南
  - 质量要求和验收标准
  - 项目规范和参考示例

## 🔧 高级用法

### 自定义输出目录
```bash
python -m scripts.ai_dev_workflow.cli workflow design/mcp-market-prd.txt -o my_output
```

### 只处理特定模块
```bash
python -m scripts.ai_dev_workflow.cli workflow design/mcp-market-prd.txt -m "mcp_server,server_submission"
```

### 非交互模式（自动选择所有模块）
```bash
python -m scripts.ai_dev_workflow.cli workflow design/mcp-market-prd.txt --auto
```

## 📝 最佳实践

### 1. PRD文档准备
- 使用清晰的Markdown结构
- 包含明确的功能需求和业务实体
- 提供详细的用户故事

### 2. 模块实现顺序
- 优先实现核心业务模块
- 考虑模块间依赖关系
- 从简单到复杂逐步推进

### 3. AI代码生成技巧
- 使用完整的AI提示词，包含所有项目上下文
- 要求AI按DDD层次顺序实现
- 每完成一个组件立即测试验证
- 保持与现有模块的一致性

### 4. 质量保证流程
```bash
# 1. 代码质量检查
inv ci

# 2. 运行测试
pytest tests/

# 3. 检查测试覆盖率
pytest --cov=modules

# 4. 类型检查
mypy modules/
```

## 🐛 常见问题解决

### 问题1：模块导入错误
```bash
# 确保在项目根目录
cd /path/to/ai4se-mcp-hub
python -m scripts.ai_dev_workflow.cli workflow design/mcp-market-prd.txt
```

### 问题2：PRD分析结果为空
- 检查PRD文档格式是否正确
- 确保文档编码为UTF-8
- 验证文件路径

### 问题3：生成的代码不符合规范
- 检查AI提示词是否完整
- 确保包含了项目规范部分
- 要求AI严格按照DDD架构实现

## 🔄 完整开发流程示例

```bash
# 1. 运行完整工作流程
python -m scripts.ai_dev_workflow.cli workflow design/mcp-market-prd.txt -o output

# 2. 选择要实现的模块（交互式）
# 输入：1,2 （选择前两个模块）

# 3. 使用生成的AI提示词与AI Agent交互
cat output/mcp_server_ai_prompt.md

# 4. AI生成代码后，运行质量检查
inv ci

# 5. 运行测试验证
pytest tests/mcp_server/

# 6. 如果测试通过，提交代码
git add .
git commit -m "feat(mcp_server): implement MCP server catalog module"
```

## 📊 工作流程总结

1. **需求分析** → PRD文档解析和业务理解
2. **架构设计** → DDD领域模块识别和边界划分
3. **需求细化** → 技术开发需求和验收标准
4. **AI准备** → 包含完整上下文的AI开发提示词
5. **代码生成** → AI按照DDD架构生成高质量代码
6. **质量验证** → 测试、代码检查和规范验证

通过这套流程，您可以高效地将业务需求转化为符合项目标准的高质量代码！
