#!/usr/bin/env python3
"""
Install Dependencies for AI Development Agents

This script installs the required dependencies for the AI development agents.
"""

import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stderr:
            print(f"   Error: {e.stderr.strip()}")
        return False

def main():
    """Install required dependencies."""
    print("🚀 Installing AI Development Agents Dependencies")
    print("=" * 50)
    
    # Check if we're in a virtual environment
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    
    if not in_venv:
        print("⚠️ Warning: You don't appear to be in a virtual environment")
        print("   It's recommended to use a virtual environment:")
        print("   python -m venv .venv")
        print("   .venv\\Scripts\\activate  # Windows")
        print("   source .venv/bin/activate  # macOS/Linux")
        print()
        
        response = input("Continue anyway? (y/N): ").strip().lower()
        if response != 'y':
            print("Installation cancelled.")
            return 1
    
    # Required packages
    packages = [
        "pyyaml",           # For configuration file parsing
        "langchain-openai", # For OpenAI/OpenRouter LLM integration
        "langchain",        # Core LangChain functionality
    ]
    
    # Optional packages for enhanced functionality
    optional_packages = [
        "langchain-anthropic",  # For direct Anthropic integration
        "langchain-community",  # Additional LangChain integrations
    ]
    
    print("📦 Installing required packages...")
    
    # Try to use uv first (faster), fall back to pip
    uv_available = False
    try:
        subprocess.run(["uv", "--version"], check=True, capture_output=True)
        uv_available = True
        print("✅ UV package manager detected")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("ℹ️ UV not available, using pip")
    
    # Install required packages
    success_count = 0
    total_packages = len(packages)
    
    for package in packages:
        if uv_available:
            command = f"uv add {package}"
        else:
            command = f"pip install {package}"
        
        if run_command(command, f"Installing {package}"):
            success_count += 1
    
    print(f"\n📊 Installation Summary:")
    print(f"   Required packages: {success_count}/{total_packages} installed")
    
    if success_count == total_packages:
        print("✅ All required packages installed successfully!")
        
        # Ask about optional packages
        print(f"\n🔧 Optional packages available:")
        for package in optional_packages:
            print(f"   - {package}")
        
        install_optional = input("\nInstall optional packages? (y/N): ").strip().lower()
        if install_optional == 'y':
            print("\n📦 Installing optional packages...")
            optional_success = 0
            
            for package in optional_packages:
                if uv_available:
                    command = f"uv add {package}"
                else:
                    command = f"pip install {package}"
                
                if run_command(command, f"Installing {package}"):
                    optional_success += 1
            
            print(f"   Optional packages: {optional_success}/{len(optional_packages)} installed")
        
        # Verify installation
        print(f"\n🧪 Verifying installation...")
        
        try:
            import yaml
            print("✅ PyYAML imported successfully")
        except ImportError:
            print("❌ PyYAML import failed")
        
        try:
            from langchain_openai import ChatOpenAI
            print("✅ LangChain OpenAI imported successfully")
        except ImportError:
            print("❌ LangChain OpenAI import failed")
        
        try:
            from langchain.schema import BaseMessage
            print("✅ LangChain core imported successfully")
        except ImportError:
            print("❌ LangChain core import failed")
        
        print(f"\n🎉 Installation completed!")
        print(f"📖 Next steps:")
        print(f"   1. Configure your API key: export OPENROUTER_API_KEY='your-key'")
        print(f"   2. Validate configuration: python tools/validate_config.py")
        print(f"   3. Run a test: python tools/simple_test.py")
        print(f"   4. See tools/OPENROUTER_CONFIG.md for detailed setup")
        
        return 0
    else:
        print("❌ Some required packages failed to install")
        print("   Please check the error messages above and try again")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
