"""
Prompt Builder Agent

Intelligent agent for building comprehensive AI development prompts.
"""

from typing import Any, Dict, List
from datetime import datetime
from pathlib import Path

from ..core.base_agent import BaseAgent, AgentResult, WorkflowContext


class PromptBuilderAgent(BaseAgent):
    """Agent for building comprehensive AI development prompts."""
    
    def __init__(self, llm=None, verbose: bool = False, stream_displayer=None):
        super().__init__(
            name="prompt_builder",
            llm=llm,
            verbose=verbose,
            stream_displayer=stream_displayer
        )

    def _load_and_compress_rules(self, project_root: str) -> str:
        """Load and compress project rules for inclusion in prompts."""
        try:
            rules_path = Path(project_root) / ".roo" / "rules" / "rules.md"
            if not rules_path.exists():
                return "项目规则文件未找到，请参考标准 DDD + FastAPI 架构原则。"

            with open(rules_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract key sections and compress
            lines = content.split('\n')
            compressed_sections = []
            current_section = ""
            in_important_section = False

            for line in lines:
                # Keep important sections
                if any(keyword in line.lower() for keyword in [
                    '## 2. 架构设计与项目结构', '### 2.1 项目整体结构',
                    '### 2.2 模块内部分层架构', '### 2.3 核心依赖规则',
                    '## 3. 各层详细实现规范', '### 3.1', '### 3.2', '### 3.3', '### 3.4',
                    '## 4. 代码生成约束与流程'
                ]):
                    in_important_section = True
                    if current_section:
                        compressed_sections.append(current_section.strip())
                    current_section = line + '\n'
                elif line.startswith('## ') and '测试' not in line and '工程实践' not in line:
                    in_important_section = False
                    if current_section:
                        compressed_sections.append(current_section.strip())
                    current_section = ""
                elif in_important_section:
                    # Skip code blocks and mermaid diagrams for compression
                    if not line.strip().startswith('```') and 'mermaid' not in line:
                        current_section += line + '\n'

            if current_section:
                compressed_sections.append(current_section.strip())

            return '\n\n'.join(compressed_sections)

        except Exception as e:
            self.logger.warning(f"Failed to load project rules: {e}")
            return "项目规则加载失败，请参考标准 DDD + FastAPI 架构原则。"
    
    def get_system_prompt(self, context: WorkflowContext) -> str:
        """Get the system prompt for prompt building."""
        # Load project rules
        project_rules = self._load_and_compress_rules(context.project_root)

        return f"""
你是一个AI提示词工程专家，专门构建高质量的中文代码生成提示词。你的任务是基于技术需求和项目上下文，创建包含完整信息的AI开发提示词。

重要：生成的所有内容必须使用中文，包括代码注释、文档和说明。

你需要构建包含以下部分的AI开发提示词：

1. **任务描述** (task_description)
   - 清晰的开发任务说明（中文）
   - 预期的交付成果
   - 成功标准

2. **项目上下文** (project_context)
   - 架构风格和技术栈
   - 现有模块和代码结构
   - 编码标准和约定

3. **架构约束** (architectural_constraints)
   - 必须严格遵循项目规则中的 DDD 四层架构
   - 模块结构：modules/{{module_name}}/{{interfaces|application|domain|infrastructure}}/
   - 依赖方向：interfaces → application → domain ← infrastructure
   - 文件命名：使用业务模块前缀，如 auth_api.py, user_service.py

4. **实现指南** (implementation_guidelines)
   - 开发顺序：Domain First 原则
   - 代码组织方式（按项目规则）
   - 最佳实践

5. **质量要求** (quality_requirements)
   - 代码质量标准
   - 测试覆盖率要求
   - 中文文档要求

6. **验收标准** (acceptance_criteria)
   - 功能验收标准
   - 技术验收标准
   - 质量验收标准

## 项目开发规范（必须严格遵循）

{project_rules}

请严格按照以下格式输出中文AI开发提示词：

```markdown
# AI 开发任务提示词

生成时间: {{timestamp}}

## 任务描述

### 开发任务
{{task_description}}

### 预期交付成果
{{deliverables}}

### 成功标准
{{success_criteria}}

## 项目上下文

### 架构风格
- 领域驱动设计 (DDD) 四层架构
- 整洁架构原则
- REST API with FastAPI

### 技术栈
{{tech_stack}}

### 编码标准
- 严格遵循 PEP 8 规范
- 强制类型提示 (Type Hinting)
- 中文注释和文档字符串
- 120 字符行长度限制
- Black 代码格式化

### 现有模块
{{existing_modules}}

## 架构约束 (必须严格遵循)

### DDD 四层架构
```
modules/{{{{module_name}}}}/
├── __init__.py
├── interfaces/          # 接口层 (FastAPI 路由、Pydantic 模式)
│   ├── {{{{module_name}}}}_api.py
│   └── schemas.py
├── application/         # 应用层 (应用服务、用例编排)
│   ├── services.py
│   └── dtos.py (可选)
├── domain/             # 领域层 (实体、值对象、仓库接口)
│   ├── models.py
│   └── repositories.py
└── infrastructure/     # 基础设施层 (仓库实现、ORM 模型)
    ├── repositories.py
    └── orm.py
```

### 依赖方向规则
- interfaces → application → domain ← infrastructure
- 严禁循环依赖
- Domain 层保持纯粹，不依赖任何外部框架

### 模块边界
- 模块间通信必须通过 Application 层服务接口
- 严禁直接访问其他模块的 Domain 或 Infrastructure 层
- 通用代码放置在 common/ 目录下

### 数据建模约束
- 所有实体 ID 字段使用 UUID 类型
- SQLAlchemy 2.0 风格模型
- Pydantic v2 用于数据验证

## 实现指南

### 开发步骤 (Domain First 原则)
1. **领域层设计** (modules/{{{{module_name}}}}/domain/)
   - 在 models.py 中创建领域实体和值对象
   - 在 repositories.py 中定义抽象仓库接口

2. **应用层实现** (modules/{{{{module_name}}}}/application/)
   - 在 services.py 中创建应用服务
   - 注入领域仓库接口，编排业务用例

3. **基础设施层实现** (modules/{{{{module_name}}}}/infrastructure/)
   - 在 orm.py 中创建 SQLAlchemy ORM 模型
   - 在 repositories.py 中实现仓库接口 (命名格式: {{{{Repository}}}}Impl)

4. **接口层实现** (modules/{{{{module_name}}}}/interfaces/)
   - 在 schemas.py 中创建 Pydantic 请求/响应模型
   - 在 {{{{module_name}}}}_api.py 中创建 FastAPI 路由

5. **集成到主应用**
   - 在 main.py 中注册路由
   - 配置依赖注入

### 代码组织原则
- 业务逻辑集中在 Domain 层
- API 相关代码放在 interfaces 层
- 数据库操作在 infrastructure 层
- 跨领域关注点在 application 层

## 质量要求

### 代码质量标准
- 单元测试覆盖率 > 80%
- 所有公共方法必须有类型提示
- 中文注释和文档字符串
- 通过 CI 代码质量检查 (inv ci)

### 测试要求
- Domain 层：纯单元测试，无外部依赖
- Application 层：Mock 仓库和外部依赖
- Infrastructure 层：集成测试，使用测试数据库
- interfaces 层：API 端到端测试

### 文档要求
- 所有 API 端点包含完整的 OpenAPI 文档
- 业务逻辑方法包含中文文档字符串
- README 文档更新

## 开发需求详情

{{detailed_requirements}}

```

构建要求：
- 所有生成的内容必须使用中文
- 架构约束必须严格遵循项目规则
- 实现指南要具体可操作
- 质量要求要量化可测量
- 代码示例要符合项目规范
"""
    
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Process technical requirements and build AI development prompt."""
        try:
            # Get technical requirements
            requirements = input_data
            if not requirements:
                return AgentResult(
                    success=False,
                    data={},
                    metadata={"agent_name": self.name},
                    errors=["No technical requirements provided"],
                    execution_time=0.0,
                    timestamp=datetime.now()
                )
            
            # Get previous results from context (if available in additional_context)
            business_analysis = context.additional_context.get("business_analysis", {})
            domain_model = context.additional_context.get("domain_model", {})
            
            # Prepare formatted content
            formatted_requirements = self._format_requirements(requirements)
            formatted_business = self._format_business_context(business_analysis)
            formatted_domain = self._format_domain_context(domain_model)
            tech_stack_str = ', '.join(context.tech_stack)
            existing_modules_str = ', '.join(context.existing_modules)

            # Prepare input for LLM
            user_input = f"""
请基于以下技术需求和项目上下文，构建完整的AI开发提示词：

=== 技术需求 ===
{formatted_requirements}

=== 项目上下文 ===
- 项目根目录: {context.project_root}
- 架构风格: {context.architecture_style}
- 技术栈: {tech_stack_str}
- 现有模块: {existing_modules_str}

=== 业务上下文 ===
{formatted_business}

=== 领域模型 ===
{formatted_domain}

=== 项目规则 ===
{context.project_rules}

=== 构建要求 ===
1. 提示词必须包含完整的开发上下文
2. 架构约束要明确且强制执行
3. 实现指南要详细可操作
4. 质量要求要具体可测量
5. 验收标准要清晰明确
6. 包含现有模块的参考模式
7. 所有内容使用中文编写

请按照指定的Markdown格式输出完整的AI开发提示词。
"""
            
            # Execute LLM call
            system_prompt = self.get_system_prompt(context)
            messages = self._create_messages(system_prompt, user_input)
            response = self._execute_llm_call(messages)
            
            # Process the response as markdown content
            prompt_content = self._process_prompt_content(response, requirements, context)
            
            return AgentResult(
                success=True,
                data={
                    "prompt_content": prompt_content,
                    "module_name": requirements.get("module_overview", {}).get("module_name", "unknown"),
                    "prompt_sections": self._extract_prompt_sections(prompt_content),
                    "word_count": len(prompt_content.split()),
                    "estimated_tokens": len(prompt_content) // 4  # Rough estimate
                },
                metadata={
                    "agent_name": self.name,
                    "raw_response": response,
                    "requirements_complexity": self._assess_requirements_complexity(requirements),
                    "context_richness": self._assess_context_richness(context)
                },
                errors=[],
                execution_time=0.0,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                data={},
                metadata={"agent_name": self.name},
                errors=[f"Prompt building failed: {str(e)}"],
                execution_time=0.0,
                timestamp=datetime.now()
            )
    
    def _format_requirements(self, requirements: Dict[str, Any]) -> str:
        """Format technical requirements for LLM input."""
        formatted = []
        
        # Module overview
        if "module_overview" in requirements:
            overview = requirements["module_overview"]
            formatted.append("## 模块概览")
            formatted.append(f"模块名: {overview.get('module_name', 'Unknown')}")
            formatted.append(f"职责: {overview.get('primary_responsibility', 'Unknown')}")
            formatted.append(f"业务价值: {overview.get('business_value', 'Unknown')}")
            formatted.append("")
        
        # User stories
        if "user_stories" in requirements:
            formatted.append("## 用户故事")
            for story in requirements["user_stories"][:5]:  # Limit to first 5
                formatted.append(f"- {story.get('id', 'Unknown')}: {story.get('title', 'Unknown')}")
                formatted.append(f"  {story.get('story', '')}")
            formatted.append("")
        
        # API design
        if "api_design" in requirements:
            api_design = requirements["api_design"]
            formatted.append("## API设计")
            formatted.append(f"基础路径: {api_design.get('base_path', '/api/v1')}")
            endpoints = api_design.get("endpoints", [])
            formatted.append(f"端点数量: {len(endpoints)}")
            for endpoint in endpoints[:3]:  # Limit to first 3
                formatted.append(f"- {endpoint.get('method', 'GET')} {endpoint.get('path', 'unknown')}")
            formatted.append("")
        
        # Data models
        if "data_models" in requirements:
            tables = requirements["data_models"].get("tables", [])
            formatted.append(f"## 数据模型")
            formatted.append(f"数据表数量: {len(tables)}")
            for table in tables[:3]:  # Limit to first 3
                formatted.append(f"- {table.get('name', 'unknown')}: {table.get('description', '')}")
            formatted.append("")
        
        return "\n".join(formatted)
    
    def _format_business_context(self, analysis: Dict[str, Any]) -> str:
        """Format business analysis for context."""
        if not analysis:
            return "无业务分析数据"
        
        formatted = []
        
        if "business_overview" in analysis:
            overview = analysis["business_overview"]
            formatted.append(f"项目: {overview.get('project_name', 'Unknown')}")
            formatted.append(f"目标: {overview.get('core_purpose', 'Unknown')}")
        
        if "core_entities" in analysis:
            entities = analysis["core_entities"]
            formatted.append(f"核心实体: {', '.join([e.get('name', 'unknown') for e in entities[:5]])}")
        
        return "\n".join(formatted)
    
    def _format_domain_context(self, model: Dict[str, Any]) -> str:
        """Format domain model for context."""
        if not model:
            return "无领域模型数据"
        
        formatted = []
        
        if "bounded_contexts" in model:
            contexts = model["bounded_contexts"]
            formatted.append(f"边界上下文: {', '.join([c.get('name', 'unknown') for c in contexts])}")
        
        if "aggregates" in model:
            aggregates = model["aggregates"]
            formatted.append(f"聚合: {', '.join([a.get('name', 'unknown') for a in aggregates])}")
        
        return "\n".join(formatted)
    
    def _process_prompt_content(self, response: str, requirements: Dict[str, Any], context: WorkflowContext) -> str:
        """Process and enhance the prompt content."""
        # Extract markdown content from response
        content = response
        
        # Add timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        content = content.replace("{timestamp}", timestamp)
        
        # Add module-specific information
        module_name = requirements.get("module_overview", {}).get("module_name", "unknown")
        content = content.replace("{module_name}", module_name)
        
        # Add project context
        content = content.replace("{architecture_style}", context.architecture_style)
        content = content.replace("{tech_stack}", ", ".join(context.tech_stack))
        content = content.replace("{existing_modules}", ", ".join(context.existing_modules))
        
        return content
    
    def _extract_prompt_sections(self, content: str) -> List[str]:
        """Extract section names from prompt content."""
        sections = []
        lines = content.split('\n')
        
        for line in lines:
            if line.startswith('## '):
                section_name = line[3:].strip()
                sections.append(section_name)
        
        return sections
    
    def _assess_requirements_complexity(self, requirements: Dict[str, Any]) -> str:
        """Assess the complexity of requirements."""
        complexity_score = 0
        
        # Count various elements
        user_stories = len(requirements.get("user_stories", []))
        api_endpoints = len(requirements.get("api_design", {}).get("endpoints", []))
        data_tables = len(requirements.get("data_models", {}).get("tables", []))
        domain_entities = len(requirements.get("business_logic", {}).get("domain_entities", []))
        
        complexity_score = user_stories + api_endpoints * 2 + data_tables * 2 + domain_entities * 3
        
        if complexity_score < 20:
            return "Low"
        elif complexity_score < 50:
            return "Medium"
        else:
            return "High"
    
    def _assess_context_richness(self, context: WorkflowContext) -> str:
        """Assess the richness of project context."""
        richness_score = 0
        
        # Check context completeness
        if context.project_rules:
            richness_score += 3
        if context.existing_modules:
            richness_score += len(context.existing_modules)
        if context.tech_stack:
            richness_score += len(context.tech_stack)
        if context.additional_context:
            richness_score += len(context.additional_context) * 2
        
        if richness_score < 10:
            return "Basic"
        elif richness_score < 20:
            return "Good"
        else:
            return "Rich"

    def _create_messages(self, system_prompt: str, user_prompt: str) -> List[Dict[str, str]]:
        """Create messages for LLM API call."""
        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    def _execute_llm_call(self, messages: List[Dict[str, str]]) -> str:
        """Execute LLM call with streaming support."""
        return self._execute_llm_call_with_streaming(messages, "AI开发提示词生成")

    def _get_mock_response(self) -> str:
        """Get mock response for testing."""
        return """
# AI Development Prompt for MCP Server Market Platform

## Project Context
This is a FastAPI-based marketplace platform for Model Context Protocol (MCP) servers, following Domain-Driven Design (DDD) principles.

## Technical Requirements
- Use FastAPI for web framework
- Implement DDD architecture with clear layer separation
- Use SQLAlchemy for ORM
- Include comprehensive type hints
- Follow PEP 8 coding standards

## Implementation Tasks
1. Create domain entities and value objects
2. Implement repository interfaces and implementations
3. Build application services
4. Create API endpoints with proper validation
5. Add comprehensive tests

Please implement the specified module following these guidelines.
"""
