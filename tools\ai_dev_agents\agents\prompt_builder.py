"""
Prompt Builder Agent

Intelligent agent for building comprehensive AI development prompts.
"""

from typing import Any, Dict, List
from datetime import datetime

from ..core.base_agent import BaseAgent, AgentResult, WorkflowContext


class PromptBuilderAgent(BaseAgent):
    """Agent for building comprehensive AI development prompts."""
    
    def __init__(self, llm=None, verbose: bool = False):
        super().__init__("prompt_builder", llm, verbose)
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for prompt building."""
        return """
你是一个AI提示词工程专家，专门构建高质量的代码生成提示词。你的任务是基于技术需求和项目上下文，创建包含完整信息的AI开发提示词。

你需要构建包含以下部分的AI开发提示词：

1. **任务描述** (task_description)
   - 清晰的开发任务说明
   - 预期的交付成果
   - 成功标准

2. **项目上下文** (project_context)
   - 架构风格和技术栈
   - 现有模块和代码结构
   - 编码标准和约定

3. **架构约束** (architectural_constraints)
   - DDD架构原则
   - 依赖方向规则
   - 模块边界要求

4. **实现指南** (implementation_guidelines)
   - 开发顺序和步骤
   - 代码组织方式
   - 最佳实践

5. **质量要求** (quality_requirements)
   - 代码质量标准
   - 测试覆盖率要求
   - 文档要求

6. **验收标准** (acceptance_criteria)
   - 功能验收标准
   - 技术验收标准
   - 质量验收标准

7. **参考示例** (reference_examples)
   - 现有代码模式
   - 架构参考
   - 最佳实践示例

请严格按照以下格式输出AI开发提示词：

```markdown
# AI Development Task Prompt

Generated on: {timestamp}

## Task Description

{task_description}

## Project Context

### Architecture Style
{architecture_style}

### Technology Stack
{tech_stack}

### Coding Standards
{coding_standards}

### Existing Modules
{existing_modules}

## Architectural Constraints (MUST FOLLOW)

{architectural_constraints}

## Implementation Guidelines

{implementation_guidelines}

## Quality Requirements

{quality_requirements}

## Acceptance Criteria

{acceptance_criteria}

## Reference Examples

{reference_examples}

## Development Requirements

{detailed_requirements}

## Project Rules and Standards

{project_rules}
```

构建要求：
- 提示词必须包含完整的项目上下文
- 架构约束必须明确且不可违背
- 实现指南要具体可操作
- 质量要求要量化可测量
- 验收标准要清晰明确
- 参考示例要具体有用
"""
    
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Process technical requirements and build AI development prompt."""
        try:
            # Get technical requirements
            requirements = input_data
            if not requirements:
                return AgentResult(
                    success=False,
                    data={},
                    metadata={"agent_name": self.name},
                    errors=["No technical requirements provided"],
                    execution_time=0.0,
                    timestamp=datetime.now()
                )
            
            # Get previous results from context
            business_analysis = context.get_result("business_analyzer") or {}
            domain_model = context.get_result("domain_modeler") or {}
            
            # Prepare input for LLM
            user_input = f"""
请基于以下技术需求和项目上下文，构建完整的AI开发提示词：

=== 技术需求 ===
{self._format_requirements(requirements)}

=== 项目上下文 ===
- 项目根目录: {context.project_root}
- 架构风格: {context.architecture_style}
- 技术栈: {', '.join(context.tech_stack)}
- 现有模块: {', '.join(context.existing_modules)}

=== 业务上下文 ===
{self._format_business_context(business_analysis)}

=== 领域模型 ===
{self._format_domain_context(domain_model)}

=== 项目规则 ===
{context.project_rules}

=== 构建要求 ===
1. 提示词必须包含完整的开发上下文
2. 架构约束要明确且强制执行
3. 实现指南要详细可操作
4. 质量要求要具体可测量
5. 验收标准要清晰明确
6. 包含现有模块的参考模式
7. 所有内容使用英文编写

请按照指定的Markdown格式输出完整的AI开发提示词。
"""
            
            # Execute LLM call
            system_prompt = self.get_system_prompt()
            messages = self._create_messages(system_prompt, user_input)
            response = self._execute_llm_call(messages)
            
            # Process the response as markdown content
            prompt_content = self._process_prompt_content(response, requirements, context)
            
            return AgentResult(
                success=True,
                data={
                    "prompt_content": prompt_content,
                    "module_name": requirements.get("module_overview", {}).get("module_name", "unknown"),
                    "prompt_sections": self._extract_prompt_sections(prompt_content),
                    "word_count": len(prompt_content.split()),
                    "estimated_tokens": len(prompt_content) // 4  # Rough estimate
                },
                metadata={
                    "agent_name": self.name,
                    "raw_response": response,
                    "requirements_complexity": self._assess_requirements_complexity(requirements),
                    "context_richness": self._assess_context_richness(context)
                },
                errors=[],
                execution_time=0.0,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                data={},
                metadata={"agent_name": self.name},
                errors=[f"Prompt building failed: {str(e)}"],
                execution_time=0.0,
                timestamp=datetime.now()
            )
    
    def _format_requirements(self, requirements: Dict[str, Any]) -> str:
        """Format technical requirements for LLM input."""
        formatted = []
        
        # Module overview
        if "module_overview" in requirements:
            overview = requirements["module_overview"]
            formatted.append("## 模块概览")
            formatted.append(f"模块名: {overview.get('module_name', 'Unknown')}")
            formatted.append(f"职责: {overview.get('primary_responsibility', 'Unknown')}")
            formatted.append(f"业务价值: {overview.get('business_value', 'Unknown')}")
            formatted.append("")
        
        # User stories
        if "user_stories" in requirements:
            formatted.append("## 用户故事")
            for story in requirements["user_stories"][:5]:  # Limit to first 5
                formatted.append(f"- {story.get('id', 'Unknown')}: {story.get('title', 'Unknown')}")
                formatted.append(f"  {story.get('story', '')}")
            formatted.append("")
        
        # API design
        if "api_design" in requirements:
            api_design = requirements["api_design"]
            formatted.append("## API设计")
            formatted.append(f"基础路径: {api_design.get('base_path', '/api/v1')}")
            endpoints = api_design.get("endpoints", [])
            formatted.append(f"端点数量: {len(endpoints)}")
            for endpoint in endpoints[:3]:  # Limit to first 3
                formatted.append(f"- {endpoint.get('method', 'GET')} {endpoint.get('path', 'unknown')}")
            formatted.append("")
        
        # Data models
        if "data_models" in requirements:
            tables = requirements["data_models"].get("tables", [])
            formatted.append(f"## 数据模型")
            formatted.append(f"数据表数量: {len(tables)}")
            for table in tables[:3]:  # Limit to first 3
                formatted.append(f"- {table.get('name', 'unknown')}: {table.get('description', '')}")
            formatted.append("")
        
        return "\n".join(formatted)
    
    def _format_business_context(self, analysis: Dict[str, Any]) -> str:
        """Format business analysis for context."""
        if not analysis:
            return "无业务分析数据"
        
        formatted = []
        
        if "business_overview" in analysis:
            overview = analysis["business_overview"]
            formatted.append(f"项目: {overview.get('project_name', 'Unknown')}")
            formatted.append(f"目标: {overview.get('core_purpose', 'Unknown')}")
        
        if "core_entities" in analysis:
            entities = analysis["core_entities"]
            formatted.append(f"核心实体: {', '.join([e.get('name', 'unknown') for e in entities[:5]])}")
        
        return "\n".join(formatted)
    
    def _format_domain_context(self, model: Dict[str, Any]) -> str:
        """Format domain model for context."""
        if not model:
            return "无领域模型数据"
        
        formatted = []
        
        if "bounded_contexts" in model:
            contexts = model["bounded_contexts"]
            formatted.append(f"边界上下文: {', '.join([c.get('name', 'unknown') for c in contexts])}")
        
        if "aggregates" in model:
            aggregates = model["aggregates"]
            formatted.append(f"聚合: {', '.join([a.get('name', 'unknown') for a in aggregates])}")
        
        return "\n".join(formatted)
    
    def _process_prompt_content(self, response: str, requirements: Dict[str, Any], context: WorkflowContext) -> str:
        """Process and enhance the prompt content."""
        # Extract markdown content from response
        content = response
        
        # Add timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        content = content.replace("{timestamp}", timestamp)
        
        # Add module-specific information
        module_name = requirements.get("module_overview", {}).get("module_name", "unknown")
        content = content.replace("{module_name}", module_name)
        
        # Add project context
        content = content.replace("{architecture_style}", context.architecture_style)
        content = content.replace("{tech_stack}", ", ".join(context.tech_stack))
        content = content.replace("{existing_modules}", ", ".join(context.existing_modules))
        
        return content
    
    def _extract_prompt_sections(self, content: str) -> List[str]:
        """Extract section names from prompt content."""
        sections = []
        lines = content.split('\n')
        
        for line in lines:
            if line.startswith('## '):
                section_name = line[3:].strip()
                sections.append(section_name)
        
        return sections
    
    def _assess_requirements_complexity(self, requirements: Dict[str, Any]) -> str:
        """Assess the complexity of requirements."""
        complexity_score = 0
        
        # Count various elements
        user_stories = len(requirements.get("user_stories", []))
        api_endpoints = len(requirements.get("api_design", {}).get("endpoints", []))
        data_tables = len(requirements.get("data_models", {}).get("tables", []))
        domain_entities = len(requirements.get("business_logic", {}).get("domain_entities", []))
        
        complexity_score = user_stories + api_endpoints * 2 + data_tables * 2 + domain_entities * 3
        
        if complexity_score < 20:
            return "Low"
        elif complexity_score < 50:
            return "Medium"
        else:
            return "High"
    
    def _assess_context_richness(self, context: WorkflowContext) -> str:
        """Assess the richness of project context."""
        richness_score = 0
        
        # Check context completeness
        if context.project_rules:
            richness_score += 3
        if context.existing_modules:
            richness_score += len(context.existing_modules)
        if context.tech_stack:
            richness_score += len(context.tech_stack)
        if context.intermediate_results:
            richness_score += len(context.intermediate_results) * 2
        
        if richness_score < 10:
            return "Basic"
        elif richness_score < 20:
            return "Good"
        else:
            return "Rich"

    def _create_messages(self, system_prompt: str, user_prompt: str) -> List[Dict[str, str]]:
        """Create messages for LLM API call."""
        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    def _execute_llm_call(self, messages: List[Dict[str, str]]) -> str:
        """Execute LLM call with retry logic."""
        def llm_operation():
            # For now, return mock response for testing
            return AgentResult(success=True, data=self._get_mock_response())

        result = self._execute_with_retry(llm_operation, "LLM call")
        if result.success:
            return result.data
        else:
            raise Exception(f"LLM call failed: {', '.join(result.errors)}")

    def _get_mock_response(self) -> str:
        """Get mock response for testing."""
        return """
# AI Development Prompt for MCP Server Market Platform

## Project Context
This is a FastAPI-based marketplace platform for Model Context Protocol (MCP) servers, following Domain-Driven Design (DDD) principles.

## Technical Requirements
- Use FastAPI for web framework
- Implement DDD architecture with clear layer separation
- Use SQLAlchemy for ORM
- Include comprehensive type hints
- Follow PEP 8 coding standards

## Implementation Tasks
1. Create domain entities and value objects
2. Implement repository interfaces and implementations
3. Build application services
4. Create API endpoints with proper validation
5. Add comprehensive tests

Please implement the specified module following these guidelines.
"""
