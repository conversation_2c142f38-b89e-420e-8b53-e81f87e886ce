#!/usr/bin/env python3
"""
Test script for streaming functionality.
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from ai_dev_agents.core.orchestrator import AIDevWorkflowOrchestrator


def test_streaming():
    """Test the streaming functionality with a simple PRD."""
    
    # Simple test PRD content
    test_prd = """
# MCP 服务器市场平台 - 产品需求文档

## 1. 产品概述
MCP 服务器市场平台是一个专门为 MCP (Model Context Protocol) 服务器提供发布、发现和管理的在线平台。

## 2. 核心功能
### 2.1 服务器发布
- 开发者可以发布自己的 MCP 服务器
- 支持版本管理
- 提供详细的服务器描述和文档

### 2.2 服务器发现
- 用户可以浏览和搜索 MCP 服务器
- 支持分类和标签筛选
- 提供评分和评论功能

### 2.3 用户管理
- 用户注册和登录
- 开发者认证
- 用户权限管理
"""
    
    print("🚀 开始测试流式输出功能...")
    print("=" * 60)
    
    try:
        # Initialize orchestrator
        orchestrator = AIDevWorkflowOrchestrator(verbose=True)
        
        # Create context
        project_root = str(Path(__file__).parent.parent.parent)
        context = orchestrator.create_context(project_root)
        
        print(f"📁 项目根目录: {project_root}")
        print(f"🏗️ 架构风格: {context.architecture_style}")
        print(f"🔧 技术栈: {', '.join(context.tech_stack)}")
        print(f"📦 现有模块: {', '.join(context.existing_modules)}")
        print()
        
        # Test complete workflow with streaming
        print("🔍 测试完整工作流程流式输出...")
        print("-" * 40)

        try:
            # Execute full workflow
            results = orchestrator.execute_full_workflow(
                prd_content=test_prd,
                project_root=project_root
            )

            print("\n📋 工作流程执行结果:")
            if results.get("success", False):
                print("  ✅ 完整工作流程: 成功")
                print(f"  📊 业务分析: 完成")
                print(f"  🏗️ 领域建模: 完成")
                print(f"  📋 模块数量: {len(results.get('modules', {}))}")
                print(f"  📁 输出目录: {results.get('output_directory', 'N/A')}")
            else:
                print("  ❌ 完整工作流程: 失败")

        except Exception as e:
            print(f"❌ 工作流程执行失败: {e}")

        print("\n" + "=" * 60)
        print("🎉 流式输出测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_streaming()
