"""
PRD (Product Requirements Document) Analyzer

This module provides tools to analyze PRD documents and extract structured business information.
"""

import re
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Any
import json


@dataclass
class BusinessEntity:
    """Represents a business entity identified in the PRD."""
    name: str
    description: str
    attributes: List[str]
    relationships: List[str]


@dataclass
class UserStory:
    """Represents a user story extracted from the PRD."""
    id: str
    title: str
    description: str
    acceptance_criteria: List[str]
    priority: str


@dataclass
class PRDAnalysisResult:
    """Contains the complete analysis result of a PRD document."""
    project_overview: str
    target_users: List[str]
    core_features: List[str]
    business_entities: List[BusinessEntity]
    user_stories: List[UserStory]
    functional_requirements: List[str]
    non_functional_requirements: List[str]
    technical_constraints: List[str]


class PRDAnalyzer:
    """Analyzes PRD documents and extracts structured business information."""
    
    def __init__(self):
        self.content = ""
        self.sections = {}
    
    def load_prd(self, file_path: str) -> None:
        """Load PRD document from file."""
        path = Path(file_path)
        if not path.exists():
            raise FileNotFoundError(f"PRD file not found: {file_path}")
        
        with open(path, 'r', encoding='utf-8') as f:
            self.content = f.read()
        
        self._parse_sections()
    
    def _parse_sections(self) -> None:
        """Parse the PRD content into sections."""
        # Split content by markdown headers
        sections = {}
        current_section = ""
        current_content = []
        
        lines = self.content.split('\n')
        for line in lines:
            if line.startswith('#'):
                if current_section:
                    sections[current_section] = '\n'.join(current_content)
                current_section = line.strip('#').strip()
                current_content = []
            else:
                current_content.append(line)
        
        if current_section:
            sections[current_section] = '\n'.join(current_content)
        
        self.sections = sections
    
    def analyze(self) -> PRDAnalysisResult:
        """Perform complete analysis of the PRD document."""
        return PRDAnalysisResult(
            project_overview=self._extract_project_overview(),
            target_users=self._extract_target_users(),
            core_features=self._extract_core_features(),
            business_entities=self._extract_business_entities(),
            user_stories=self._extract_user_stories(),
            functional_requirements=self._extract_functional_requirements(),
            non_functional_requirements=self._extract_non_functional_requirements(),
            technical_constraints=self._extract_technical_constraints()
        )
    
    def _extract_project_overview(self) -> str:
        """Extract project overview from PRD."""
        overview_sections = ['执行摘要', '项目概述', '概述', 'Executive Summary', 'Overview']
        for section_name in overview_sections:
            if section_name in self.sections:
                return self.sections[section_name].strip()
        return "No project overview found"
    
    def _extract_target_users(self) -> List[str]:
        """Extract target users from PRD."""
        users = []
        content = self.content.lower()
        
        # Look for user role patterns
        user_patterns = [
            r'作为.*?用户',
            r'作为.*?开发者',
            r'作为.*?管理员',
            r'as a.*?user',
            r'as a.*?developer',
            r'as an.*?admin'
        ]
        
        for pattern in user_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            users.extend(matches)
        
        return list(set(users))
    
    def _extract_core_features(self) -> List[str]:
        """Extract core features from PRD."""
        features = []
        
        # Look for feature sections
        feature_sections = ['功能需求', '核心功能', '主要功能', 'Features', 'Core Features']
        for section_name in feature_sections:
            if section_name in self.sections:
                content = self.sections[section_name]
                # Extract bullet points and numbered lists
                feature_lines = re.findall(r'[*\-•]\s*(.+)', content)
                numbered_lines = re.findall(r'\d+\.\s*(.+)', content)
                features.extend(feature_lines + numbered_lines)
        
        return [f.strip() for f in features if f.strip()]
    
    def _extract_business_entities(self) -> List[BusinessEntity]:
        """Extract business entities from PRD."""
        entities = []
        
        # Look for entity definitions in DDD sections
        ddd_sections = ['领域驱动设计', 'DDD', '核心实体模型', 'Domain Model']
        for section_name in ddd_sections:
            if section_name in self.sections:
                content = self.sections[section_name]
                # Extract entity patterns
                entity_patterns = re.findall(r'(\w+)\s*[:：]\s*(.+)', content)
                for name, description in entity_patterns:
                    if any(keyword in name.lower() for keyword in ['entity', '实体', 'aggregate', '聚合']):
                        entities.append(BusinessEntity(
                            name=name.strip(),
                            description=description.strip(),
                            attributes=[],
                            relationships=[]
                        ))
        
        return entities
    
    def _extract_user_stories(self) -> List[UserStory]:
        """Extract user stories from PRD."""
        stories = []
        
        # Look for user story sections
        story_sections = ['用户故事', 'User Stories', '敏捷用户故事']
        for section_name in story_sections:
            if section_name in self.sections:
                content = self.sections[section_name]
                # Extract user story patterns
                story_patterns = re.findall(
                    r'(US-\d+)[:：]\s*(.+?)\n.*?作为.*?我想要.*?以便.*?',
                    content,
                    re.DOTALL
                )
                for story_id, title in story_patterns:
                    stories.append(UserStory(
                        id=story_id.strip(),
                        title=title.strip(),
                        description="",
                        acceptance_criteria=[],
                        priority="Medium"
                    ))
        
        return stories
    
    def _extract_functional_requirements(self) -> List[str]:
        """Extract functional requirements from PRD."""
        requirements = []
        
        functional_sections = ['功能需求', 'Functional Requirements', '业务需求']
        for section_name in functional_sections:
            if section_name in self.sections:
                content = self.sections[section_name]
                req_lines = re.findall(r'[*\-•]\s*(.+)', content)
                requirements.extend(req_lines)
        
        return [req.strip() for req in requirements if req.strip()]
    
    def _extract_non_functional_requirements(self) -> List[str]:
        """Extract non-functional requirements from PRD."""
        requirements = []
        
        nfr_sections = ['非功能需求', 'Non-Functional Requirements', '性能需求', '安全需求']
        for section_name in nfr_sections:
            if section_name in self.sections:
                content = self.sections[section_name]
                req_lines = re.findall(r'[*\-•]\s*(.+)', content)
                requirements.extend(req_lines)
        
        return [req.strip() for req in requirements if req.strip()]
    
    def _extract_technical_constraints(self) -> List[str]:
        """Extract technical constraints from PRD."""
        constraints = []
        
        tech_sections = ['技术约束', 'Technical Constraints', '技术要求', '架构要求']
        for section_name in tech_sections:
            if section_name in self.sections:
                content = self.sections[section_name]
                constraint_lines = re.findall(r'[*\-•]\s*(.+)', content)
                constraints.extend(constraint_lines)
        
        return [c.strip() for c in constraints if c.strip()]
    
    def export_analysis(self, result: PRDAnalysisResult, output_path: str) -> None:
        """Export analysis result to JSON file."""
        output_data = {
            "project_overview": result.project_overview,
            "target_users": result.target_users,
            "core_features": result.core_features,
            "business_entities": [
                {
                    "name": entity.name,
                    "description": entity.description,
                    "attributes": entity.attributes,
                    "relationships": entity.relationships
                }
                for entity in result.business_entities
            ],
            "user_stories": [
                {
                    "id": story.id,
                    "title": story.title,
                    "description": story.description,
                    "acceptance_criteria": story.acceptance_criteria,
                    "priority": story.priority
                }
                for story in result.user_stories
            ],
            "functional_requirements": result.functional_requirements,
            "non_functional_requirements": result.non_functional_requirements,
            "technical_constraints": result.technical_constraints
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)


def main():
    """Main function for command line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Analyze PRD document')
    parser.add_argument('prd_file', help='Path to PRD document')
    parser.add_argument('-o', '--output', help='Output JSON file path', 
                       default='prd_analysis.json')
    
    args = parser.parse_args()
    
    analyzer = PRDAnalyzer()
    analyzer.load_prd(args.prd_file)
    result = analyzer.analyze()
    analyzer.export_analysis(result, args.output)
    
    print(f"PRD analysis completed. Results saved to {args.output}")


if __name__ == "__main__":
    main()
