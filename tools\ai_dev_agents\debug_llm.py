#!/usr/bin/env python3
"""
Debug LLM connection and API calls.
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from ai_dev_agents.utils.config_manager import ConfigManager

def test_llm_connection():
    """Test basic LLM connection and API call."""
    print("🔍 Testing LLM Connection...")
    
    try:
        # Initialize config and LLM
        config_manager = ConfigManager("config.yaml")
        llm = config_manager.create_llm()
        
        if llm is None:
            print("❌ Failed to create LLM. Please check your configuration.")
            return False
        
        print(f"✅ LLM created successfully: {llm}")
        
        # Test simple API call
        from langchain_core.messages import HumanMessage
        
        print("📞 Testing simple API call...")
        test_message = HumanMessage(content="Hello! Please respond with 'Hello World' in JSON format: {\"message\": \"Hello World\"}")
        
        try:
            response = llm.invoke([test_message])
            print(f"✅ LLM Response: {response.content}")
            return True
        except Exception as e:
            print(f"❌ LLM API call failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config():
    """Test configuration loading."""
    print("🔧 Testing Configuration...")
    
    try:
        config_manager = ConfigManager("config.yaml")
        llm_config = config_manager.get_llm_config()
        
        print(f"Provider: {llm_config.provider}")
        print(f"Model: {llm_config.model}")
        print(f"API Key: {llm_config.api_key[:10]}..." if llm_config.api_key else "No API Key")
        print(f"Base URL: {llm_config.base_url}")
        
        return True
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting LLM Debug Tests\n")
    
    # Test configuration
    config_success = test_config()
    print()
    
    # Test LLM connection
    llm_success = test_llm_connection()
    print()
    
    print("=" * 50)
    print("DEBUG SUMMARY")
    print("=" * 50)
    print(f"Configuration: {'✅ PASSED' if config_success else '❌ FAILED'}")
    print(f"LLM Connection: {'✅ PASSED' if llm_success else '❌ FAILED'}")
    
    if config_success and llm_success:
        print("\n🎉 All tests passed! LLM is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Please check the configuration and network connection.")
