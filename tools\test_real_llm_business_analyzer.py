#!/usr/bin/env python3
"""
Test BusinessAnalyzerAgent with real LLM
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_dev_agents.business_analyzer import BusinessAnalyzerAgent
from ai_dev_agents.base_agent import WorkflowContext
from ai_dev_agents.config_manager import Config<PERSON>anager

def test_real_llm_business_analyzer():
    """Test the business analyzer with real LLM."""
    print("=== Testing Business Analyzer (Real LLM) ===")
    
    try:
        # Initialize config and agent
        config_manager = ConfigManager("config.yaml")
        
        # Create LLM instance directly
        llm = config_manager.create_llm()
        
        # Initialize enhanced agent
        agent = BusinessAnalyzerAgent(llm=llm, verbose=True, quality_threshold=0.6)
        
        # Create workflow context
        workflow_context = WorkflowContext(
            project_root=".",
            project_rules={},
            existing_modules=["auth", "user"],
            tech_stack=["FastAPI", "SQLAlchemy"],
            architecture_style="DDD"
        )
        
        # Test PRD content
        test_prd = """
# MCP Server Market Platform

## 项目概述
MCP Server Market Platform 是一个集中化的 Model Context Protocol 服务器市场平台，旨在为开发者提供发现、分享和管理 MCP 服务器的统一平台。

## 目标用户
- 开发者：寻找和使用 MCP 服务器
- 服务器作者：发布和管理自己的 MCP 服务器
- 平台管理员：维护平台运营

## 核心功能

### 1. 服务器发现
用户可以浏览和搜索可用的 MCP 服务器，支持按类别、标签、评分等多维度筛选。

### 2. 服务器提交
认证用户可以提交自己开发的 MCP 服务器，包括元数据、文档、版本管理等。

### 3. 用户管理
支持用户注册、登录、个人资料管理、权限控制等。

### 4. 评价系统
用户可以对使用过的服务器进行评价和评分，帮助其他用户选择。

## 业务规则
- 只有认证用户才能提交服务器
- 服务器必须通过基本验证才能发布
- 用户只能修改自己提交的服务器
- 管理员可以审核和管理所有内容

## 技术要求
- 支持高并发访问（1000+ 用户）
- API 响应时间 < 200ms
- 数据安全和隐私保护
- 支持多种认证方式
"""
        
        # Test basic analysis
        print("\n--- 真实 LLM 分析测试 ---")
        input_data = {"prd_content": test_prd}
        
        result = agent.process(input_data, workflow_context)
        
        if result.success:
            print("✓ 分析成功")
            print(f"  执行时间: {result.execution_time:.2f}s")
            
            # Print metadata
            metadata = result.metadata
            print(f"  分析上下文: {metadata.get('analysis_context', {})}")
            print(f"  质量指标: {metadata.get('quality_metrics', {})}")
            print(f"  文档分析: {metadata.get('document_analysis', {})}")
            print(f"  分析轮次: {metadata.get('analysis_passes', 1)}")
            
            # Print data summary
            data = result.data
            print(f"  业务实体数量: {len(data.get('core_entities', []))}")
            print(f"  功能需求数量: {len(data.get('functional_requirements', []))}")
            print(f"  用户故事数量: {len(data.get('user_stories', []))}")
            print(f"  业务规则数量: {len(data.get('business_rules', []))}")
            
            # Print sample entities
            entities = data.get('core_entities', [])
            if entities:
                print(f"\n  示例业务实体:")
                for i, entity in enumerate(entities[:3]):
                    print(f"    {i+1}. {entity.get('name', 'N/A')} - {entity.get('description', 'N/A')[:50]}...")
            
            # Print sample requirements
            requirements = data.get('functional_requirements', [])
            if requirements:
                print(f"\n  示例功能需求:")
                for i, req in enumerate(requirements[:3]):
                    print(f"    {i+1}. {req.get('title', 'N/A')} - {req.get('description', 'N/A')[:50]}...")
            
            # Print insights if available
            insights = data.get('analysis_insights', {})
            if insights:
                print(f"\n  分析洞察:")
                print(f"    领域复杂度: {insights.get('domain_complexity', 'N/A')}")
                challenges = insights.get('implementation_challenges', [])
                if challenges:
                    print(f"    实现挑战: {len(challenges)} 项")
                    for challenge in challenges[:2]:
                        print(f"      - {challenge}")
                
                risks = insights.get('business_risks', [])
                if risks:
                    print(f"    业务风险: {len(risks)} 项")
                    for risk in risks[:2]:
                        print(f"      - {risk}")
            
            # Print recommendations
            recommendations = data.get('recommendations', [])
            if recommendations:
                print(f"\n  建议 ({len(recommendations)} 项):")
                for i, rec in enumerate(recommendations[:3]):
                    print(f"    {i+1}. {rec}")
                
        else:
            print("✗ 分析失败")
            print(f"  错误: {result.errors}")
            if hasattr(result, 'warnings') and result.warnings:
                print(f"  警告: {result.warnings}")
                
    except Exception as e:
        print(f"✗ 测试异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_real_llm_business_analyzer()
