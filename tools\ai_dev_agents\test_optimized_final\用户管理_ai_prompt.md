```markdown
# AI 开发任务提示词

生成时间: 2023-11-15

## 任务描述

### 开发任务
开发用户管理模块(user_management)，实现用户注册、认证和基本信息管理功能。主要包含：
1. 用户注册功能 (US-001)
2. 用户认证功能 (US-002)
3. 用户基本信息查询功能

### 预期交付成果
1. 完整的DDD四层架构实现
2. 2个REST API端点
3. 2个数据表模型
4. 单元测试和集成测试套件
5. 完整的API文档

### 成功标准
1. 所有用户故事功能完整实现
2. 通过所有自动化测试
3. 代码覆盖率≥80%
4. 符合DDD架构规范

## 项目上下文

### 架构风格
- 领域驱动设计 (DDD) 四层架构
- 整洁架构原则
- REST API with FastAPI

### 技术栈
- Python 3.10+
- FastAPI (Web框架)
- SQLAlchemy 2.0 (ORM)
- Pydantic v2 (数据验证)
- Alembic (数据库迁移)
- Pytest (测试框架)
- UUID (主键生成)

### 编码标准
- 严格遵循 PEP 8 规范
- 强制类型提示 (Type Hinting)
- 中文注释和文档字符串
- 120 字符行长度限制
- Black 代码格式化

### 现有模块
- oauth_provider: OAuth2认证提供者
- auth: 基础认证功能
- user: 用户基础模块(需重构)

## 架构约束 (必须严格遵循)

### DDD 四层架构
```
modules/user_management/
├── __init__.py
├── interfaces/          # 接口层
│   ├── user_management_api.py
│   └── schemas.py
├── application/         # 应用层
│   ├── services.py
│   └── dtos.py
├── domain/             # 领域层
│   ├── models.py
│   └── repositories.py
└── infrastructure/     # 基础设施层
    ├── repositories.py
    └── orm.py
```

### 依赖方向规则
- interfaces → application → domain ← infrastructure
- 严禁循环依赖
- Domain 层保持纯粹，不依赖任何外部框架

### 模块边界
- 与auth模块交互必须通过应用层服务
- 严禁直接访问其他模块的Domain或Infrastructure层
- 用户角色数据需与auth模块共享

### 数据建模约束
- 所有实体ID使用UUID类型
- SQLAlchemy 2.0风格模型
- Pydantic v2用于数据验证
- 密码必须加密存储

## 实现指南

### 开发步骤 (Domain First 原则)
1. **领域层设计**
   - 在domain/models.py中定义User和UserRole实体
   - 在domain/repositories.py中定义UserRepository接口

2. **应用层实现**
   - 在application/services.py中创建UserService
   - 实现注册、认证、查询等用例逻辑
   - 使用依赖注入获取仓库实例

3. **基础设施层实现**
   - 在infrastructure/orm.py中定义SQLAlchemy模型
   - 在infrastructure/repositories.py中实现UserRepositoryImpl
   - 集成密码加密功能

4. **接口层实现**
   - 在interfaces/schemas.py中定义请求/响应模型
   - 在interfaces/user_management_api.py中实现FastAPI路由
   - 集成到主应用路由

5. **测试实现**
   - Domain层: 纯单元测试
   - Application层: Mock测试
   - Infrastructure层: 集成测试
   - Interfaces层: API测试

### 代码组织原则
- 用户核心业务逻辑在Domain层
- 认证相关逻辑委托给auth模块
- 密码处理在基础设施层
- API文档通过OpenAPI自动生成

## 质量要求

### 代码质量标准
- 单元测试覆盖率 > 80%
- 所有公共方法必须有类型提示
- 中文注释和文档字符串
- 通过所有静态代码检查

### 测试要求
- Domain层: 100%覆盖率
- Application层: 核心用例100%覆盖
- Infrastructure层: 主要集成路径覆盖
- Interfaces层: 所有API端点测试

### 文档要求
- 所有API端点包含OpenAPI文档
- 复杂业务逻辑有中文注释
- 模块README说明架构设计
- 类型提示完整

## 开发需求详情

### 核心领域模型
```python
# domain/models.py
class User:
    id: UUID
    username: str
    email: str
    hashed_password: str
    is_active: bool
    roles: List["UserRole"]

class UserRole:
    id: UUID  
    user_id: UUID
    role_name: str
```

### API端点规范
```python
# interfaces/schemas.py
class UserCreateRequest(BaseModel):
    username: str
    email: EmailStr
    password: str

class UserResponse(BaseModel):
    id: UUID
    username: str
    email: str
    is_active: bool
```

### 测试用例示例
```python
# tests/domain/test_user.py
def test_user_entity():
    user = User(
        id=uuid.uuid4(),
        username="testuser",
        email="<EMAIL>",
        hashed_password="...",
        is_active=True
    )
    assert user.username == "testuser"
    assert user.is_active is True
```

## 验收标准

### 功能验收标准
1. 用户可以通过API成功注册
2. 注册用户可以通过API认证
3. 可以查询用户基本信息
4. 密码安全存储

### 技术验收标准
1. 严格遵循DDD四层架构
2. 通过所有自动化测试
3. 代码覆盖率达标
4. 无架构违规

### 质量验收标准
1. 代码风格一致
2. 文档完整
3. 性能达标(注册<500ms)
4. 安全合规
```