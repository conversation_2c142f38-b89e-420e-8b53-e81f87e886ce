{"module_overview": {"module_name": "category_service", "bounded_context": "Category Management", "primary_responsibility": "Manage MCP server function categories", "business_value": "Provide structured classification for MCP server functions to improve discoverability and organization", "core_capabilities": ["Create and manage categories", "Ensure category name uniqueness"], "external_dependencies": ["oauth_provider for authentication", "auth for authorization", "user for ownership tracking"]}, "user_stories": [{"id": "US-CAT-001", "title": "Create a new category", "story": "As a system administrator, I want to create a new function category so that I can organize MCP server functions", "acceptance_criteria": ["Given valid authentication, when I provide a unique category name, then the category should be created", "Given an existing category name, when I try to create a duplicate, then the system should reject with an error"], "priority": "高", "story_points": 3, "business_rules": ["Category name must be unique", "Category name must be between 3-50 characters"]}, {"id": "US-CAT-002", "title": "List all categories", "story": "As a user, I want to view all available categories so that I can browse MCP functions", "acceptance_criteria": ["Given valid authentication, when I request the category list, then I should receive all active categories"], "priority": "中", "story_points": 2, "business_rules": ["Only active categories should be returned"]}], "api_design": {"base_path": "/api/v1/categories", "endpoints": [{"method": "POST", "path": "/", "summary": "Create a new category", "description": "Creates a new MCP function category with unique name validation", "request_schema": "CategoryCreateRequest", "response_schema": "CategoryResponse", "authentication_required": true, "authorization_roles": ["admin"], "business_logic": "Validates name uniqueness before creation"}, {"method": "GET", "path": "/", "summary": "List all categories", "description": "Returns paginated list of all active categories", "request_schema": null, "response_schema": "CategoryListResponse", "authentication_required": true, "authorization_roles": ["user", "admin"], "business_logic": "Returns only active categories"}], "schemas": [{"name": "CategoryCreateRequest", "type": "request", "fields": [{"name": "name", "type": "string", "required": true, "description": "Unique name for the category", "validation_rules": ["min_length=3", "max_length=50"]}]}, {"name": "CategoryResponse", "type": "response", "fields": [{"name": "id", "type": "UUID", "required": true, "description": "Unique identifier for the category"}, {"name": "name", "type": "string", "required": true, "description": "Category name"}, {"name": "created_at", "type": "datetime", "required": true, "description": "Creation timestamp"}]}, {"name": "CategoryListResponse", "type": "response", "fields": [{"name": "items", "type": "array<CategoryResponse>", "required": true, "description": "List of categories"}, {"name": "total", "type": "integer", "required": true, "description": "Total count of categories"}]}]}, "data_models": {"tables": [{"name": "categories", "description": "Stores MCP function categories", "columns": [{"name": "id", "type": "UUID", "constraints": ["PRIMARY KEY"], "description": "Primary key"}, {"name": "name", "type": "VARCHAR(50)", "constraints": ["NOT NULL", "UNIQUE"], "description": "Unique category name"}, {"name": "created_at", "type": "TIMESTAMP", "constraints": ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"], "description": "Creation timestamp"}, {"name": "is_active", "type": "BOOLEAN", "constraints": ["NOT NULL", "DEFAULT TRUE"], "description": "Soft delete flag"}], "indexes": [{"name": "idx_categories_name", "columns": ["name"], "type": "btree", "unique": true}], "relationships": []}]}, "business_logic": {"domain_entities": [{"name": "Category", "aggregate": "Category", "attributes": [{"name": "id", "type": "UUID", "validation_rules": ["not_null"]}, {"name": "name", "type": "str", "validation_rules": ["min_length=3", "max_length=50", "unique"]}], "business_methods": [{"name": "create", "description": "Creates a new category with validation", "parameters": ["name"], "business_rules": ["Name must be unique", "Name must meet length requirements"], "exceptions": ["DuplicateCategoryError", "InvalidCategoryNameError"]}], "invariants": ["Category name must be unique", "Category must have a valid UUID"]}], "application_services": [{"name": "CategoryService", "description": "Handles category management operations", "methods": [{"name": "create_category", "description": "Creates a new category", "use_case": "US-CAT-001", "transaction_boundary": true, "dependencies": ["CategoryRepository"]}, {"name": "list_categories", "description": "Lists all active categories", "use_case": "US-CAT-002", "transaction_boundary": false, "dependencies": ["CategoryRepository"]}]}], "repositories": [{"name": "CategoryRepository", "entity": "Category", "interface_methods": [{"name": "create", "description": "Persists a new category", "parameters": ["category"], "return_type": "Category"}, {"name": "get_by_name", "description": "Finds category by name", "parameters": ["name"], "return_type": "Optional[Category]"}, {"name": "list_all", "description": "Lists all active categories", "parameters": [], "return_type": "List[Category]"}]}]}, "testing_strategy": {"unit_tests": [{"target": "Category entity", "test_cases": [{"name": "should_create_category_when_name_is_valid", "description": "Test valid category creation", "given": "Valid category name", "when": "Creating category", "then": "Category is created with correct attributes"}, {"name": "should_reject_duplicate_name", "description": "Test duplicate name validation", "given": "Existing category name", "when": "Creating category with same name", "then": "DuplicateCategoryError is raised"}]}], "integration_tests": [{"scope": "CategoryService with Repository", "scenarios": [{"name": "category_creation_flow", "description": "Test complete creation flow", "test_data": "New category name", "expected_outcome": "Category is persisted correctly"}]}], "api_tests": [{"endpoint": "/api/v1/categories/", "test_cases": [{"name": "create_category_success", "method": "POST", "request_data": {"name": "test-category"}, "expected_status": 201, "expected_response": {"name": "test-category"}}, {"name": "create_category_duplicate", "method": "POST", "request_data": {"name": "existing-category"}, "expected_status": 409, "expected_response": {"detail": "Category name already exists"}}]}]}, "technical_constraints": {"architecture_constraints": ["Follow DDD four-layer architecture", "Use UUID as primary key", "Domain layer must not depend on external frameworks"], "performance_requirements": ["API response time < 200ms for read operations", "Database queries should use indexes effectively"], "security_requirements": ["JWT authentication required for all endpoints", "Admin role required for write operations"], "quality_requirements": ["Code coverage > 80%", "Follow PEP 8 style guide", "Type hints for all public interfaces"]}, "implementation_order": ["1. Domain layer - Category entity and repository interface", "2. Infrastructure layer - SQLAlchemy model and repository implementation", "3. Application layer - CategoryService", "4. Interface layer - FastAPI endpoints", "5. Database migration with Alembic", "6. Test implementation (unit, integration, API)"], "project_metadata": {"generation_timestamp": "2025-06-25T11:13:21.424095", "architecture_style": "DDD + FastAPI", "tech_stack": ["FastAPI", "SQLAlchemy", "Pydantic", "Alembic", "<PERSON><PERSON><PERSON>"], "existing_modules": ["oauth_provider", "auth", "user"]}, "dependency_analysis": {"internal_dependencies": [], "external_dependencies": ["oauth_provider for authentication", "auth for authorization", "user for ownership tracking"], "cross_module_dependencies": ["oauth_provider", "auth", "user"]}, "complexity_estimation": {"story_points_total": 5, "api_endpoints_count": 2, "database_tables_count": 1, "domain_entities_count": 1, "estimated_development_days": 2.5}}