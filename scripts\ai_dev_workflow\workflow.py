"""
AI Development Workflow

Main workflow script that orchestrates the entire AI-assisted development process.
"""

import argparse
import json
from pathlib import Path
from typing import List, Optional
import sys

from .prd_analyzer import PRDA<PERSON>yzer
from .domain_identifier import DomainIdentifier
from .requirements_generator import RequirementsGenerator
from .prompt_builder import PromptBuilder


class AIDevWorkflow:
    """Orchestrates the complete AI development workflow."""
    
    def __init__(self, output_dir: str = "output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Initialize components
        self.prd_analyzer = PRDAnalyzer()
        self.domain_identifier = DomainIdentifier()
        self.requirements_generator = RequirementsGenerator()
        self.prompt_builder = PromptBuilder()
    
    def run_complete_workflow(self, prd_file: str, selected_modules: Optional[List[str]] = None) -> None:
        """Run the complete AI development workflow."""
        print("🚀 Starting AI Development Workflow")
        print("=" * 50)
        
        # Step 1: Analyze PRD
        print("\n📋 Step 1: Analyzing PRD document...")
        prd_analysis_file = self._analyze_prd(prd_file)
        print(f"✅ PRD analysis completed: {prd_analysis_file}")
        
        # Step 2: Identify domains
        print("\n🏗️  Step 2: Identifying domain modules...")
        domain_analysis_file = self._identify_domains(prd_analysis_file)
        print(f"✅ Domain analysis completed: {domain_analysis_file}")
        
        # Step 3: Show identified modules and get selection
        modules = self._load_identified_modules(domain_analysis_file)
        if not selected_modules:
            selected_modules = self._select_modules_interactive(modules)
        
        # Step 4: Generate requirements for selected modules
        print(f"\n📝 Step 3: Generating requirements for {len(selected_modules)} modules...")
        requirements_files = []
        for module_name in selected_modules:
            req_file = self._generate_requirements(domain_analysis_file, module_name)
            requirements_files.append(req_file)
            print(f"✅ Requirements generated for {module_name}: {req_file}")
        
        # Step 5: Build AI prompts
        print(f"\n🤖 Step 4: Building AI prompts for {len(selected_modules)} modules...")
        prompt_files = []
        for req_file in requirements_files:
            prompt_file = self._build_ai_prompt(req_file)
            prompt_files.append(prompt_file)
            module_name = Path(req_file).stem.replace('_requirements', '')
            print(f"✅ AI prompt created for {module_name}: {prompt_file}")
        
        # Step 6: Generate summary
        print("\n📊 Step 5: Generating workflow summary...")
        summary_file = self._generate_summary(selected_modules, requirements_files, prompt_files)
        print(f"✅ Workflow summary: {summary_file}")
        
        print("\n🎉 AI Development Workflow completed successfully!")
        print("=" * 50)
        print("\nNext steps:")
        print("1. Review the generated requirements and prompts")
        print("2. Use the AI prompts with your AI agent to generate code")
        print("3. Test and validate the generated code")
        print("4. Iterate as needed")
    
    def _analyze_prd(self, prd_file: str) -> str:
        """Analyze PRD document."""
        self.prd_analyzer.load_prd(prd_file)
        result = self.prd_analyzer.analyze()
        
        output_file = self.output_dir / "prd_analysis.json"
        self.prd_analyzer.export_analysis(result, str(output_file))
        
        return str(output_file)
    
    def _identify_domains(self, prd_analysis_file: str) -> str:
        """Identify domain modules."""
        self.domain_identifier.load_analysis(prd_analysis_file)
        self.domain_identifier.scan_existing_modules()
        
        contexts = self.domain_identifier.identify_bounded_contexts()
        modules = self.domain_identifier.create_domain_modules(contexts)
        
        output_file = self.output_dir / "domain_analysis.json"
        self.domain_identifier.export_domain_analysis(modules, str(output_file))
        
        return str(output_file)
    
    def _load_identified_modules(self, domain_analysis_file: str) -> List[str]:
        """Load identified modules from domain analysis."""
        with open(domain_analysis_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return [module['name'] for module in data['modules']]
    
    def _select_modules_interactive(self, modules: List[str]) -> List[str]:
        """Interactive module selection."""
        print("\n🎯 Identified modules:")
        for i, module in enumerate(modules, 1):
            print(f"  {i}. {module}")
        
        print("\nSelect modules to implement:")
        print("  - Enter numbers separated by commas (e.g., 1,3,4)")
        print("  - Enter 'all' to select all modules")
        print("  - Enter 'none' to exit")
        
        while True:
            selection = input("\nYour selection: ").strip().lower()
            
            if selection == 'none':
                print("Workflow cancelled.")
                sys.exit(0)
            elif selection == 'all':
                return modules
            else:
                try:
                    indices = [int(x.strip()) - 1 for x in selection.split(',')]
                    selected = [modules[i] for i in indices if 0 <= i < len(modules)]
                    if selected:
                        return selected
                    else:
                        print("Invalid selection. Please try again.")
                except (ValueError, IndexError):
                    print("Invalid input format. Please try again.")
    
    def _generate_requirements(self, domain_analysis_file: str, module_name: str) -> str:
        """Generate requirements for a module."""
        self.requirements_generator.load_domain_analysis(domain_analysis_file)
        requirement = self.requirements_generator.generate_requirements(module_name)
        
        output_file = self.output_dir / f"{module_name}_requirements.md"
        self.requirements_generator.export_requirements(requirement, str(output_file))
        
        return str(output_file)
    
    def _build_ai_prompt(self, requirements_file: str) -> str:
        """Build AI prompt from requirements."""
        module_name = Path(requirements_file).stem.replace('_requirements', '')
        task_description = f"Implement the {module_name} module with complete DDD architecture"
        
        prompt = self.prompt_builder.build_prompt(requirements_file, task_description)
        
        output_file = self.output_dir / f"{module_name}_ai_prompt.md"
        self.prompt_builder.export_prompt(prompt, str(output_file))
        
        return str(output_file)
    
    def _generate_summary(self, modules: List[str], requirements_files: List[str], 
                         prompt_files: List[str]) -> str:
        """Generate workflow summary."""
        summary_content = f"""# AI Development Workflow Summary

Generated on: {Path().cwd()}
Modules processed: {len(modules)}

## Processed Modules

{chr(10).join([f"### {module}" + chr(10) + f"- Requirements: {req}" + chr(10) + f"- AI Prompt: {prompt}" + chr(10) for module, req, prompt in zip(modules, requirements_files, prompt_files)])}

## Next Steps

1. **Review Generated Files**
   - Check requirements for completeness and accuracy
   - Verify AI prompts contain all necessary context

2. **AI Code Generation**
   - Use the AI prompts with your preferred AI agent
   - Start with the highest priority module
   - Generate code following the DDD architecture

3. **Implementation Validation**
   - Run tests to ensure code quality
   - Check compliance with project standards
   - Validate business requirements

4. **Integration**
   - Integrate new modules with existing codebase
   - Update main application configuration
   - Run full test suite

## Generated Files

### Analysis Files
- PRD Analysis: output/prd_analysis.json
- Domain Analysis: output/domain_analysis.json

### Requirements Files
{chr(10).join([f"- {Path(req).name}" for req in requirements_files])}

### AI Prompt Files
{chr(10).join([f"- {Path(prompt).name}" for prompt in prompt_files])}

## Tips for AI Code Generation

1. **Use Complete Context**: Always provide the full AI prompt to your AI agent
2. **Iterative Development**: Implement one layer at a time (Domain → Infrastructure → Application → Interface)
3. **Test Early**: Write and run tests as you implement each component
4. **Follow Patterns**: Use existing modules as reference for consistency
5. **Validate Constraints**: Ensure all architectural constraints are followed

## Quality Checklist

- [ ] All modules follow DDD 4-layer architecture
- [ ] UUID primary keys used for all entities
- [ ] Proper file naming conventions followed
- [ ] Type hints included for all functions
- [ ] Comprehensive test coverage
- [ ] API documentation complete
- [ ] Database migrations created
- [ ] Error handling implemented
"""
        
        output_file = self.output_dir / "workflow_summary.md"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(summary_content)
        
        return str(output_file)


def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description='AI Development Workflow')
    parser.add_argument('prd_file', help='Path to PRD document')
    parser.add_argument('-o', '--output', help='Output directory', default='output')
    parser.add_argument('-m', '--modules', help='Comma-separated list of modules to process')
    parser.add_argument('--auto', action='store_true', help='Auto-select all modules (non-interactive)')
    
    args = parser.parse_args()
    
    # Parse selected modules
    selected_modules = None
    if args.modules:
        selected_modules = [m.strip() for m in args.modules.split(',')]
    elif args.auto:
        selected_modules = []  # Will be set to all modules
    
    # Run workflow
    workflow = AIDevWorkflow(args.output)
    workflow.run_complete_workflow(args.prd_file, selected_modules)


if __name__ == "__main__":
    main()
