```markdown
# AI 开发任务提示词

生成时间: 2023-11-15 14:30:00

## 任务描述

### 开发任务
开发 MCP 服务器管理模块，实现以下核心功能：
1. MCP 服务器注册功能
2. MCP 服务器查询功能
3. MCP 服务器工具定义管理功能

### 预期交付成果
1. 完整的 DDD 四层架构模块：mcp_server
2. 3个 REST API 端点实现
3. 2个数据表的 ORM 模型
4. 单元测试和集成测试套件
5. 完整的 OpenAPI 文档

### 成功标准
1. 所有 API 端点通过验收测试
2. 代码覆盖率 ≥ 80%
3. 符合 DDD 架构规范
4. 通过 CI 流水线检查

## 项目上下文

### 架构风格
- 领域驱动设计 (DDD) 四层架构
- 整洁架构原则
- REST API with FastAPI

### 技术栈
- Python 3.10+
- FastAPI (Web 框架)
- SQLAlchemy 2.0 (ORM)
- Pydantic v2 (数据验证)
- Alembic (数据库迁移)
- Pytest (测试框架)

### 编码标准
- 严格遵循 PEP 8 规范
- 强制类型提示 (Type Hinting)
- 中文注释和文档字符串
- 120 字符行长度限制
- Black 代码格式化

### 现有模块
- oauth_provider: OAuth 2.0 认证模块
- auth: 基础认证模块  
- user: 用户管理模块

## 架构约束 (必须严格遵循)

### DDD 四层架构
```
modules/mcp_server/
├── __init__.py
├── interfaces/          # 接口层
│   ├── mcp_server_api.py
│   └── schemas.py
├── application/         # 应用层
│   ├── services.py
│   └── dtos.py
├── domain/             # 领域层
│   ├── models.py
│   └── repositories.py
└── infrastructure/     # 基础设施层
    ├── repositories.py
    └── orm.py
```

### 依赖方向规则
- interfaces → application → domain ← infrastructure
- 严禁循环依赖
- Domain 层保持纯粹，不依赖任何外部框架

### 模块边界
- 模块间通信必须通过 Application 层服务接口
- 严禁直接访问其他模块的 Domain 或 Infrastructure 层
- 通用代码放置在 common/ 目录下

### 数据建模约束
- 所有实体 ID 字段使用 UUID 类型
- SQLAlchemy 2.0 风格模型
- Pydantic v2 用于数据验证

## 实现指南

### 开发步骤 (Domain First 原则)
1. **领域层设计** (modules/mcp_server/domain/)
   - 在 models.py 中定义:
     ```python
     class MCPServer:
         id: UUID
         name: str
         endpoint: str
         status: ServerStatus  # 枚举值

     class ToolDefinition:
         id: UUID
         server_id: UUID
         name: str
         version: str
     ```
   - 在 repositories.py 中定义抽象接口:
     ```python
     class MCPServerRepository(Protocol):
         def add(self, server: MCPServer) -> None: ...
         def get(self, server_id: UUID) -> MCPServer: ...
         def list_all(self) -> list[MCPServer]: ...

     class ToolDefinitionRepository(Protocol):
         def add_tool(self, server_id: UUID, tool: ToolDefinition) -> None: ...
     ```

2. **应用层实现** (modules/mcp_server/application/)
   - 在 services.py 中创建应用服务:
     ```python
     class MCPServerService:
         def __init__(self, server_repo: MCPServerRepository):
             self._repo = server_repo

         def register_server(self, name: str, endpoint: str) -> UUID:
             # 业务逻辑实现
     ```

3. **基础设施层实现** (modules/mcp_server/infrastructure/)
   - 在 orm.py 中定义 SQLAlchemy 模型
   - 在 repositories.py 中实现仓库接口:
     ```python
     class MCPServerRepositoryImpl:
         def __init__(self, session: AsyncSession):
             self._session = session
     ```

4. **接口层实现** (modules/mcp_server/interfaces/)
   - 在 schemas.py 中定义 Pydantic 模型
   - 在 mcp_server_api.py 中创建路由:
     ```python
     @router.post("/servers")
     async def register_server(
         payload: RegisterServerRequest,
         service: MCPServerService = Depends()
     ) -> ServerResponse:
         # 调用应用服务
     ```

5. **集成到主应用**
   - 在 main.py 中注册路由
   - 配置依赖注入

## 质量要求

### 代码质量标准
- 单元测试覆盖率 > 80%
- 所有公共方法必须有类型提示
- 中文注释和文档字符串
- 通过 CI 代码质量检查

### 测试要求
1. Domain 层测试：
   - 测试领域模型行为
   - 纯单元测试，无外部依赖

2. Application 层测试：
   - 使用 unittest.mock 模拟依赖
   - 测试服务方法逻辑

3. Infrastructure 层测试：
   - 使用测试数据库
   - 测试仓库实现

4. API 层测试：
   - 端到端测试
   - 测试 API 契约

### 文档要求
- 所有 API 端点包含 OpenAPI 文档
- 业务逻辑方法包含中文文档字符串
- 模块 README 文档

## 验收标准

### 功能验收标准
1. 成功实现以下 API：
   - POST /api/v1/mcp/servers - 注册新服务器
   - GET /api/v1/mcp/servers - 查询服务器列表
   - POST /api/v1/mcp/servers/{server_id}/tools - 添加工具定义

2. 数据持久化正确：
   - 服务器信息存入 mcp_servers 表
   - 工具定义存入 tool_definitions 表

### 技术验收标准
1. 严格遵循 DDD 四层架构
2. 符合依赖方向规则
3. 通过所有静态代码检查

### 质量验收标准
1. 测试覆盖率 ≥ 80%
2. 所有公共 API 有文档
3. 代码符合 PEP 8 规范
```