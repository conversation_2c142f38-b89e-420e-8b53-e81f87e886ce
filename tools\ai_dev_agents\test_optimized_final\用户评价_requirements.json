{"module_overview": {"module_name": "review", "bounded_context": "User <PERSON>", "primary_responsibility": "Manage user reviews for MCP servers", "business_value": "Collect and display user feedback to improve service quality", "core_capabilities": ["Create and manage reviews", "Validate review data", "Associate reviews with users and servers"], "external_dependencies": ["user module", "auth module", "oauth_provider"]}, "user_stories": [{"id": "US-001", "title": "Submit a review", "story": "As a registered user, I want to submit a review for an MCP server so that I can share my experience", "acceptance_criteria": ["Given I'm authenticated, when I submit valid review data, then the review is saved", "Given I'm not authenticated, when I try to submit a review, then I receive a 401 error", "Given I submit invalid rating (e.g., 6), when I try to submit, then I receive a validation error"], "priority": "高", "story_points": 3, "business_rules": ["Review must be associated with existing user", "Rating must be between 1-5", "User can only submit one review per server"]}, {"id": "US-002", "title": "View server reviews", "story": "As any user, I want to view reviews for an MCP server so I can evaluate its quality", "acceptance_criteria": ["Given a server exists, when I request its reviews, then I see all valid reviews", "Given a server has no reviews, when I request its reviews, then I see an empty list"], "priority": "中", "story_points": 2, "business_rules": ["Only show approved reviews", "Sort reviews by creation date (newest first)"]}], "api_design": {"base_path": "/api/v1/reviews", "endpoints": [{"method": "POST", "path": "/", "summary": "Create a new review", "description": "Submit a review for an MCP server", "request_schema": "ReviewCreateRequest", "response_schema": "ReviewResponse", "authentication_required": true, "authorization_roles": ["user"], "business_logic": "Validates user and server existence, checks for duplicate reviews"}, {"method": "GET", "path": "/servers/{server_id}", "summary": "Get reviews for server", "description": "Retrieve all reviews for a specific MCP server", "request_schema": null, "response_schema": "ReviewListResponse", "authentication_required": false, "authorization_roles": [], "business_logic": "Filters reviews by server, applies sorting"}], "schemas": [{"name": "ReviewCreateRequest", "type": "request", "fields": [{"name": "server_id", "type": "UUID", "required": true, "description": "ID of the MCP server being reviewed", "validation_rules": ["must be valid UUID", "server must exist"]}, {"name": "rating", "type": "Integer", "required": true, "description": "Rating score (1-5)", "validation_rules": ["min:1", "max:5"]}, {"name": "comment", "type": "String", "required": false, "description": "Optional review text", "validation_rules": ["max_length:500"]}]}, {"name": "ReviewResponse", "type": "response", "fields": [{"name": "id", "type": "UUID", "required": true, "description": "Review ID", "validation_rules": []}, {"name": "rating", "type": "Integer", "required": true, "description": "Rating score", "validation_rules": []}, {"name": "created_at", "type": "DateTime", "required": true, "description": "Review creation timestamp", "validation_rules": []}]}]}, "data_models": {"tables": [{"name": "reviews", "description": "Stores user reviews for MCP servers", "columns": [{"name": "id", "type": "UUID", "constraints": ["PRIMARY KEY"], "description": "Unique review identifier"}, {"name": "user_id", "type": "UUID", "constraints": ["NOT NULL"], "description": "Reference to user who created review"}, {"name": "server_id", "type": "UUID", "constraints": ["NOT NULL"], "description": "Reference to reviewed MCP server"}, {"name": "rating", "type": "Integer", "constraints": ["NOT NULL", "CHECK (rating BETWEEN 1 AND 5)"], "description": "Rating score (1-5)"}, {"name": "comment", "type": "Text", "constraints": [], "description": "Optional review text"}, {"name": "created_at", "type": "Timestamp", "constraints": ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"], "description": "Review creation time"}], "indexes": [{"name": "idx_reviews_server", "columns": ["server_id"], "type": "btree", "unique": false}, {"name": "idx_reviews_user_server", "columns": ["user_id", "server_id"], "type": "btree", "unique": true}], "relationships": [{"type": "foreign_key", "target_table": "users", "columns": ["user_id"], "target_columns": ["id"]}]}]}, "business_logic": {"domain_entities": [{"name": "Review", "aggregate": "Review", "attributes": [{"name": "id", "type": "UUID", "validation_rules": ["not null"]}, {"name": "user_id", "type": "UUID", "validation_rules": ["not null", "user must exist"]}, {"name": "server_id", "type": "UUID", "validation_rules": ["not null", "server must exist"]}, {"name": "rating", "type": "Integer", "validation_rules": ["min:1", "max:5"]}], "business_methods": [{"name": "validate_rating", "description": "Ensure rating is within valid range", "parameters": [], "business_rules": ["rating must be 1-5"], "exceptions": ["InvalidRatingError"]}], "invariants": ["Review must have valid user reference", "Review must have valid server reference"]}], "application_services": [{"name": "ReviewService", "description": "Handles review-related operations", "methods": [{"name": "create_review", "description": "Creates a new review", "use_case": "User submits review for server", "transaction_boundary": true, "dependencies": ["ReviewRepository", "UserService", "ServerService"]}, {"name": "get_reviews_for_server", "description": "Retrieves reviews for a server", "use_case": "View server reviews", "transaction_boundary": false, "dependencies": ["ReviewRepository"]}]}], "repositories": [{"name": "ReviewRepository", "entity": "Review", "interface_methods": [{"name": "save", "description": "Persists a review", "parameters": ["review"], "return_type": "Review"}, {"name": "find_by_server", "description": "Finds reviews by server ID", "parameters": ["server_id"], "return_type": "List[Review]"}, {"name": "exists_by_user_and_server", "description": "Checks if user already reviewed server", "parameters": ["user_id", "server_id"], "return_type": "bool"}]}]}, "testing_strategy": {"unit_tests": [{"target": "Review entity", "test_cases": [{"name": "should_raise_error_when_rating_is_zero", "description": "Test invalid rating validation", "given": "Review with rating 0", "when": "validating rating", "then": "raises InvalidRatingError"}]}, {"target": "ReviewService", "test_cases": [{"name": "should_prevent_duplicate_reviews", "description": "Test duplicate review prevention", "given": "User has existing review for server", "when": "attempting to create another review", "then": "raises DuplicateReviewError"}]}], "integration_tests": [{"scope": "Review creation flow", "scenarios": [{"name": "successful_review_creation", "description": "Test complete review submission flow", "test_data": "Valid review data", "expected_outcome": "Review is persisted in database"}]}], "api_tests": [{"endpoint": "/api/v1/reviews/", "test_cases": [{"name": "create_review_unauthorized", "method": "POST", "request_data": "Valid review data", "expected_status": 401, "expected_response": "Authentication required"}]}]}, "technical_constraints": {"architecture_constraints": ["Must follow DDD four-layer architecture", "Domain layer must not depend on external frameworks", "Use UUID as primary keys", "Follow FastAPI best practices"], "performance_requirements": ["API response time < 300ms", "Database queries must use indexes", "Paginate large review lists"], "security_requirements": ["Require OAuth2 authentication for write operations", "Validate all input data", "Implement rate limiting"], "quality_requirements": ["Code coverage > 85%", "Follow PEP 8 style guide", "Type hints for all public interfaces"]}, "implementation_order": ["1. Domain layer - Review entity and repository interface", "2. Infrastructure layer - SQLAlchemy model and repository implementation", "3. Application layer - ReviewService", "4. Interface layer - FastAPI endpoints", "5. Database migration (Alembic)", "6. Test implementation (unit, integration, API)"], "project_metadata": {"generation_timestamp": "2025-06-25T11:15:52.985003", "architecture_style": "DDD + FastAPI", "tech_stack": ["FastAPI", "SQLAlchemy", "Pydantic", "Alembic", "<PERSON><PERSON><PERSON>"], "existing_modules": ["oauth_provider", "auth", "user"]}, "dependency_analysis": {"internal_dependencies": [], "external_dependencies": ["user module", "auth module", "oauth_provider"], "cross_module_dependencies": ["oauth_provider", "auth", "user"]}, "complexity_estimation": {"story_points_total": 5, "api_endpoints_count": 2, "database_tables_count": 1, "domain_entities_count": 1, "estimated_development_days": 2.5}}