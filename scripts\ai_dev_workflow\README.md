# AI辅助开发流程工具

这是一套完整的AI辅助业务功能开发工具，帮助您从产品需求文档（PRD）到最终的代码实现，建立标准化、高效的开发流程。

## 🎯 工具概览

### 核心组件

1. **PRD分析器** (`prd_analyzer.py`) - 分析产品需求文档，提取业务信息
2. **领域识别器** (`domain_identifier.py`) - 基于DDD方法识别业务模块
3. **需求生成器** (`requirements_generator.py`) - 生成详细的技术开发需求
4. **提示词构建器** (`prompt_builder.py`) - 构建包含项目上下文的AI开发提示词
5. **工作流程编排器** (`workflow.py`) - 编排完整的开发流程

### 工作流程

```mermaid
graph LR
    A[PRD文档] --> B[PRD分析]
    B --> C[领域识别]
    C --> D[需求生成]
    D --> E[提示词构建]
    E --> F[AI代码生成]
```

## 🚀 快速开始

### 方式一：完整工作流程（推荐）

```bash
# 运行完整的AI开发工作流程
python -m scripts.ai_dev_workflow.cli workflow design/mcp-market-prd.txt

# 指定输出目录
python -m scripts.ai_dev_workflow.cli workflow design/mcp-market-prd.txt -o my_output

# 只处理特定模块
python -m scripts.ai_dev_workflow.cli workflow design/mcp-market-prd.txt -m "server_catalog,user_identity"
```

### 方式二：分步执行

```bash
# 1. 分析PRD文档
python -m scripts.ai_dev_workflow.cli analyze-prd design/mcp-market-prd.txt -o prd_analysis.json

# 2. 识别领域模块
python -m scripts.ai_dev_workflow.cli identify-domains prd_analysis.json -o domain_analysis.json

# 3. 生成特定模块的开发需求
python -m scripts.ai_dev_workflow.cli generate-req domain_analysis.json server_catalog -o server_catalog_requirements.md

# 4. 构建AI开发提示词
python -m scripts.ai_dev_workflow.cli build-prompt server_catalog_requirements.md -o server_catalog_ai_prompt.md
```

## 📋 详细使用说明

### 1. PRD分析

**功能**：从PRD文档中提取结构化的业务信息

**输入**：PRD文档（Markdown格式）
**输出**：JSON格式的分析结果

```bash
python -m scripts.ai_dev_workflow.cli analyze-prd design/mcp-market-prd.txt
```

**输出内容**：
- 项目概览
- 目标用户
- 核心功能
- 业务实体
- 功能需求
- 非功能需求

### 2. 领域模块识别

**功能**：基于DDD方法识别业务模块和边界上下文

**输入**：PRD分析结果（JSON）
**输出**：领域分析结果（JSON）

```bash
python -m scripts.ai_dev_workflow.cli identify-domains prd_analysis.json
```

**输出内容**：
- 边界上下文定义
- 领域实体设计
- 仓库接口规范
- 模块依赖关系
- 建议的文件结构

### 3. 开发需求生成

**功能**：将业务需求转换为具体的技术开发任务

**输入**：领域分析结果（JSON）+ 模块名称
**输出**：开发需求文档（Markdown）

```bash
python -m scripts.ai_dev_workflow.cli generate-req domain_analysis.json server_catalog
```

**输出内容**：
- 业务概览
- 用户故事
- 领域模型设计
- API端点规范
- 数据库设计
- 测试用例规范
- 实现顺序
- 验收标准

### 4. AI提示词构建

**功能**：构建包含完整项目上下文的AI开发提示词

**输入**：开发需求文档（Markdown）
**输出**：AI提示词文档（Markdown）

```bash
python -m scripts.ai_dev_workflow.cli build-prompt server_catalog_requirements.md
```

**输出内容**：
- 任务描述
- 项目上下文
- 架构约束
- 实现指南
- 质量要求
- 验收标准
- 参考示例
- 项目规范

## 📁 输出文件结构

运行完整工作流程后，会在输出目录生成以下文件：

```
output/
├── prd_analysis.json              # PRD分析结果
├── domain_analysis.json           # 领域分析结果
├── workflow_summary.md            # 工作流程总结
├── server_catalog_requirements.md # 模块开发需求
├── server_catalog_ai_prompt.md    # AI开发提示词
├── user_identity_requirements.md  # 其他模块需求...
└── user_identity_ai_prompt.md     # 其他模块提示词...
```

## 🤖 使用AI提示词进行代码生成

生成的AI提示词文件包含了完整的项目上下文，可以直接用于AI代码生成：

### 步骤1：准备提示词
```bash
# 复制生成的AI提示词内容
cat output/server_catalog_ai_prompt.md
```

### 步骤2：与AI Agent交互
将提示词内容提供给您的AI助手（如Claude、GPT-4等），并说明：

```
请根据以下提示词和需求，为我实现server_catalog模块。请严格按照DDD架构和项目规范进行开发。

[粘贴AI提示词内容]

请从实现计划开始，然后逐步实现每个层次的代码。
```

### 步骤3：迭代开发
- AI会按照DDD层次顺序实现代码
- 每完成一个层次，运行测试验证
- 根据测试结果调整和优化代码
- 重复直到满足所有验收标准

## 🔧 高级用法

### 自定义模块选择

```bash
# 交互式选择模块
python -m scripts.ai_dev_workflow.cli workflow design/mcp-market-prd.txt

# 自动选择所有模块（非交互）
python -m scripts.ai_dev_workflow.cli workflow design/mcp-market-prd.txt --auto

# 指定特定模块
python -m scripts.ai_dev_workflow.cli workflow design/mcp-market-prd.txt -m "server_catalog,community_feedback"
```

### 自定义输出目录

```bash
# 指定输出目录
python -m scripts.ai_dev_workflow.cli workflow design/mcp-market-prd.txt -o custom_output_dir
```

### 单独使用各个工具

```bash
# 只分析PRD
python scripts/ai_dev_workflow/prd_analyzer.py design/mcp-market-prd.txt

# 只识别领域
python scripts/ai_dev_workflow/domain_identifier.py prd_analysis.json

# 只生成需求
python scripts/ai_dev_workflow/requirements_generator.py domain_analysis.json server_catalog

# 只构建提示词
python scripts/ai_dev_workflow/prompt_builder.py server_catalog_requirements.md
```

## 📝 最佳实践

### 1. PRD文档准备
- 确保PRD文档结构清晰，使用标准的Markdown格式
- 包含明确的功能需求和业务实体描述
- 提供用户故事和验收标准

### 2. 模块选择策略
- 优先实现核心业务模块
- 考虑模块间的依赖关系
- 从简单到复杂逐步实现

### 3. AI代码生成
- 使用完整的AI提示词，不要省略项目上下文
- 按照DDD层次顺序实现：Domain → Infrastructure → Application → Interface
- 每完成一个组件就运行测试验证
- 保持与现有模块的一致性

### 4. 质量保证
- 运行 `inv ci` 检查代码质量
- 确保测试覆盖率达标
- 验证所有验收标准
- 更新相关文档

## 🐛 故障排除

### 常见问题

1. **模块导入错误**
   ```bash
   # 确保在项目根目录运行
   cd /path/to/ai4se-mcp-hub
   python -m scripts.ai_dev_workflow.cli workflow design/mcp-market-prd.txt
   ```

2. **PRD文档解析失败**
   - 检查文档编码是否为UTF-8
   - 确保Markdown格式正确
   - 验证文件路径是否正确

3. **输出目录权限问题**
   ```bash
   # 确保有写入权限
   chmod 755 output/
   ```

## 🔄 工作流程集成

这套工具可以与现有的开发流程无缝集成：

1. **需求分析阶段**：使用PRD分析器提取业务需求
2. **架构设计阶段**：使用领域识别器设计模块架构
3. **开发计划阶段**：使用需求生成器制定开发计划
4. **代码实现阶段**：使用AI提示词进行代码生成
5. **测试验证阶段**：运行生成的测试用例验证功能

通过这套工具，您可以建立标准化、可重复的AI辅助开发流程，大大提高开发效率和代码质量。
