{"module_overview": {"module_name": "CommunityInteraction", "bounded_context": "Community Interaction Context", "primary_responsibility": "Manage server reviews and user feedback", "business_value": "Enhance user engagement and collect valuable feedback for server improvement", "core_capabilities": ["Review management", "Feedback collection", "Community interaction features"], "external_dependencies": ["User module", "Authentication service", "MCP_Server module"]}, "user_stories": [{"id": "US-001", "title": "Submit server review", "story": "As a registered user, I want to submit a review for a server, so that I can share my experience", "acceptance_criteria": ["Given I'm authenticated and the server exists, when I submit a valid review, then it should be saved", "Given I'm not authenticated, when I try to submit a review, then I should receive a 401 error", "Given the server doesn't exist, when I submit a review, then I should receive a 404 error"], "priority": "高", "story_points": 3, "business_rules": ["Review must be linked to existing User and MCP_Server", "Rating must be between 1-5", "Comment must be less than 1000 characters"]}, {"id": "US-002", "title": "View server reviews", "story": "As any user, I want to view reviews for a server, so that I can see community feedback", "acceptance_criteria": ["Given a server exists, when I request its reviews, then I should see all approved reviews", "Given a server has no reviews, when I request its reviews, then I should see an empty list"], "priority": "中", "story_points": 2, "business_rules": ["Reviews should be ordered by timestamp descending", "Only show approved reviews to non-admin users"]}], "api_design": {"base_path": "/api/v1/reviews", "endpoints": [{"method": "POST", "path": "/", "summary": "Create a new review", "description": "Submit a review for a specific server", "request_schema": "ReviewCreateRequest", "response_schema": "ReviewResponse", "authentication_required": true, "authorization_roles": ["user"], "business_logic": "Validates user and server existence, applies business rules for rating and comment"}, {"method": "GET", "path": "/server/{server_id}", "summary": "Get reviews for a server", "description": "Retrieve all approved reviews for a specific server", "request_schema": null, "response_schema": "ReviewListResponse", "authentication_required": false, "authorization_roles": [], "business_logic": "Filters reviews by server and approval status, orders by timestamp"}], "schemas": [{"name": "ReviewCreateRequest", "type": "request", "fields": [{"name": "server_id", "type": "UUID", "required": true, "description": "ID of the server being reviewed", "validation_rules": ["valid UUID"]}, {"name": "rating", "type": "Integer", "required": true, "description": "Rating from 1 to 5 stars", "validation_rules": ["min:1", "max:5"]}, {"name": "comment", "type": "String", "required": false, "description": "Optional review text", "validation_rules": ["max_length:1000"]}]}, {"name": "ReviewResponse", "type": "response", "fields": [{"name": "id", "type": "UUID", "required": true, "description": "Review ID", "validation_rules": []}, {"name": "rating", "type": "Integer", "required": true, "description": "Rating value", "validation_rules": []}, {"name": "comment", "type": "String", "required": false, "description": "Review text", "validation_rules": []}, {"name": "timestamp", "type": "DateTime", "required": true, "description": "When review was created", "validation_rules": []}]}, {"name": "ReviewListResponse", "type": "response", "fields": [{"name": "reviews", "type": "Array<ReviewResponse>", "required": true, "description": "List of reviews", "validation_rules": []}]}]}, "data_models": {"tables": [{"name": "reviews", "description": "Stores user reviews of servers", "columns": [{"name": "id", "type": "UUID", "constraints": ["PRIMARY KEY"], "description": "Unique review identifier"}, {"name": "user_id", "type": "UUID", "constraints": ["NOT NULL"], "description": "User who created the review"}, {"name": "server_id", "type": "UUID", "constraints": ["NOT NULL"], "description": "Server being reviewed"}, {"name": "rating", "type": "INTEGER", "constraints": ["NOT NULL", "CHECK (rating BETWEEN 1 AND 5)"], "description": "Star rating (1-5)"}, {"name": "comment", "type": "TEXT", "constraints": [], "description": "Review text content"}, {"name": "timestamp", "type": "TIMESTAMP", "constraints": ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"], "description": "When review was created"}, {"name": "is_approved", "type": "BOOLEAN", "constraints": ["NOT NULL", "DEFAULT FALSE"], "description": "Moderation status"}], "indexes": [{"name": "idx_reviews_server_id", "columns": ["server_id"], "type": "btree", "unique": false}, {"name": "idx_reviews_user_server", "columns": ["user_id", "server_id"], "type": "btree", "unique": true}], "relationships": [{"type": "foreign_key", "target_table": "users", "columns": ["user_id"], "target_columns": ["id"]}, {"type": "foreign_key", "target_table": "mc<PERSON><PERSON><PERSON>", "columns": ["server_id"], "target_columns": ["id"]}]}]}, "business_logic": {"domain_entities": [{"name": "Review", "aggregate": "ReviewAggregate", "attributes": [{"name": "id", "type": "UUID", "validation_rules": ["not null"]}, {"name": "user_id", "type": "UUID", "validation_rules": ["not null", "valid user reference"]}, {"name": "server_id", "type": "UUID", "validation_rules": ["not null", "valid server reference"]}, {"name": "rating", "type": "Integer", "validation_rules": ["between 1 and 5"]}, {"name": "comment", "type": "String", "validation_rules": ["max length 1000"]}, {"name": "timestamp", "type": "DateTime", "validation_rules": ["not null"]}], "business_methods": [{"name": "validate", "description": "Validate review business rules", "parameters": [], "business_rules": ["Rating must be between 1-5", "Comment must be <= 1000 chars", "User and server must exist"], "exceptions": ["InvalidRatingError", "InvalidCommentError", "InvalidReferenceError"]}], "invariants": ["Review must reference valid user and server", "Rating must be between 1-5"]}], "application_services": [{"name": "ReviewService", "description": "Handles review-related operations", "methods": [{"name": "create_review", "description": "Creates a new review", "use_case": "User submits review for server", "transaction_boundary": true, "dependencies": ["ReviewRepository", "UserService", "ServerService"]}, {"name": "get_reviews_for_server", "description": "Retrieves reviews for a server", "use_case": "User views server reviews", "transaction_boundary": false, "dependencies": ["ReviewRepository"]}]}], "repositories": [{"name": "ReviewRepository", "entity": "Review", "interface_methods": [{"name": "save", "description": "Persists a review", "parameters": ["review: Review"], "return_type": "Review"}, {"name": "find_by_server", "description": "Finds reviews for a server", "parameters": ["server_id: UUID"], "return_type": "List[Review]"}]}]}, "testing_strategy": {"unit_tests": [{"target": "Review entity", "test_cases": [{"name": "should_validate_successfully_when_all_rules_are_met", "description": "Test valid review passes validation", "given": "Valid review data", "when": "validate() is called", "then": "No exceptions are raised"}, {"name": "should_throw_exception_when_rating_is_out_of_range", "description": "Test invalid rating is rejected", "given": "Review with rating 0", "when": "validate() is called", "then": "InvalidRatingError is raised"}]}], "integration_tests": [{"scope": "Review creation", "scenarios": [{"name": "create_review_happy_path", "description": "Successful review creation", "test_data": "Valid user, valid server, valid review data", "expected_outcome": "Review is persisted in database"}, {"name": "create_review_invalid_server", "description": "Attempt to review non-existent server", "test_data": "Valid user, invalid server ID", "expected_outcome": "404 error is returned"}]}], "api_tests": [{"endpoint": "/api/v1/reviews/", "test_cases": [{"name": "POST_create_review_authenticated", "method": "POST", "request_data": "Valid review payload", "expected_status": 201, "expected_response": "Contains created review data"}, {"name": "POST_create_review_unauthenticated", "method": "POST", "request_data": "Valid review payload", "expected_status": 401, "expected_response": "Authentication error"}]}]}, "technical_constraints": {"architecture_constraints": ["Must follow DDD four-layer architecture", "Domain layer must not depend on external frameworks", "Use UUID as primary keys", "Follow FastAPI best practices"], "performance_requirements": ["API response time < 300ms", "Review queries should use indexes", "Pagination for large review sets"], "security_requirements": ["Authentication required for review submission", "Input validation for all API endpoints", "SQL injection prevention"], "quality_requirements": ["Code coverage > 85%", "Follow PEP 8 style guide", "Type hints for all public methods"]}, "implementation_order": ["1. Domain layer - Review entity and repository interface", "2. Infrastructure layer - SQLAlchemy models and repository implementation", "3. Application layer - ReviewService", "4. Interface layer - API endpoints", "5. Database migration for reviews table", "6. Test implementation (unit -> integration -> API)", "7. Documentation (OpenAPI spec, README)"], "project_metadata": {"generation_timestamp": "2025-06-25T10:20:21.720378", "architecture_style": "DDD + FastAPI", "tech_stack": ["FastAPI", "SQLAlchemy", "Pydantic", "Alembic", "<PERSON><PERSON><PERSON>"], "existing_modules": ["user", "auth", "oauth_provider"]}, "dependency_analysis": {"internal_dependencies": [], "external_dependencies": ["User module", "Authentication service", "MCP_Server module"], "cross_module_dependencies": ["user"]}, "complexity_estimation": {"story_points_total": 5, "api_endpoints_count": 2, "database_tables_count": 1, "domain_entities_count": 1, "estimated_development_days": 2.5}}