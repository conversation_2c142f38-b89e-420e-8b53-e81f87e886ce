"""
Configuration Manager for AI Development Agents

Handles loading and processing configuration from YAML files and environment variables.
"""

import os
import re
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

try:
    from langchain_openai import ChatOpenAI
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False


@dataclass
class LLMConfig:
    """LLM configuration data class."""
    provider: str
    api_key: str
    base_url: str
    model: str
    temperature: float
    max_tokens: int
    timeout: int
    headers: Optional[Dict[str, str]] = None


class ConfigManager:
    """Configuration manager for AI development agents."""
    
    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_path: Path to configuration file. If None, uses default location.
        """
        self.logger = logging.getLogger("config.manager")
        
        # Determine config file path
        if config_path is None:
            config_path = Path(__file__).parent.parent / "config.yaml"
        else:
            config_path = Path(config_path)
        
        self.config_path = config_path
        self.config = {}
        self._load_config()
    
    def _load_config(self) -> None:
        """Load configuration from file."""
        if not YAML_AVAILABLE:
            self.logger.warning("PyYAML not available. Using default configuration.")
            self._load_default_config()
            return
        
        if not self.config_path.exists():
            self.logger.warning(f"Config file not found: {self.config_path}. Using default configuration.")
            self._load_default_config()
            return
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                raw_config = yaml.safe_load(f)
            
            # Process environment variable substitutions
            self.config = self._process_env_vars(raw_config)
            self.logger.info(f"Configuration loaded from: {self.config_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to load config: {e}")
            self._load_default_config()
    
    def _load_default_config(self) -> None:
        """Load default configuration when file is not available."""
        self.config = {
            "llm": {
                "provider": "openrouter",
                "openrouter": {
                    "api_key": os.getenv("OPENROUTER_API_KEY", ""),
                    "base_url": "https://openrouter.ai/api/v1",
                    "model": "anthropic/claude-3-sonnet",
                    "temperature": 0.1,
                    "max_tokens": 4000,
                    "timeout": 60,
                    "headers": {
                        "HTTP-Referer": "https://github.com/agilemetrics-tech/ai4se-mcp-hub",
                        "X-Title": "AI4SE MCP Hub Development"
                    }
                }
            },
            "system": {
                "log_level": "INFO",
                "verbose": True,
                "retry": {
                    "max_attempts": 3,
                    "delay_seconds": 1
                }
            },
            "project": {
                "architecture_style": "DDD + FastAPI",
                "tech_stack": ["FastAPI", "SQLAlchemy", "Pydantic"],
                "existing_modules": ["auth", "user"]
            }
        }
    
    def _process_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Process environment variable substitutions in config."""
        def substitute_env_vars(obj):
            if isinstance(obj, dict):
                return {k: substitute_env_vars(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [substitute_env_vars(item) for item in obj]
            elif isinstance(obj, str):
                # Replace ${VAR_NAME} with environment variable value
                pattern = r'\$\{([^}]+)\}'
                matches = re.findall(pattern, obj)
                for var_name in matches:
                    env_value = os.getenv(var_name, "")
                    obj = obj.replace(f"${{{var_name}}}", env_value)
                return obj
            else:
                return obj
        
        return substitute_env_vars(config)
    
    def get_llm_config(self, preset: Optional[str] = None) -> LLMConfig:
        """
        Get LLM configuration.
        
        Args:
            preset: Optional preset name to use instead of default config.
            
        Returns:
            LLMConfig object with LLM settings.
        """
        if preset and "model_presets" in self.config and preset in self.config["model_presets"]:
            # Use preset configuration
            preset_config = self.config["model_presets"][preset]
            provider = preset_config["provider"]
            
            # Merge preset with provider config
            provider_config = self.config["llm"].get(provider, {})
            merged_config = {**provider_config, **preset_config}
            
            return LLMConfig(
                provider=provider,
                api_key=merged_config.get("api_key", ""),
                base_url=merged_config.get("base_url", ""),
                model=merged_config["model"],
                temperature=merged_config.get("temperature", 0.1),
                max_tokens=merged_config.get("max_tokens", 4000),
                timeout=merged_config.get("timeout", 60),
                headers=merged_config.get("headers")
            )
        else:
            # Use default configuration
            llm_config = self.config.get("llm", {})
            provider = llm_config.get("provider", "openrouter")
            provider_config = llm_config.get(provider, {})
            
            return LLMConfig(
                provider=provider,
                api_key=provider_config.get("api_key", ""),
                base_url=provider_config.get("base_url", ""),
                model=provider_config.get("model", "anthropic/claude-3-sonnet"),
                temperature=provider_config.get("temperature", 0.1),
                max_tokens=provider_config.get("max_tokens", 4000),
                timeout=provider_config.get("timeout", 60),
                headers=provider_config.get("headers")
            )
    
    def create_llm(self, preset: Optional[str] = None):
        """
        Create LLM instance based on configuration.
        
        Args:
            preset: Optional preset name to use.
            
        Returns:
            LLM instance or None if creation fails.
        """
        if not LANGCHAIN_AVAILABLE:
            self.logger.error("LangChain not available. Cannot create LLM.")
            return None
        
        config = self.get_llm_config(preset)
        
        if not config.api_key:
            self.logger.error(f"API key not found for provider: {config.provider}")
            return None
        
        try:
            if config.provider in ["openrouter", "openai"]:
                llm = ChatOpenAI(
                    api_key=config.api_key,
                    base_url=config.base_url,
                    model=config.model,
                    temperature=config.temperature,
                    max_tokens=config.max_tokens,
                    timeout=config.timeout,
                    default_headers=config.headers or {}
                )
                self.logger.info(f"LLM created: {config.provider}/{config.model}")
                return llm
            else:
                self.logger.error(f"Unsupported provider: {config.provider}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to create LLM: {e}")
            return None
    
    def get_system_config(self) -> Dict[str, Any]:
        """Get system configuration."""
        return self.config.get("system", {})
    
    def get_project_config(self) -> Dict[str, Any]:
        """Get project configuration."""
        return self.config.get("project", {})
    
    def get_agent_config(self, agent_name: str) -> Dict[str, Any]:
        """Get configuration for specific agent."""
        agents_config = self.config.get("agents", {})
        return agents_config.get(agent_name, {})
    
    def get_environment_config(self, env_name: str) -> Dict[str, Any]:
        """Get environment-specific configuration."""
        environments = self.config.get("environments", {})
        return environments.get(env_name, {})
    
    def list_available_presets(self) -> Dict[str, str]:
        """List available model presets with descriptions."""
        presets = self.config.get("model_presets", {})
        return {name: preset.get("description", "No description") 
                for name, preset in presets.items()}
    
    def validate_config(self) -> Dict[str, Any]:
        """
        Validate configuration and return validation results.
        
        Returns:
            Dictionary with validation results.
        """
        results = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Check LLM configuration
        llm_config = self.get_llm_config()
        if not llm_config.api_key:
            results["errors"].append(f"API key not set for provider: {llm_config.provider}")
            results["valid"] = False
        
        # Check dependencies
        if not YAML_AVAILABLE:
            results["warnings"].append("PyYAML not installed. Using default configuration.")
        
        if not LANGCHAIN_AVAILABLE:
            results["errors"].append("LangChain not installed. Cannot create LLM instances.")
            results["valid"] = False
        
        return results
