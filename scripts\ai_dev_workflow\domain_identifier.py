"""
Domain Module Identifier

This module identifies domain modules and bounded contexts using DDD methodology.
"""

import json
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Set
from .prd_analyzer import PRDAnalysisResult, BusinessEntity


@dataclass
class DomainEntity:
    """Represents a domain entity with its properties."""
    name: str
    description: str
    attributes: List[str]
    business_methods: List[str]
    invariants: List[str]


@dataclass
class ValueObject:
    """Represents a value object in the domain."""
    name: str
    description: str
    properties: List[str]


@dataclass
class Repository:
    """Represents a repository interface."""
    name: str
    entity_name: str
    methods: List[str]


@dataclass
class BoundedContext:
    """Represents a bounded context in DDD."""
    name: str
    description: str
    core_responsibility: str
    entities: List[DomainEntity]
    value_objects: List[ValueObject]
    repositories: List[Repository]
    use_cases: List[str]
    external_dependencies: List[str]


@dataclass
class DomainModule:
    """Represents a domain module that maps to a code module."""
    name: str
    bounded_context: BoundedContext
    dependencies: List[str]
    integration_points: List[str]
    suggested_file_structure: Dict[str, List[str]]


class DomainIdentifier:
    """Identifies domain modules and bounded contexts from PRD analysis."""
    
    def __init__(self):
        self.analysis_result: Optional[PRDAnalysisResult] = None
        self.existing_modules: Set[str] = set()
    
    def load_analysis(self, analysis_file: str) -> None:
        """Load PRD analysis result from JSON file."""
        with open(analysis_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Convert back to PRDAnalysisResult
        business_entities = [
            BusinessEntity(
                name=entity['name'],
                description=entity['description'],
                attributes=entity['attributes'],
                relationships=entity['relationships']
            )
            for entity in data['business_entities']
        ]
        
        self.analysis_result = PRDAnalysisResult(
            project_overview=data['project_overview'],
            target_users=data['target_users'],
            core_features=data['core_features'],
            business_entities=business_entities,
            user_stories=[],  # Simplified for now
            functional_requirements=data['functional_requirements'],
            non_functional_requirements=data['non_functional_requirements'],
            technical_constraints=data['technical_constraints']
        )
    
    def scan_existing_modules(self, modules_dir: str = "modules") -> None:
        """Scan existing modules in the project."""
        modules_path = Path(modules_dir)
        if modules_path.exists():
            self.existing_modules = {
                item.name for item in modules_path.iterdir() 
                if item.is_dir() and not item.name.startswith('.')
            }
    
    def identify_bounded_contexts(self) -> List[BoundedContext]:
        """Identify bounded contexts from the analysis."""
        if not self.analysis_result:
            raise ValueError("No analysis result loaded")
        
        contexts = []
        
        # Group features and entities by domain responsibility
        feature_groups = self._group_features_by_domain()
        
        for domain_name, features in feature_groups.items():
            # Find related entities
            related_entities = self._find_related_entities(domain_name, features)
            
            # Create domain entities
            domain_entities = []
            for entity in related_entities:
                domain_entities.append(DomainEntity(
                    name=entity.name,
                    description=entity.description,
                    attributes=entity.attributes,
                    business_methods=self._suggest_business_methods(entity),
                    invariants=self._suggest_invariants(entity)
                ))
            
            # Create repositories
            repositories = [
                Repository(
                    name=f"{entity.name}Repository",
                    entity_name=entity.name,
                    methods=self._suggest_repository_methods(entity.name)
                )
                for entity in domain_entities
            ]
            
            # Create bounded context
            context = BoundedContext(
                name=domain_name,
                description=self._generate_context_description(domain_name, features),
                core_responsibility=self._identify_core_responsibility(features),
                entities=domain_entities,
                value_objects=self._suggest_value_objects(domain_entities),
                repositories=repositories,
                use_cases=features,
                external_dependencies=self._identify_external_dependencies(features)
            )
            
            contexts.append(context)
        
        return contexts
    
    def _group_features_by_domain(self) -> Dict[str, List[str]]:
        """Group features by domain responsibility."""
        feature_groups = {}
        
        # Define domain keywords for grouping
        domain_keywords = {
            'server_catalog': ['服务器', 'server', '目录', 'catalog', '搜索', 'search', '发现', 'discovery'],
            'server_submission': ['提交', 'submission', '管理', 'management', '审核', 'review'],
            'user_identity': ['用户', 'user', '身份', 'identity', '认证', 'authentication'],
            'community_feedback': ['评论', 'comment', '评分', 'rating', '反馈', 'feedback'],
            'administration': ['管理员', 'admin', '监控', 'monitoring', '配置', 'configuration']
        }
        
        for feature in self.analysis_result.core_features:
            feature_lower = feature.lower()
            assigned = False
            
            for domain, keywords in domain_keywords.items():
                if any(keyword in feature_lower for keyword in keywords):
                    if domain not in feature_groups:
                        feature_groups[domain] = []
                    feature_groups[domain].append(feature)
                    assigned = True
                    break
            
            if not assigned:
                if 'general' not in feature_groups:
                    feature_groups['general'] = []
                feature_groups['general'].append(feature)
        
        return feature_groups
    
    def _find_related_entities(self, domain_name: str, features: List[str]) -> List[BusinessEntity]:
        """Find entities related to a specific domain."""
        related_entities = []
        
        for entity in self.analysis_result.business_entities:
            entity_name_lower = entity.name.lower()
            domain_lower = domain_name.lower()
            
            # Check if entity name contains domain keywords
            if any(keyword in entity_name_lower for keyword in domain_lower.split('_')):
                related_entities.append(entity)
                continue
            
            # Check if entity description relates to domain features
            for feature in features:
                if any(word in entity.description.lower() for word in feature.lower().split()):
                    related_entities.append(entity)
                    break
        
        return related_entities
    
    def _suggest_business_methods(self, entity: BusinessEntity) -> List[str]:
        """Suggest business methods for an entity."""
        methods = []
        entity_name = entity.name.lower()
        
        # Common business method patterns
        if 'server' in entity_name:
            methods.extend(['validate', 'publish', 'archive', 'update_metadata'])
        elif 'user' in entity_name:
            methods.extend(['activate', 'deactivate', 'update_profile'])
        elif 'review' in entity_name or 'feedback' in entity_name:
            methods.extend(['approve', 'reject', 'moderate'])
        
        return methods
    
    def _suggest_invariants(self, entity: BusinessEntity) -> List[str]:
        """Suggest business invariants for an entity."""
        invariants = []
        entity_name = entity.name.lower()
        
        if 'server' in entity_name:
            invariants.append("Server name must be unique")
            invariants.append("Server must have at least one tool definition")
        elif 'user' in entity_name:
            invariants.append("Email must be unique")
            invariants.append("Username must be unique")
        
        return invariants
    
    def _suggest_repository_methods(self, entity_name: str) -> List[str]:
        """Suggest repository methods for an entity."""
        base_methods = [
            f"find_by_id(id: UUID) -> Optional[{entity_name}]",
            f"save({entity_name.lower()}: {entity_name}) -> {entity_name}",
            f"delete(id: UUID) -> bool",
            f"find_all() -> List[{entity_name}]"
        ]
        
        # Add specific methods based on entity type
        entity_lower = entity_name.lower()
        if 'server' in entity_lower:
            base_methods.extend([
                f"find_by_category(category: str) -> List[{entity_name}]",
                f"search(query: str) -> List[{entity_name}]"
            ])
        elif 'user' in entity_lower:
            base_methods.extend([
                f"find_by_email(email: str) -> Optional[{entity_name}]",
                f"find_by_username(username: str) -> Optional[{entity_name}]"
            ])
        
        return base_methods
    
    def _suggest_value_objects(self, entities: List[DomainEntity]) -> List[ValueObject]:
        """Suggest value objects for the domain."""
        value_objects = []
        
        # Common value objects
        value_objects.append(ValueObject(
            name="Email",
            description="Email address value object",
            properties=["value", "domain"]
        ))
        
        value_objects.append(ValueObject(
            name="URL",
            description="URL value object",
            properties=["value", "scheme", "host"]
        ))
        
        return value_objects
    
    def _generate_context_description(self, domain_name: str, features: List[str]) -> str:
        """Generate description for bounded context."""
        descriptions = {
            'server_catalog': "Manages MCP server discovery, search, and public catalog functionality",
            'server_submission': "Handles server submission, review, and lifecycle management",
            'user_identity': "Manages user registration, authentication, and profile management",
            'community_feedback': "Handles user feedback, reviews, and community interactions",
            'administration': "Provides platform administration and monitoring capabilities"
        }
        
        return descriptions.get(domain_name, f"Manages {domain_name.replace('_', ' ')} functionality")
    
    def _identify_core_responsibility(self, features: List[str]) -> str:
        """Identify the core responsibility of a domain."""
        if not features:
            return "General functionality"
        
        # Extract key verbs and nouns from features
        key_words = []
        for feature in features:
            words = feature.lower().split()
            key_words.extend([word for word in words if len(word) > 3])
        
        # Find most common words
        word_count = {}
        for word in key_words:
            word_count[word] = word_count.get(word, 0) + 1
        
        if word_count:
            most_common = max(word_count, key=word_count.get)
            return f"Primary responsibility: {most_common}-related operations"
        
        return "General domain operations"
    
    def _identify_external_dependencies(self, features: List[str]) -> List[str]:
        """Identify external dependencies for a domain."""
        dependencies = []
        
        for feature in features:
            feature_lower = feature.lower()
            if 'github' in feature_lower:
                dependencies.append("GitHub API")
            elif 'oauth' in feature_lower:
                dependencies.append("OAuth providers")
            elif 'email' in feature_lower:
                dependencies.append("Email service")
            elif 'search' in feature_lower:
                dependencies.append("Search engine")
        
        return list(set(dependencies))
    
    def create_domain_modules(self, contexts: List[BoundedContext]) -> List[DomainModule]:
        """Create domain modules from bounded contexts."""
        modules = []
        
        for context in contexts:
            # Suggest module name
            module_name = context.name.lower()
            
            # Check for conflicts with existing modules
            if module_name in self.existing_modules:
                module_name = f"{module_name}_new"
            
            # Create file structure
            file_structure = {
                "domain": [
                    f"{entity.name.lower()}_models.py" for entity in context.entities
                ] + [
                    f"{repo.entity_name.lower()}_repositories.py" for repo in context.repositories
                ],
                "application": [
                    f"{context.name.lower()}_service.py"
                ],
                "infrastructure": [
                    f"{entity.name.lower()}_orm.py" for entity in context.entities
                ] + [
                    f"{repo.entity_name.lower()}_repositories.py" for repo in context.repositories
                ],
                "interfaces": [
                    f"{context.name.lower()}_api.py",
                    f"{context.name.lower()}_schemas.py"
                ]
            }
            
            # Identify dependencies
            dependencies = []
            if context.external_dependencies:
                dependencies.extend(context.external_dependencies)
            
            # Check for dependencies on existing modules
            for existing_module in self.existing_modules:
                if existing_module in str(context.use_cases).lower():
                    dependencies.append(f"modules.{existing_module}")
            
            module = DomainModule(
                name=module_name,
                bounded_context=context,
                dependencies=dependencies,
                integration_points=self._identify_integration_points(context),
                suggested_file_structure=file_structure
            )
            
            modules.append(module)
        
        return modules
    
    def _identify_integration_points(self, context: BoundedContext) -> List[str]:
        """Identify integration points with other modules."""
        integration_points = []
        
        # Check for common integration patterns
        if 'user' in context.name.lower():
            integration_points.append("Authentication module")
        elif 'server' in context.name.lower():
            integration_points.append("User module for ownership")
        elif 'feedback' in context.name.lower():
            integration_points.append("User module for reviewers")
            integration_points.append("Server module for reviewed items")
        
        return integration_points
    
    def export_domain_analysis(self, modules: List[DomainModule], output_path: str) -> None:
        """Export domain analysis to JSON file."""
        output_data = {
            "modules": [
                {
                    "name": module.name,
                    "bounded_context": {
                        "name": module.bounded_context.name,
                        "description": module.bounded_context.description,
                        "core_responsibility": module.bounded_context.core_responsibility,
                        "entities": [
                            {
                                "name": entity.name,
                                "description": entity.description,
                                "attributes": entity.attributes,
                                "business_methods": entity.business_methods,
                                "invariants": entity.invariants
                            }
                            for entity in module.bounded_context.entities
                        ],
                        "repositories": [
                            {
                                "name": repo.name,
                                "entity_name": repo.entity_name,
                                "methods": repo.methods
                            }
                            for repo in module.bounded_context.repositories
                        ],
                        "use_cases": module.bounded_context.use_cases,
                        "external_dependencies": module.bounded_context.external_dependencies
                    },
                    "dependencies": module.dependencies,
                    "integration_points": module.integration_points,
                    "suggested_file_structure": module.suggested_file_structure
                }
                for module in modules
            ]
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)


def main():
    """Main function for command line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Identify domain modules from PRD analysis')
    parser.add_argument('analysis_file', help='Path to PRD analysis JSON file')
    parser.add_argument('-o', '--output', help='Output JSON file path', 
                       default='domain_analysis.json')
    parser.add_argument('-m', '--modules-dir', help='Existing modules directory', 
                       default='modules')
    
    args = parser.parse_args()
    
    identifier = DomainIdentifier()
    identifier.load_analysis(args.analysis_file)
    identifier.scan_existing_modules(args.modules_dir)
    
    contexts = identifier.identify_bounded_contexts()
    modules = identifier.create_domain_modules(contexts)
    identifier.export_domain_analysis(modules, args.output)
    
    print(f"Domain analysis completed. Results saved to {args.output}")
    print(f"Identified {len(modules)} domain modules:")
    for module in modules:
        print(f"  - {module.name}: {module.bounded_context.description}")


if __name__ == "__main__":
    main()
