```markdown
# AI 开发任务提示词

生成时间: 2023-11-15 14:30:00

## 任务描述

### 开发任务
开发平台管理模块(platform_management)，主要实现内容审核和平台监控功能：
1. 内容审核功能：管理员可以查看内容列表并修改内容状态
2. 平台监控功能：系统操作员可以查看平台健康指标

### 预期交付成果
1. 完整的platform_management模块，包含DDD四层架构实现
2. 2个API端点实现：
   - GET /api/v1/platform/content
   - PATCH /api/v1/platform/content/{content_id}/status
3. 内容审核数据模型实现
4. 单元测试和集成测试代码
5. 完整的API文档和中文代码注释

### 成功标准
1. 所有功能通过验收测试
2. 代码符合DDD架构规范
3. 测试覆盖率≥80%
4. API文档完整准确

## 项目上下文

### 架构风格
- 领域驱动设计 (DDD) 四层架构
- 整洁架构原则
- REST API with FastAPI

### 技术栈
- Python 3.10+
- FastAPI
- SQLAlchemy 2.0
- Pydantic v2
- Alembic (数据库迁移)
- Pytest (测试框架)
- PostgreSQL (数据库)

### 编码标准
- 严格遵循 PEP 8 规范
- 强制类型提示 (Type Hinting)
- 中文注释和文档字符串
- 120 字符行长度限制
- Black 代码格式化

### 现有模块
- oauth_provider: OAuth2认证提供者
- auth: 认证授权模块
- user: 用户管理模块

## 架构约束 (必须严格遵循)

### DDD 四层架构
```
modules/platform_management/
├── __init__.py
├── interfaces/          # 接口层
│   ├── platform_management_api.py
│   └── schemas.py
├── application/         # 应用层
│   ├── services.py
│   └── dtos.py
├── domain/             # 领域层
│   ├── models.py
│   └── repositories.py
└── infrastructure/     # 基础设施层
    ├── repositories.py
    └── orm.py
```

### 依赖方向规则
- interfaces → application → domain ← infrastructure
- 严禁循环依赖
- Domain 层保持纯粹，不依赖任何外部框架

### 模块边界
- 模块间通信必须通过 Application 层服务接口
- 严禁直接访问其他模块的 Domain 或 Infrastructure 层
- 通用代码放置在 common/ 目录下

### 数据建模约束
- 所有实体 ID 字段使用 UUID 类型
- SQLAlchemy 2.0 风格模型
- Pydantic v2 用于数据验证

## 实现指南

### 开发步骤 (Domain First 原则)
1. **领域层设计** (modules/platform_management/domain/)
   - 在 models.py 中创建ContentModeration领域实体
   - 在 repositories.py 中定义ContentModerationRepository接口

2. **应用层实现** (modules/platform_management/application/)
   - 在 services.py 中创建PlatformManagementService
   - 实现内容审核和平台监控的业务逻辑

3. **基础设施层实现** (modules/platform_management/infrastructure/)
   - 在 orm.py 中创建ContentModerationORM模型
   - 在 repositories.py 中实现ContentModerationRepositoryImpl

4. **接口层实现** (modules/platform_management/interfaces/)
   - 在 schemas.py 中创建请求/响应模型
   - 在 platform_management_api.py 中实现FastAPI路由

5. **集成到主应用**
   - 在 main.py 中注册路由
   - 配置依赖注入

### 代码组织原则
- 业务逻辑集中在 Domain 层
- API 相关代码放在 interfaces 层
- 数据库操作在 infrastructure 层
- 跨领域关注点在 application 层

## 质量要求

### 代码质量标准
- 单元测试覆盖率 > 80%
- 所有公共方法必须有类型提示
- 中文注释和文档字符串
- 通过 CI 代码质量检查

### 测试要求
- Domain 层：纯单元测试，无外部依赖
- Application 层：Mock 仓库和外部依赖
- Infrastructure 层：集成测试，使用测试数据库
- interfaces 层：API 端到端测试

### 文档要求
- 所有 API 端点包含完整的 OpenAPI 文档
- 业务逻辑方法包含中文文档字符串
- README 文档更新

## 开发需求详情

### 领域模型设计
```python
# modules/platform_management/domain/models.py
from uuid import UUID
from enum import Enum

class ContentStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"

class ContentModeration:
    def __init__(
        self,
        id: UUID,
        content_id: UUID,
        status: ContentStatus,
        reason: str | None = None
    ):
        self.id = id
        self.content_id = content_id
        self.status = status
        self.reason = reason
```

### API端点实现
```python
# modules/platform_management/interfaces/platform_management_api.py
from fastapi import APIRouter, Depends
from .schemas import ContentStatusUpdateRequest

router = APIRouter(prefix="/platform", tags=["Platform Management"])

@router.get("/content")
async def get_content_list():
    """获取待审核内容列表"""
    pass

@router.patch("/content/{content_id}/status")
async def update_content_status(
    content_id: UUID, 
    request: ContentStatusUpdateRequest
):
    """更新内容审核状态"""
    pass
```

### 测试要求
1. 测试ContentModeration领域实体的状态转换逻辑
2. 测试PlatformManagementService的业务逻辑
3. 测试ContentModerationRepositoryImpl的数据库操作
4. 测试API端点的请求和响应

### 验收标准
1. 功能验收标准
   - 管理员可以查看待审核内容列表
   - 管理员可以修改内容审核状态
   - 系统操作员可以查看平台健康指标

2. 技术验收标准
   - 严格遵循DDD四层架构
   - 代码符合PEP8规范
   - 类型提示覆盖率100%

3. 质量验收标准
   - 测试覆盖率≥80%
   - API文档完整准确
   - 代码审查无严重问题
```