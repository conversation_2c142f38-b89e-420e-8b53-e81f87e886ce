#!/usr/bin/env python3
"""
Basic Usage Example for AI Development Agents

This example demonstrates how to use the AI Development Agents toolkit
for analyzing a PRD document and generating development requirements.
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from core.orchestrator import AIDevWorkflowOrchestrator
from core.base_agent import WorkflowContext


def main():
    """Basic usage example."""
    print("=== AI Development Agents - Basic Usage Example ===\n")
    
    # Sample PRD content
    sample_prd = """
# E-commerce Platform

## 项目概述
构建一个现代化的电商平台，支持商品管理、订单处理、用户管理等核心功能。

## 核心功能
1. 用户注册和登录
2. 商品浏览和搜索
3. 购物车管理
4. 订单处理
5. 支付集成

## 业务规则
- 用户必须注册才能下单
- 库存不足时不能下单
- 订单支付后才能发货
"""
    
    try:
        # Initialize orchestrator
        print("1. 初始化 AI 开发工作流编排器...")
        orchestrator = AIDevWorkflowOrchestrator(
            verbose=True,
            config_path="../config.yaml"
        )
        
        # Create workflow context
        print("2. 创建工作流上下文...")
        context = WorkflowContext(
            project_root="./demo_project",
            project_rules={},
            existing_modules=["auth", "user"],
            tech_stack=["FastAPI", "SQLAlchemy", "Pydantic"],
            architecture_style="DDD"
        )
        
        # Run the complete workflow
        print("3. 执行完整的 AI 开发工作流...")
        result = orchestrator.run_complete_workflow(
            prd_content=sample_prd,
            context=context
        )
        
        # Display results
        if result.success:
            print("✅ 工作流执行成功!")
            print(f"   执行时间: {result.execution_time:.2f}秒")
            
            # Show generated outputs
            data = result.data
            if "business_analysis" in data:
                entities = data["business_analysis"].get("core_entities", [])
                print(f"   识别的业务实体: {len(entities)}个")
                
            if "domain_model" in data:
                aggregates = data["domain_model"].get("aggregates", [])
                print(f"   生成的聚合: {len(aggregates)}个")
                
            if "technical_requirements" in data:
                modules = data["technical_requirements"].get("module_structure", [])
                print(f"   技术模块: {len(modules)}个")
                
            if "ai_prompts" in data:
                prompts = data["ai_prompts"].get("prompts", [])
                print(f"   AI 提示词: {len(prompts)}个")
                
        else:
            print("❌ 工作流执行失败:")
            for error in result.errors:
                print(f"   - {error}")
                
    except Exception as e:
        print(f"❌ 示例执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
