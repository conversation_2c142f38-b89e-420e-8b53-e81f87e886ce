# OpenRouter LLM 配置指南

本文档说明如何配置 OpenRouter LLM 来运行 AI 开发代理系统。

## 1. 获取 OpenRouter API Key

1. 访问 [OpenRouter](https://openrouter.ai/)
2. 注册账户并登录
3. 在控制台中创建 API Key
4. 复制您的 API Key

## 2. 安装依赖

确保安装了必要的 Python 包：

```bash
# 安装 LangChain OpenAI 集成
pip install langchain-openai

# 或者使用 uv（推荐）
uv add langchain-openai
```

## 3. 配置方式

### 方式一：环境变量（推荐）

```bash
# 设置 API Key（必需）
export OPENROUTER_API_KEY="your-api-key-here"

# 设置模型（可选，默认：anthropic/claude-3-sonnet）
export OPENROUTER_MODEL="anthropic/claude-3-sonnet"

# 设置 API 端点（可选，默认：https://openrouter.ai/api/v1）
export OPENROUTER_BASE_URL="https://openrouter.ai/api/v1"

# 设置温度参数（可选，默认：0.1）
export OPENROUTER_TEMPERATURE="0.1"

# 设置最大 token 数（可选，默认：4000）
export OPENROUTER_MAX_TOKENS="4000"
```

### 方式二：直接修改代码

在 `tools/simple_test.py` 中找到配置部分并修改：

```python
config = {
    "api_key": "your-api-key-here",  # 直接设置您的 API Key
    "model": "anthropic/claude-3-sonnet",
    "base_url": "https://openrouter.ai/api/v1",
    "temperature": 0.1,
    "max_tokens": 2000
}
```

## 4. 推荐模型

根据不同需求选择合适的模型：

### 高质量分析（推荐）
- `anthropic/claude-3-sonnet` - 平衡性能和成本
- `anthropic/claude-3-opus` - 最高质量，成本较高
- `openai/gpt-4-turbo` - OpenAI 最新模型

### 成本优化
- `anthropic/claude-3-haiku` - 快速且经济
- `openai/gpt-3.5-turbo` - 经济选择

### 开源选择
- `meta-llama/llama-3-70b-instruct` - 高质量开源模型
- `mistralai/mixtral-8x7b-instruct` - 平衡选择

## 5. 运行测试

### 简单测试
```bash
# 运行简化的测试脚本
python tools/simple_test.py
```

### 完整测试
```bash
# 运行完整的测试套件
python tools/test_agents.py
```

### CLI 测试
```bash
# 使用 CLI 进行业务分析
python -m tools.ai_dev_agents.cli business-analysis design/mcp-market-prd.txt
```

## 6. 预期输出

成功运行后，您应该看到：

```
🚀 Simple AI Agent Test with OpenRouter
==================================================

✅ LangChain available

🔧 Configuration:
   - API Key: ✅ Set
   - Model: anthropic/claude-3-sonnet
   - Base URL: https://openrouter.ai/api/v1

🤖 Creating OpenRouter LLM...
✅ LLM created successfully

📊 Testing Business Analyzer Agent...
✅ Business Analyzer Agent created
✅ Workflow context created

🔍 Running business analysis...
✅ Business analysis completed successfully!

📋 Results Summary:
   - Project: MCP Server Market Platform
   - Core Entities: 3
   - Functional Requirements: 5
   - User Stories: 8
   - Business Rules: 12

💾 Detailed results saved to: business_analysis_result.json

🎉 Test completed successfully!
```

## 7. 故障排除

### 常见问题

1. **ImportError: No module named 'langchain_openai'**
   ```bash
   pip install langchain-openai
   ```

2. **API Key 错误**
   - 检查 API Key 是否正确设置
   - 确认 API Key 有效且有足够余额

3. **网络连接问题**
   - 检查网络连接
   - 确认防火墙设置

4. **模型不可用**
   - 尝试其他模型
   - 检查 OpenRouter 模型可用性

### 调试模式

在代码中启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 8. 成本控制

- 使用 `max_tokens` 参数限制输出长度
- 选择合适的模型平衡质量和成本
- 监控 OpenRouter 控制台中的使用情况

## 9. 下一步

测试成功后，您可以：

1. 运行完整的工作流程
2. 测试其他代理（Domain Modeler, Requirements Generator 等）
3. 使用 CLI 工具处理实际的 PRD 文档
4. 集成到您的开发工作流程中
