"""
AI Development Prompt Builder

This module builds comprehensive AI prompts for code generation tasks.
"""

import json
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime


@dataclass
class ProjectContext:
    """Project context information for AI prompts."""
    architecture_style: str
    tech_stack: List[str]
    coding_standards: str
    existing_modules: List[str]
    project_rules: str


@dataclass
class AIPrompt:
    """Complete AI development prompt."""
    task_description: str
    project_context: ProjectContext
    development_requirements: str
    architectural_constraints: List[str]
    implementation_guidelines: List[str]
    quality_requirements: List[str]
    acceptance_criteria: List[str]
    reference_examples: List[str]


class PromptBuilder:
    """Builds AI development prompts with project context."""
    
    def __init__(self):
        self.project_context = self._load_project_context()
    
    def _load_project_context(self) -> ProjectContext:
        """Load project context from various sources."""
        # Load project rules
        rules_content = ""
        rules_path = Path(".roo/rules/rules.md")
        if rules_path.exists():
            with open(rules_path, 'r', encoding='utf-8') as f:
                rules_content = f.read()
        
        # Scan existing modules
        existing_modules = []
        modules_path = Path("modules")
        if modules_path.exists():
            existing_modules = [
                item.name for item in modules_path.iterdir() 
                if item.is_dir() and not item.name.startswith('.')
            ]
        
        # Load tech stack from pyproject.toml
        tech_stack = ["FastAPI", "SQLAlchemy", "Pydantic", "PostgreSQL", "Alembic"]
        
        return ProjectContext(
            architecture_style="Domain-Driven Design (DDD) with 4-layer architecture",
            tech_stack=tech_stack,
            coding_standards="PEP 8, Type hints required, English documentation",
            existing_modules=existing_modules,
            project_rules=rules_content
        )
    
    def build_prompt(self, requirements_file: str, task_description: str = "") -> AIPrompt:
        """Build comprehensive AI prompt from requirements file."""
        # Load requirements
        requirements_content = ""
        if Path(requirements_file).exists():
            with open(requirements_file, 'r', encoding='utf-8') as f:
                requirements_content = f.read()
        
        # Extract module name from requirements
        module_name = self._extract_module_name(requirements_content)
        
        if not task_description:
            task_description = f"Implement the {module_name} module according to the provided requirements"
        
        return AIPrompt(
            task_description=task_description,
            project_context=self.project_context,
            development_requirements=requirements_content,
            architectural_constraints=self._generate_architectural_constraints(),
            implementation_guidelines=self._generate_implementation_guidelines(module_name),
            quality_requirements=self._generate_quality_requirements(),
            acceptance_criteria=self._extract_acceptance_criteria(requirements_content),
            reference_examples=self._generate_reference_examples()
        )
    
    def _extract_module_name(self, requirements_content: str) -> str:
        """Extract module name from requirements content."""
        lines = requirements_content.split('\n')
        for line in lines:
            if line.startswith('# ') and 'Module Development Requirements' in line:
                return line.split()[1].lower()
        return "unknown_module"
    
    def _generate_architectural_constraints(self) -> List[str]:
        """Generate architectural constraints from project rules."""
        constraints = [
            "STRICT DDD 4-layer architecture: Domain → Application → Infrastructure → Interface",
            "Module isolation: New functionality must be in modules/{module_name}/ directory",
            "Dependency direction: Interfaces → Application → Domain, Infrastructure implements Domain",
            "Domain layer purity: NO imports of fastapi, sqlalchemy, or external frameworks in Domain layer",
            "UUID primary keys: ALL entities must use UUID as primary key type",
            "File naming: Use business subdomain prefixes (e.g., user_models.py, not models.py)",
            "Cross-module communication: Only through Application layer service interfaces",
            "Database migrations: Use Alembic, NO automatic table creation"
        ]
        
        return constraints
    
    def _generate_implementation_guidelines(self, module_name: str) -> List[str]:
        """Generate implementation guidelines for the module."""
        guidelines = [
            f"Create module structure: modules/{module_name}/{{domain,application,infrastructure,interfaces}}/",
            "Domain First: Start with entities and repository interfaces",
            "Repository pattern: Abstract interfaces in Domain, implementations in Infrastructure",
            "Service layer: Application services orchestrate domain objects and repositories",
            "API layer: FastAPI routers with comprehensive OpenAPI documentation",
            "Type hints: ALL functions and variables must have explicit type annotations",
            "Error handling: Proper exception handling with meaningful error messages",
            "Validation: Use Pydantic for data validation and serialization",
            "Testing: Write tests for each layer (unit tests for domain, integration tests for API)",
            "Documentation: All docstrings and comments in English"
        ]
        
        return guidelines
    
    def _generate_quality_requirements(self) -> List[str]:
        """Generate quality requirements."""
        return [
            "Code must pass all type checking (mypy)",
            "Code must follow PEP 8 formatting standards",
            "All functions must have comprehensive docstrings",
            "Test coverage must be above 80%",
            "All API endpoints must have OpenAPI documentation",
            "Database schema must follow naming conventions",
            "Error responses must be consistent and informative",
            "Security: Proper authentication and authorization",
            "Performance: Efficient database queries and proper indexing"
        ]
    
    def _extract_acceptance_criteria(self, requirements_content: str) -> List[str]:
        """Extract acceptance criteria from requirements."""
        criteria = []
        lines = requirements_content.split('\n')
        in_criteria_section = False
        
        for line in lines:
            if '## Acceptance Criteria' in line:
                in_criteria_section = True
                continue
            elif line.startswith('##') and in_criteria_section:
                break
            elif in_criteria_section and line.strip().startswith('- [ ]'):
                criteria.append(line.strip()[5:])  # Remove '- [ ] '
        
        return criteria
    
    def _generate_reference_examples(self) -> List[str]:
        """Generate reference examples from existing modules."""
        examples = []
        
        for module in self.project_context.existing_modules:
            examples.append(f"Reference existing {module} module structure and patterns")
        
        examples.extend([
            "Follow the same patterns as user and auth modules for consistency",
            "Use similar dependency injection patterns as existing modules",
            "Follow the same testing patterns as established in the project"
        ])
        
        return examples
    
    def export_prompt(self, prompt: AIPrompt, output_path: str) -> None:
        """Export AI prompt to markdown file."""
        content = f"""# AI Development Task Prompt

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Task Description

{prompt.task_description}

## Project Context

### Architecture Style
{prompt.project_context.architecture_style}

### Technology Stack
{chr(10).join([f"- {tech}" for tech in prompt.project_context.tech_stack])}

### Coding Standards
{prompt.project_context.coding_standards}

### Existing Modules
{chr(10).join([f"- modules/{module}/" for module in prompt.project_context.existing_modules])}

## Architectural Constraints (MUST FOLLOW)

{chr(10).join([f"- {constraint}" for constraint in prompt.architectural_constraints])}

## Implementation Guidelines

{chr(10).join([f"- {guideline}" for guideline in prompt.implementation_guidelines])}

## Quality Requirements

{chr(10).join([f"- {requirement}" for requirement in prompt.quality_requirements])}

## Acceptance Criteria

{chr(10).join([f"- [ ] {criteria}" for criteria in prompt.acceptance_criteria])}

## Reference Examples

{chr(10).join([f"- {example}" for example in prompt.reference_examples])}

## Development Requirements

{prompt.development_requirements}

## Project Rules and Standards

```markdown
{prompt.project_context.project_rules}
```

---

## Instructions for AI Agent

You are tasked with implementing a new module for this FastAPI + DDD project. Please:

1. **Analyze the requirements** thoroughly and understand the business domain
2. **Plan the implementation** following the DDD 4-layer architecture
3. **Create the module structure** in the correct directory hierarchy
4. **Implement each layer** in the proper order: Domain → Infrastructure → Application → Interface
5. **Write comprehensive tests** for each component
6. **Ensure all constraints** and quality requirements are met
7. **Update documentation** as needed

Remember:
- Follow the existing project patterns and conventions
- Maintain consistency with existing modules
- Ask for clarification if any requirements are unclear
- Test your implementation thoroughly before considering it complete

Start with a detailed implementation plan, then proceed with the actual code generation.
"""
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def create_development_context(self, requirements_file: str, module_name: str) -> str:
        """Create a comprehensive development context for AI agents."""
        prompt = self.build_prompt(requirements_file, 
                                 f"Implement the {module_name} module with full DDD architecture")
        
        context_file = f"{module_name}_ai_prompt.md"
        self.export_prompt(prompt, context_file)
        
        return context_file


def main():
    """Main function for command line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Build AI development prompt from requirements')
    parser.add_argument('requirements_file', help='Path to development requirements file')
    parser.add_argument('-t', '--task', help='Task description', 
                       default="Implement the module according to requirements")
    parser.add_argument('-o', '--output', help='Output prompt file path')
    
    args = parser.parse_args()
    
    if not args.output:
        module_name = Path(args.requirements_file).stem.replace('_requirements', '')
        args.output = f"{module_name}_ai_prompt.md"
    
    builder = PromptBuilder()
    prompt = builder.build_prompt(args.requirements_file, args.task)
    builder.export_prompt(prompt, args.output)
    
    print(f"AI development prompt created: {args.output}")
    print(f"Use this prompt with your AI agent to implement the module")


if __name__ == "__main__":
    main()
