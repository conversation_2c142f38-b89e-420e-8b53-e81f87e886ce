```markdown
# AI Development Task Prompt

Generated on: 2023-11-15

## Task Description

### Development Task
Implement the `CommunityInteraction` module to manage server reviews and user feedback with the following capabilities:
1. Allow registered users to submit server reviews (US-001)
2. Allow any user to view server reviews (US-002)

### Expected Deliverables
1. Complete FastAPI implementation with 2 endpoints:
   - POST /api/v1/reviews/
   - GET /api/v1/reviews/server/{server_id}
2. SQLAlchemy data model for reviews
3. Pydantic schemas for request/response validation
4. Alembic migration script
5. Unit tests with >80% coverage
6. API documentation in OpenAPI format

### Success Criteria
1. All user stories implemented and tested
2. Code passes all quality gates (linting, type checking, tests)
3. API endpoints properly secured (auth for POST endpoint)
4. Documentation complete and accurate

## Project Context

### Architecture Style
- Domain-Driven Design (4-layer architecture)
- Clean Architecture principles
- REST API with FastAPI

### Technology Stack
- Core: Python 3.10+
- Web: FastAPI
- ORM: SQLAlchemy 2.0+
- Validation: Pydantic v2
- Migrations: Alembic
- Testing: Pytest + HTTPX
- Auth: JWT (integrated with existing auth module)

### Coding Standards
- PEP 8 compliance
- Type hints for all functions/methods
- Google-style docstrings
- English-only comments/doc
- 120 character line length
- Black code formatting

### Existing Modules
1. `user` module (reference for DDD implementation)
   - Structure: domain/, application/, infrastructure/, interfaces/
   - Example patterns: BaseRepository, DomainService
2. `auth` module (reference for JWT integration)
   - Auth dependency injection
   - Permission checking
3. `oauth_provider` module (reference for external integrations)

## Architectural Constraints (MUST FOLLOW)

1. **DDD Layer Separation**:
   - Domain layer must not depend on any other layer
   - Application layer depends only on Domain
   - Infrastructure depends on Domain/Application
   - Interfaces depend on all lower layers

2. **Dependency Rules**:
   - No circular dependencies between modules
   - Infrastructure adapters must implement domain interfaces
   - Use dependency injection for all external services

3. **Module Boundaries**:
   - Reviews domain must be isolated from User/Server domains
   - Only allowed to depend on auth module for authentication
   - No direct database access from application layer

## Implementation Guidelines

### Development Sequence
1. Implement domain model (Review aggregate root, value objects)
2. Create repository interface in domain layer
3. Implement application services (review submission, retrieval)
4. Create infrastructure adapters (SQLAlchemy repository)
5. Implement API endpoints
6. Write migration script
7. Implement tests (unit, integration)

### Code Organization
```
community_interaction/
├── domain/
│   ├── models/
│   │   └── review.py
│   ├── repositories.py
│   └── services.py
├── application/
│   ├── services.py
│   └── dtos.py
├── infrastructure/
│   ├── repositories/
│   │   └── sqlalchemy_review_repo.py
│   └── config.py
├── interfaces/
│   ├── api/
│   │   ├── v1/
│   │   │   ├── endpoints.py
│   │   │   └── schemas.py
│   │   └── dependencies.py
│   └── events.py
└── tests/
    ├── unit/
    └── integration/
```

### Best Practices
1. Use UUID primary keys (like existing modules)
2. Implement proper error handling (domain exceptions)
3. Use async/await consistently
4. Validate all inputs with Pydantic
5. Follow repository pattern for data access
6. Implement proper transaction management

## Quality Requirements

### Code Quality
- 10/10 pylint score
- No mypy errors
- 100% type coverage
- No code duplication (SonarQube check)

### Testing
- >80% test coverage (measured by pytest-cov)
- Unit tests for all domain logic
- Integration tests for API endpoints
- Test all error scenarios

### Documentation
- Complete OpenAPI docs with examples
- Module-level docstrings
- All public methods documented
- README.md with setup instructions

## Acceptance Criteria

### Functional
1. POST endpoint:
   - Requires authentication
   - Validates review content (1-5 rating, text length)
   - Returns 201 on success
2. GET endpoint:
   - Returns paginated results
   - Filters by server_id
   - Returns 200 with empty array if no reviews

### Technical
1. Proper DDD layer separation
2. JWT auth integration
3. Async database access
4. Proper error responses (400, 401, 404, 500)

### Quality
1. All tests pass
2. Coverage report meets requirements
3. No critical SonarQube issues
4. Documentation complete

## Reference Examples

### Code Patterns
1. User module's BaseRepository implementation:
   ```python
   class BaseRepository(Generic[T]):
       async def get(self, id: UUID) -> Optional[T]: ...
       async def add(self, entity: T) -> None: ...
   ```

2. Auth module's dependency injection:
   ```python
   async def get_current_user(token: str = Depends(oauth2_scheme)) -> User:
       # JWT validation logic
   ```

### Architecture
1. Domain event implementation from oauth_provider:
   ```python
   class DomainEvent:
       def __init__(self, occurred_on: datetime = None):
           self.occurred_on = occurred_on or datetime.utcnow()
   ```

### Best Practices
1. Pydantic schema example from user module:
   ```python
   class UserCreateSchema(BaseModel):
       email: EmailStr
       password: constr(min_length=8, max_length=64)
   ```

## Development Requirements

### Detailed Requirements
1. Review model must include:
   - server_id (UUID)
   - user_id (UUID)
   - rating (int 1-5)
   - comment (str, max 500 chars)
   - created_at (datetime)

2. API Schemas:
   - ReviewCreateSchema (for POST)
   - ReviewResponseSchema (for GET)
   - ReviewListSchema (paginated response)

3. Repository must implement:
   - async add(review)
   - async list_for_server(server_id, limit, offset)

4. Tests must cover:
   - Invalid rating values
   - Empty comments
   - Unauthenticated access
   - Pagination logic

## Project Rules and Standards

1. **DDD Rules**:
   - No business logic in infrastructure/interface layers
   - Domain models must be pure (no dependencies)
   - Use value objects for domain concepts

2. **Code Standards**:
   - All imports sorted and grouped (stdlib, third-party, local)
   - No direct instantiation of infrastructure in domain
   - All exceptions must be domain-specific

3. **Security Standards**:
   - Never expose internal IDs in responses
   - Always validate permissions
   - Use existing auth middleware

4. **Performance**:
   - All database queries must be optimized
   - N+1 query prevention required
   - Pagination default limit of 20 items
```