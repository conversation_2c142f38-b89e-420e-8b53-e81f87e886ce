"""
Domain Modeler Agent

Intelligent agent for applying DDD methodology to create domain models.
"""

from typing import Any, Dict, List
from datetime import datetime

from .base_agent import BaseAgent, AgentResult, WorkflowContext


class DomainModelerAgent(BaseAgent):
    """Agent for creating domain models using DDD methodology."""
    
    def __init__(self, llm=None, verbose: bool = False):
        super().__init__("domain_modeler", llm, verbose)
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for domain modeling."""
        return """
你是一个领域驱动设计(DDD)专家，专门将业务需求转换为领域模型。你的任务是基于业务分析结果，应用DDD方法论创建清晰的领域模型。

你需要基于业务分析结果创建以下DDD模型：

1. **边界上下文** (bounded_contexts)
   - 识别不同的业务边界
   - 定义上下文的职责范围
   - 确定上下文间的关系

2. **聚合根** (aggregates)
   - 识别聚合边界
   - 定义聚合根实体
   - 确定聚合内的实体和值对象

3. **领域实体** (domain_entities)
   - 具有唯一标识的业务对象
   - 实体的属性和行为
   - 实体的生命周期

4. **值对象** (value_objects)
   - 不可变的业务概念
   - 值对象的属性
   - 值对象的验证规则

5. **领域服务** (domain_services)
   - 不属于特定实体的业务逻辑
   - 跨实体的业务操作
   - 领域规则的实现

6. **仓储接口** (repositories)
   - 数据访问抽象
   - 查询接口定义
   - 持久化操作

7. **领域事件** (domain_events)
   - 业务重要事件
   - 事件的触发条件
   - 事件的处理逻辑

请严格按照以下JSON格式输出领域模型：

```json
{
  "bounded_contexts": [
    {
      "name": "上下文名称",
      "description": "上下文描述",
      "responsibilities": ["职责1", "职责2"],
      "relationships": [
        {
          "target_context": "目标上下文",
          "relationship_type": "Customer-Supplier/Shared Kernel/Anti-Corruption Layer",
          "description": "关系描述"
        }
      ]
    }
  ],
  "aggregates": [
    {
      "name": "聚合名称",
      "bounded_context": "所属上下文",
      "aggregate_root": "聚合根实体名",
      "entities": ["实体1", "实体2"],
      "value_objects": ["值对象1", "值对象2"],
      "business_rules": ["业务规则1", "业务规则2"],
      "invariants": ["不变量1", "不变量2"]
    }
  ],
  "domain_entities": [
    {
      "name": "实体名称",
      "aggregate": "所属聚合",
      "description": "实体描述",
      "attributes": [
        {
          "name": "属性名",
          "type": "属性类型",
          "description": "属性描述",
          "required": true
        }
      ],
      "business_methods": [
        {
          "name": "方法名",
          "description": "方法描述",
          "parameters": ["参数1", "参数2"],
          "return_type": "返回类型"
        }
      ],
      "business_rules": ["规则1", "规则2"]
    }
  ],
  "value_objects": [
    {
      "name": "值对象名称",
      "description": "值对象描述",
      "attributes": [
        {
          "name": "属性名",
          "type": "属性类型",
          "description": "属性描述"
        }
      ],
      "validation_rules": ["验证规则1", "验证规则2"],
      "immutable": true
    }
  ],
  "domain_services": [
    {
      "name": "服务名称",
      "bounded_context": "所属上下文",
      "description": "服务描述",
      "methods": [
        {
          "name": "方法名",
          "description": "方法描述",
          "parameters": ["参数1", "参数2"],
          "return_type": "返回类型"
        }
      ],
      "dependencies": ["依赖的仓储或服务"]
    }
  ],
  "repositories": [
    {
      "name": "仓储名称",
      "aggregate": "管理的聚合",
      "description": "仓储描述",
      "methods": [
        {
          "name": "方法名",
          "description": "方法描述",
          "parameters": ["参数1", "参数2"],
          "return_type": "返回类型"
        }
      ]
    }
  ],
  "domain_events": [
    {
      "name": "事件名称",
      "description": "事件描述",
      "trigger_conditions": ["触发条件1", "触发条件2"],
      "event_data": [
        {
          "name": "数据字段",
          "type": "数据类型",
          "description": "字段描述"
        }
      ],
      "handlers": ["处理器1", "处理器2"]
    }
  ]
}
```

设计原则：
- 遵循DDD核心概念和模式
- 确保聚合边界清晰
- 保持领域模型的纯粹性
- 考虑实体的生命周期
- 识别重要的业务不变量
- 设计合理的仓储接口
"""
    
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Process business analysis and create domain models."""
        try:
            # Get business analysis results
            business_analysis = input_data
            if not business_analysis:
                return AgentResult(
                    success=False,
                    data={},
                    metadata={"agent_name": self.name},
                    errors=["No business analysis data provided"],
                    execution_time=0.0,
                    timestamp=datetime.now()
                )
            
            # Prepare input for LLM
            user_input = f"""
请基于以下业务分析结果，应用DDD方法论创建领域模型：

=== 业务分析结果 ===
{self._format_business_analysis(business_analysis)}

=== 项目上下文 ===
- 架构风格: {context.architecture_style}
- 技术栈: {', '.join(context.tech_stack)}
- 现有模块: {', '.join(context.existing_modules)}

=== 设计要求 ===
1. 识别清晰的边界上下文，避免过度分割
2. 设计合理的聚合边界，确保业务一致性
3. 定义富有行为的领域实体，不要贫血模型
4. 识别合适的值对象，提高模型表达力
5. 设计必要的领域服务，处理跨实体业务逻辑
6. 定义抽象的仓储接口，支持数据访问
7. 识别重要的领域事件，支持事件驱动架构

请确保输出的JSON格式正确且领域模型设计合理。
"""
            
            # Execute LLM call
            system_prompt = self.get_system_prompt()
            messages = self._create_messages(system_prompt, user_input)
            response = self._execute_with_retry(messages)
            
            # Parse response
            parsed_data = self._parse_json_response(response)
            
            # Validate required fields
            required_fields = [
                "bounded_contexts", "aggregates", "domain_entities",
                "value_objects", "domain_services", "repositories", "domain_events"
            ]
            errors = self._validate_required_fields(parsed_data, required_fields)
            
            if errors:
                return AgentResult(
                    success=False,
                    data=parsed_data,
                    metadata={"agent_name": self.name, "raw_response": response},
                    errors=errors,
                    execution_time=0.0,
                    timestamp=datetime.now()
                )
            
            # Post-process and validate domain model
            validated_data = self._validate_domain_model(parsed_data)
            
            return AgentResult(
                success=True,
                data=validated_data,
                metadata={
                    "agent_name": self.name,
                    "raw_response": response,
                    "bounded_contexts_count": len(validated_data.get("bounded_contexts", [])),
                    "aggregates_count": len(validated_data.get("aggregates", [])),
                    "entities_count": len(validated_data.get("domain_entities", [])),
                    "repositories_count": len(validated_data.get("repositories", []))
                },
                errors=[],
                execution_time=0.0,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                data={},
                metadata={"agent_name": self.name},
                errors=[f"Domain modeling failed: {str(e)}"],
                execution_time=0.0,
                timestamp=datetime.now()
            )
    
    def _format_business_analysis(self, analysis: Dict[str, Any]) -> str:
        """Format business analysis for LLM input."""
        formatted = []
        
        # Business overview
        if "business_overview" in analysis:
            overview = analysis["business_overview"]
            formatted.append("## 业务概览")
            formatted.append(f"项目名称: {overview.get('project_name', 'N/A')}")
            formatted.append(f"核心目标: {overview.get('core_purpose', 'N/A')}")
            formatted.append(f"目标用户: {', '.join(overview.get('target_users', []))}")
            formatted.append("")
        
        # Core entities
        if "core_entities" in analysis:
            formatted.append("## 核心业务实体")
            for entity in analysis["core_entities"]:
                formatted.append(f"- {entity.get('name', 'Unknown')}: {entity.get('description', '')}")
                if entity.get('key_attributes'):
                    formatted.append(f"  属性: {', '.join(entity['key_attributes'])}")
            formatted.append("")
        
        # Business rules
        if "business_rules" in analysis:
            formatted.append("## 业务规则")
            for rule in analysis["business_rules"]:
                formatted.append(f"- [{rule.get('category', 'General')}] {rule.get('rule', '')}")
            formatted.append("")
        
        # Functional requirements
        if "functional_requirements" in analysis:
            formatted.append("## 功能需求")
            for req in analysis["functional_requirements"]:
                formatted.append(f"- {req.get('id', 'Unknown')}: {req.get('title', '')}")
                formatted.append(f"  {req.get('description', '')}")
            formatted.append("")
        
        return "\n".join(formatted)
    
    def _validate_domain_model(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and enrich domain model."""
        validated = data.copy()
        
        # Add model metadata
        validated["model_metadata"] = {
            "creation_timestamp": datetime.now().isoformat(),
            "ddd_patterns_used": self._identify_ddd_patterns(data),
            "complexity_metrics": self._calculate_model_complexity(data)
        }
        
        # Validate aggregate consistency
        validated["validation_results"] = self._validate_aggregate_consistency(data)
        
        return validated
    
    def _identify_ddd_patterns(self, data: Dict[str, Any]) -> List[str]:
        """Identify DDD patterns used in the model."""
        patterns = []
        
        if data.get("bounded_contexts"):
            patterns.append("Bounded Context")
        if data.get("aggregates"):
            patterns.append("Aggregate")
        if data.get("domain_entities"):
            patterns.append("Entity")
        if data.get("value_objects"):
            patterns.append("Value Object")
        if data.get("domain_services"):
            patterns.append("Domain Service")
        if data.get("repositories"):
            patterns.append("Repository")
        if data.get("domain_events"):
            patterns.append("Domain Event")
        
        return patterns
    
    def _calculate_model_complexity(self, data: Dict[str, Any]) -> Dict[str, int]:
        """Calculate complexity metrics for the domain model."""
        return {
            "total_bounded_contexts": len(data.get("bounded_contexts", [])),
            "total_aggregates": len(data.get("aggregates", [])),
            "total_entities": len(data.get("domain_entities", [])),
            "total_value_objects": len(data.get("value_objects", [])),
            "total_services": len(data.get("domain_services", [])),
            "total_repositories": len(data.get("repositories", [])),
            "total_events": len(data.get("domain_events", []))
        }
    
    def _validate_aggregate_consistency(self, data: Dict[str, Any]) -> Dict[str, List[str]]:
        """Validate aggregate consistency and relationships."""
        issues = []
        warnings = []
        
        aggregates = data.get("aggregates", [])
        entities = data.get("domain_entities", [])
        repositories = data.get("repositories", [])
        
        # Check if each aggregate has a corresponding repository
        aggregate_names = {agg.get("name") for agg in aggregates}
        repository_aggregates = {repo.get("aggregate") for repo in repositories}
        
        missing_repos = aggregate_names - repository_aggregates
        if missing_repos:
            warnings.extend([f"Aggregate '{name}' has no corresponding repository" for name in missing_repos])
        
        # Check if entities belong to defined aggregates
        entity_aggregates = {entity.get("aggregate") for entity in entities}
        undefined_aggregates = entity_aggregates - aggregate_names
        if undefined_aggregates:
            issues.extend([f"Entity references undefined aggregate '{name}'" for name in undefined_aggregates])
        
        return {
            "issues": issues,
            "warnings": warnings
        }

    def _get_mock_response(self) -> str:
        """Get mock response for testing."""
        return '''
{
  "bounded_contexts": [
    {
      "name": "Server Management",
      "description": "Manages MCP server lifecycle and metadata",
      "responsibilities": ["Server registration", "Version management", "Metadata storage"],
      "ubiquitous_language": {
        "Server": "MCP server instance",
        "Version": "Server version release",
        "Metadata": "Server descriptive information"
      }
    },
    {
      "name": "User Management",
      "description": "Handles user accounts and authentication",
      "responsibilities": ["User registration", "Authentication", "Profile management"],
      "ubiquitous_language": {
        "User": "Platform user account",
        "Profile": "User information and preferences",
        "Authentication": "User identity verification"
      }
    },
    {
      "name": "Discovery",
      "description": "Enables server search and discovery",
      "responsibilities": ["Search functionality", "Categorization", "Filtering"],
      "ubiquitous_language": {
        "Search": "Server discovery process",
        "Category": "Server classification",
        "Filter": "Search refinement criteria"
      }
    }
  ],
  "aggregates": [
    {
      "name": "MCPServer",
      "bounded_context": "Server Management",
      "aggregate_root": "MCPServer",
      "entities": ["MCPServer", "ServerVersion"],
      "value_objects": ["ServerMetadata", "VersionInfo"],
      "business_rules": [
        "Server must have unique name within author scope",
        "Version numbers must follow semantic versioning",
        "Published servers cannot be deleted, only deprecated"
      ],
      "invariants": [
        "Server must have at least one version",
        "Latest version must be accessible"
      ]
    },
    {
      "name": "User",
      "bounded_context": "User Management",
      "aggregate_root": "User",
      "entities": ["User", "UserProfile"],
      "value_objects": ["Email", "Username"],
      "business_rules": [
        "Email must be unique across platform",
        "Username must be unique and follow naming conventions",
        "User can only modify their own profile"
      ],
      "invariants": [
        "User must have valid email",
        "User must have unique username"
      ]
    }
  ],
  "domain_entities": [
    {
      "name": "MCPServer",
      "aggregate": "MCPServer",
      "description": "Core entity representing an MCP server",
      "attributes": [
        {"name": "id", "type": "UUID", "description": "Unique server identifier"},
        {"name": "name", "type": "String", "description": "Server name"},
        {"name": "description", "type": "String", "description": "Server description"},
        {"name": "author_id", "type": "UUID", "description": "Server author reference"},
        {"name": "category_id", "type": "UUID", "description": "Server category"},
        {"name": "created_at", "type": "DateTime", "description": "Creation timestamp"},
        {"name": "updated_at", "type": "DateTime", "description": "Last update timestamp"}
      ],
      "business_methods": [
        "publish_version",
        "update_metadata",
        "deprecate",
        "transfer_ownership"
      ]
    },
    {
      "name": "User",
      "aggregate": "User",
      "description": "Platform user entity",
      "attributes": [
        {"name": "id", "type": "UUID", "description": "Unique user identifier"},
        {"name": "username", "type": "String", "description": "Unique username"},
        {"name": "email", "type": "String", "description": "User email address"},
        {"name": "created_at", "type": "DateTime", "description": "Registration timestamp"},
        {"name": "last_login", "type": "DateTime", "description": "Last login timestamp"}
      ],
      "business_methods": [
        "update_profile",
        "change_password",
        "deactivate_account"
      ]
    }
  ],
  "value_objects": [
    {
      "name": "ServerMetadata",
      "description": "Immutable server metadata information",
      "attributes": [
        {"name": "tags", "type": "List[String]", "description": "Server tags"},
        {"name": "documentation_url", "type": "URL", "description": "Documentation link"},
        {"name": "repository_url", "type": "URL", "description": "Source code repository"}
      ]
    },
    {
      "name": "Email",
      "description": "Valid email address",
      "attributes": [
        {"name": "value", "type": "String", "description": "Email address value"}
      ],
      "validation_rules": ["Must be valid email format", "Must not be empty"]
    }
  ],
  "domain_services": [
    {
      "name": "ServerDiscoveryService",
      "description": "Handles complex server search and discovery logic",
      "methods": [
        "search_servers",
        "recommend_servers",
        "categorize_server"
      ],
      "dependencies": ["ServerRepository", "CategoryRepository"]
    }
  ],
  "repositories": [
    {
      "name": "MCPServerRepository",
      "aggregate": "MCPServer",
      "description": "Persistence interface for MCP servers",
      "methods": [
        "find_by_id",
        "find_by_author",
        "find_by_category",
        "search_by_keywords",
        "save",
        "delete"
      ]
    },
    {
      "name": "UserRepository",
      "aggregate": "User",
      "description": "Persistence interface for users",
      "methods": [
        "find_by_id",
        "find_by_username",
        "find_by_email",
        "save",
        "delete"
      ]
    }
  ],
  "domain_events": [
    {
      "name": "ServerPublished",
      "description": "Raised when a new server is published",
      "attributes": [
        {"name": "server_id", "type": "UUID"},
        {"name": "author_id", "type": "UUID"},
        {"name": "published_at", "type": "DateTime"}
      ]
    },
    {
      "name": "UserRegistered",
      "description": "Raised when a new user registers",
      "attributes": [
        {"name": "user_id", "type": "UUID"},
        {"name": "username", "type": "String"},
        {"name": "registered_at", "type": "DateTime"}
      ]
    }
  ]
}
'''
