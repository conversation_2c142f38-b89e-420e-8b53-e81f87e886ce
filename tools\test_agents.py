#!/usr/bin/env python3
"""
Test script for AI Development Agents with OpenRouter LLM

Simple test to verify the agent system is working correctly with real LLM.
"""

import sys
import json
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from langchain_openai import ChatOpenAI
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    print("⚠️ LangChain not available. Please install: pip install langchain-openai")

from tools.ai_dev_agents import AIDevWorkflowOrchestrator


def create_openrouter_llm():
    """Create OpenRouter LLM instance with configuration."""
    if not LANGCHAIN_AVAILABLE:
        print("❌ LangChain not available. Cannot create LLM.")
        return None

    # Configuration - you can modify these or set as environment variables
    config = {
        "api_key": os.getenv("OPENROUTER_API_KEY", ""),
        "model": os.getenv("OPENROUTER_MODEL", "anthropic/claude-3-sonnet"),
        "base_url": os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1"),
        "temperature": float(os.getenv("OPENROUTER_TEMPERATURE", "0.1")),
        "max_tokens": int(os.getenv("OPENROUTER_MAX_TOKENS", "4000"))
    }

    if not config["api_key"]:
        print("❌ OpenRouter API key not found. Please set OPENROUTER_API_KEY environment variable.")
        print("   Or modify the config in this script directly.")
        return None

    try:
        llm = ChatOpenAI(
            api_key=config["api_key"],
            model=config["model"],
            base_url=config["base_url"],
            temperature=config["temperature"],
            max_tokens=config["max_tokens"],
            default_headers={
                "HTTP-Referer": "https://github.com/agilemetrics-tech/ai4se-mcp-hub",
                "X-Title": "AI4SE MCP Hub Development"
            }
        )
        print(f"✅ OpenRouter LLM configured: {config['model']}")
        return llm
    except Exception as e:
        print(f"❌ Failed to create OpenRouter LLM: {e}")
        return None


def test_single_agent(agent_name="business_analyzer"):
    """Test a single agent with real LLM."""
    print(f"🧪 Testing {agent_name} with OpenRouter LLM...")

    # Create LLM
    llm = create_openrouter_llm()
    if not llm:
        return False

    # Sample PRD content for testing
    sample_prd = """
# MCP Server Market Platform

## Project Overview
The MCP Server Market Platform is a centralized marketplace for Model Context Protocol (MCP) servers, enabling developers to discover, share, and manage MCP servers efficiently.

## Core Features

### 1. Server Discovery
- Browse available MCP servers
- Search by category, functionality, or keywords
- View server details and documentation

### 2. Server Submission
- Submit new MCP servers to the marketplace
- Provide server metadata and documentation
- Version management for server updates

### 3. User Management
- User registration and authentication
- Profile management
- Server ownership tracking

## Business Requirements

### User Stories
1. As a developer, I want to discover MCP servers that match my needs
2. As a server author, I want to publish my MCP server to reach more users
3. As a platform admin, I want to manage server quality and user accounts

### Functional Requirements
- RESTful API for all operations
- Database persistence for servers and users
- Authentication and authorization
- Search and filtering capabilities

### Non-Functional Requirements
- High availability and scalability
- Security and data protection
- Performance optimization
- Comprehensive documentation
"""

    try:
        # Create orchestrator with real LLM
        orchestrator = AIDevWorkflowOrchestrator(llm=llm, verbose=True)
        print("✅ Orchestrator created with OpenRouter LLM")

        # Create context
        context = orchestrator.create_context(str(project_root))
        print(f"✅ Context created - Existing modules: {context.existing_modules}")

        if agent_name == "business_analyzer":
            print("\n🔍 Testing Business Analysis with real LLM...")
            result = orchestrator._execute_business_analysis(sample_prd)

            if result.success:
                print("✅ Business analysis completed successfully!")
                data = result.data
                print(f"   - Project: {data.get('business_overview', {}).get('project_name', 'N/A')}")
                print(f"   - Entities: {len(data.get('core_entities', []))}")
                print(f"   - Requirements: {len(data.get('functional_requirements', []))}")
                print(f"   - User Stories: {len(data.get('user_stories', []))}")

                # Save result for inspection
                output_file = project_root / "test_business_analysis_result.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                print(f"   - Result saved to: {output_file}")

                return True
            else:
                print(f"❌ Business analysis failed: {result.errors}")
                return False

        # Add other agent tests here if needed

    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_basic_functionality():
    """Test basic functionality of the agent system."""
    print("🧪 Testing AI Development Agents...")
    
    # Sample PRD content for testing
    sample_prd = """
# MCP Server Market Platform

## Project Overview
The MCP Server Market Platform is a centralized marketplace for Model Context Protocol (MCP) servers, enabling developers to discover, share, and manage MCP servers efficiently.

## Core Features

### 1. Server Discovery
- Browse available MCP servers
- Search by category, functionality, or keywords
- View server details and documentation

### 2. Server Submission
- Submit new MCP servers to the marketplace
- Provide server metadata and documentation
- Version management for server updates

### 3. User Management
- User registration and authentication
- Profile management
- Server ownership tracking

## Business Requirements

### User Stories
1. As a developer, I want to discover MCP servers that match my needs
2. As a server author, I want to publish my MCP server to reach more users
3. As a platform admin, I want to manage server quality and user accounts

### Functional Requirements
- RESTful API for all operations
- Database persistence for servers and users
- Authentication and authorization
- Search and filtering capabilities

### Non-Functional Requirements
- High availability and scalability
- Security and data protection
- Performance optimization
- Comprehensive documentation
"""
    
    try:
        # Create orchestrator
        orchestrator = AIDevWorkflowOrchestrator(llm=None, verbose=True)
        print("✅ Orchestrator created successfully")
        
        # Create context
        context = orchestrator.create_context(str(project_root))
        print(f"✅ Context created - Existing modules: {context.existing_modules}")
        
        # Test business analysis
        print("\n🔍 Testing Business Analysis...")
        business_result = orchestrator._execute_business_analysis(sample_prd)
        
        if business_result.success:
            print("✅ Business analysis completed")
            print(f"   - Entities: {len(business_result.data.get('core_entities', []))}")
            print(f"   - Requirements: {len(business_result.data.get('functional_requirements', []))}")
        else:
            print(f"❌ Business analysis failed: {business_result.errors}")
            return False
        
        # Test domain modeling
        print("\n🏗️ Testing Domain Modeling...")
        domain_result = orchestrator._execute_domain_modeling(business_result.data)
        
        if domain_result.success:
            print("✅ Domain modeling completed")
            print(f"   - Bounded Contexts: {len(domain_result.data.get('bounded_contexts', []))}")
            print(f"   - Aggregates: {len(domain_result.data.get('aggregates', []))}")
        else:
            print(f"❌ Domain modeling failed: {domain_result.errors}")
            return False
        
        # Test requirements generation
        print("\n📋 Testing Requirements Generation...")
        req_result = orchestrator._execute_requirements_generation(domain_result.data)
        
        if req_result.success:
            print("✅ Requirements generation completed")
            req_data = req_result.data
            print(f"   - User Stories: {len(req_data.get('user_stories', []))}")
            print(f"   - API Endpoints: {len(req_data.get('api_design', {}).get('endpoints', []))}")
        else:
            print(f"❌ Requirements generation failed: {req_result.errors}")
            return False
        
        # Test prompt building
        print("\n🤖 Testing Prompt Building...")
        prompt_result = orchestrator._execute_prompt_building(req_result.data)
        
        if prompt_result.success:
            print("✅ Prompt building completed")
            prompt_data = prompt_result.data
            print(f"   - Word Count: {prompt_data.get('word_count', 0)}")
            print(f"   - Sections: {len(prompt_data.get('prompt_sections', []))}")
        else:
            print(f"❌ Prompt building failed: {prompt_result.errors}")
            return False
        
        print("\n🎉 All tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_cli_help():
    """Test CLI help functionality."""
    print("\n🖥️ Testing CLI Help...")
    
    try:
        from tools.ai_dev_agents.cli import main
        
        # Test help command
        import sys
        original_argv = sys.argv
        sys.argv = ["cli.py", "--help"]
        
        try:
            main()
        except SystemExit as e:
            if e.code == 0:
                print("✅ CLI help displayed successfully")
                return True
            else:
                print(f"❌ CLI help failed with exit code: {e.code}")
                return False
        finally:
            sys.argv = original_argv
            
    except Exception as e:
        print(f"❌ CLI test failed: {e}")
        return False


def test_module_imports():
    """Test that all modules can be imported correctly."""
    print("\n📦 Testing Module Imports...")
    
    try:
        # Test individual agent imports
        from tools.ai_dev_agents.base_agent import BaseAgent, AgentResult, WorkflowContext
        print("✅ Base agent imports successful")
        
        from tools.ai_dev_agents.business_analyzer import BusinessAnalyzerAgent
        print("✅ Business analyzer import successful")
        
        from tools.ai_dev_agents.domain_modeler import DomainModelerAgent
        print("✅ Domain modeler import successful")
        
        from tools.ai_dev_agents.requirements_generator import RequirementsGeneratorAgent
        print("✅ Requirements generator import successful")
        
        from tools.ai_dev_agents.prompt_builder import PromptBuilderAgent
        print("✅ Prompt builder import successful")
        
        from tools.ai_dev_agents.orchestrator import AIDevWorkflowOrchestrator
        print("✅ Orchestrator import successful")
        
        # Test package-level imports
        from tools.ai_dev_agents import (
            AIDevWorkflowOrchestrator,
            BaseAgent,
            BusinessAnalyzerAgent,
            DomainModelerAgent,
            RequirementsGeneratorAgent,
            PromptBuilderAgent
        )
        print("✅ Package-level imports successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("🚀 AI Development Agents - OpenRouter LLM Test")
    print("=" * 50)

    # Check if we have the required configuration
    if not LANGCHAIN_AVAILABLE:
        print("❌ LangChain not installed. Please run: pip install langchain-openai")
        return 1

    # Test configuration
    print("\n🔧 Configuration Check:")
    print(f"   - OpenRouter API Key: {'✅ Set' if os.getenv('OPENROUTER_API_KEY') else '❌ Not set'}")
    print(f"   - Model: {os.getenv('OPENROUTER_MODEL', 'anthropic/claude-3-sonnet')}")
    print(f"   - Base URL: {os.getenv('OPENROUTER_BASE_URL', 'https://openrouter.ai/api/v1')}")

    if not os.getenv('OPENROUTER_API_KEY'):
        print("\n⚠️ Please set your OpenRouter configuration:")
        print("   export OPENROUTER_API_KEY='your-api-key'")
        print("   export OPENROUTER_MODEL='anthropic/claude-3-sonnet'  # optional")
        print("   export OPENROUTER_BASE_URL='https://openrouter.ai/api/v1'  # optional")
        print("\nOr modify the config directly in the create_openrouter_llm() function.")
        return 1

    # Run the actual test
    print("\n🧪 Testing Business Analyzer Agent with Real LLM...")
    print("-" * 50)

    if test_single_agent("business_analyzer"):
        print("\n🎉 Test completed successfully!")
        print("📄 Check the generated test_business_analysis_result.json file for detailed results.")
        return 0
    else:
        print("\n❌ Test failed. Please check the configuration and try again.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
