#!/usr/bin/env python3
"""
Test script for AI Development Agents

Simple test to verify the agent system is working correctly.
"""

import sys
import json
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tools.ai_dev_agents import AIDevWorkflowOrchestrator


def test_basic_functionality():
    """Test basic functionality of the agent system."""
    print("🧪 Testing AI Development Agents...")
    
    # Sample PRD content for testing
    sample_prd = """
# MCP Server Market Platform

## Project Overview
The MCP Server Market Platform is a centralized marketplace for Model Context Protocol (MCP) servers, enabling developers to discover, share, and manage MCP servers efficiently.

## Core Features

### 1. Server Discovery
- Browse available MCP servers
- Search by category, functionality, or keywords
- View server details and documentation

### 2. Server Submission
- Submit new MCP servers to the marketplace
- Provide server metadata and documentation
- Version management for server updates

### 3. User Management
- User registration and authentication
- Profile management
- Server ownership tracking

## Business Requirements

### User Stories
1. As a developer, I want to discover MCP servers that match my needs
2. As a server author, I want to publish my MCP server to reach more users
3. As a platform admin, I want to manage server quality and user accounts

### Functional Requirements
- RESTful API for all operations
- Database persistence for servers and users
- Authentication and authorization
- Search and filtering capabilities

### Non-Functional Requirements
- High availability and scalability
- Security and data protection
- Performance optimization
- Comprehensive documentation
"""
    
    try:
        # Create orchestrator
        orchestrator = AIDevWorkflowOrchestrator(llm=None, verbose=True)
        print("✅ Orchestrator created successfully")
        
        # Create context
        context = orchestrator.create_context(str(project_root))
        print(f"✅ Context created - Existing modules: {context.existing_modules}")
        
        # Test business analysis
        print("\n🔍 Testing Business Analysis...")
        business_result = orchestrator._execute_business_analysis(sample_prd)
        
        if business_result.success:
            print("✅ Business analysis completed")
            print(f"   - Entities: {len(business_result.data.get('core_entities', []))}")
            print(f"   - Requirements: {len(business_result.data.get('functional_requirements', []))}")
        else:
            print(f"❌ Business analysis failed: {business_result.errors}")
            return False
        
        # Test domain modeling
        print("\n🏗️ Testing Domain Modeling...")
        domain_result = orchestrator._execute_domain_modeling(business_result.data)
        
        if domain_result.success:
            print("✅ Domain modeling completed")
            print(f"   - Bounded Contexts: {len(domain_result.data.get('bounded_contexts', []))}")
            print(f"   - Aggregates: {len(domain_result.data.get('aggregates', []))}")
        else:
            print(f"❌ Domain modeling failed: {domain_result.errors}")
            return False
        
        # Test requirements generation
        print("\n📋 Testing Requirements Generation...")
        req_result = orchestrator._execute_requirements_generation(domain_result.data)
        
        if req_result.success:
            print("✅ Requirements generation completed")
            req_data = req_result.data
            print(f"   - User Stories: {len(req_data.get('user_stories', []))}")
            print(f"   - API Endpoints: {len(req_data.get('api_design', {}).get('endpoints', []))}")
        else:
            print(f"❌ Requirements generation failed: {req_result.errors}")
            return False
        
        # Test prompt building
        print("\n🤖 Testing Prompt Building...")
        prompt_result = orchestrator._execute_prompt_building(req_result.data)
        
        if prompt_result.success:
            print("✅ Prompt building completed")
            prompt_data = prompt_result.data
            print(f"   - Word Count: {prompt_data.get('word_count', 0)}")
            print(f"   - Sections: {len(prompt_data.get('prompt_sections', []))}")
        else:
            print(f"❌ Prompt building failed: {prompt_result.errors}")
            return False
        
        print("\n🎉 All tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_cli_help():
    """Test CLI help functionality."""
    print("\n🖥️ Testing CLI Help...")
    
    try:
        from tools.ai_dev_agents.cli import main
        
        # Test help command
        import sys
        original_argv = sys.argv
        sys.argv = ["cli.py", "--help"]
        
        try:
            main()
        except SystemExit as e:
            if e.code == 0:
                print("✅ CLI help displayed successfully")
                return True
            else:
                print(f"❌ CLI help failed with exit code: {e.code}")
                return False
        finally:
            sys.argv = original_argv
            
    except Exception as e:
        print(f"❌ CLI test failed: {e}")
        return False


def test_module_imports():
    """Test that all modules can be imported correctly."""
    print("\n📦 Testing Module Imports...")
    
    try:
        # Test individual agent imports
        from tools.ai_dev_agents.base_agent import BaseAgent, AgentResult, WorkflowContext
        print("✅ Base agent imports successful")
        
        from tools.ai_dev_agents.business_analyzer import BusinessAnalyzerAgent
        print("✅ Business analyzer import successful")
        
        from tools.ai_dev_agents.domain_modeler import DomainModelerAgent
        print("✅ Domain modeler import successful")
        
        from tools.ai_dev_agents.requirements_generator import RequirementsGeneratorAgent
        print("✅ Requirements generator import successful")
        
        from tools.ai_dev_agents.prompt_builder import PromptBuilderAgent
        print("✅ Prompt builder import successful")
        
        from tools.ai_dev_agents.orchestrator import AIDevWorkflowOrchestrator
        print("✅ Orchestrator import successful")
        
        # Test package-level imports
        from tools.ai_dev_agents import (
            AIDevWorkflowOrchestrator,
            BaseAgent,
            BusinessAnalyzerAgent,
            DomainModelerAgent,
            RequirementsGeneratorAgent,
            PromptBuilderAgent
        )
        print("✅ Package-level imports successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("🚀 Starting AI Development Agents Test Suite")
    print("=" * 50)
    
    tests = [
        ("Module Imports", test_module_imports),
        ("Basic Functionality", test_basic_functionality),
        ("CLI Help", test_cli_help),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The agent system is ready to use.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
