#!/usr/bin/env python3
"""
Test script for enhanced configuration manager.
"""

from ai_dev_agents.config_manager import ConfigManager, ValidationLevel, ConfigFormat
import logging

def test_enhanced_config():
    """Test enhanced configuration manager features."""
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    print('=== Testing Enhanced Configuration Manager ===')
    
    try:
        # Test with different validation levels
        config_manager = ConfigManager(
            validation_level=ValidationLevel.MODERATE,
            enable_hot_reload=False,
            enable_caching=True
        )
        
        print('✓ Configuration manager initialized')
        print(f'  Config path: {config_manager.config_path}')
        print(f'  Format: {config_manager._detect_format().value}')
        
        # Test configuration summary
        summary = config_manager.get_config_summary()
        print('✓ Configuration summary:')
        for key, value in summary.items():
            print(f'  {key}: {value}')
        
        # Test validation
        validation = config_manager.validate_config()
        print('✓ Configuration validation:')
        print(f'  Valid: {validation["valid"]}')
        print(f'  Errors: {len(validation["errors"])}')
        print(f'  Warnings: {len(validation["warnings"])}')
        
        if validation["errors"]:
            print('  Error details:')
            for error in validation["errors"]:
                print(f'    - {error}')
        
        if validation["warnings"]:
            print('  Warning details:')
            for warning in validation["warnings"]:
                print(f'    - {warning}')
        
        # Test enhanced config objects
        llm_config = config_manager.get_llm_config()
        print(f'✓ LLM Config: {llm_config.provider}/{llm_config.model}')
        print(f'  Temperature: {llm_config.temperature}')
        print(f'  Max tokens: {llm_config.max_tokens}')
        print(f'  Retry attempts: {llm_config.retry_attempts}')
        
        system_config = config_manager.get_system_config()
        print(f'✓ System Config: log_level={system_config.log_level}, workers={system_config.max_workers}')
        print(f'  Cache enabled: {system_config.cache_enabled}')
        print(f'  Metrics enabled: {system_config.metrics_enabled}')
        
        project_config = config_manager.get_project_config()
        print(f'✓ Project Config: {project_config.architecture_style}')
        print(f'  Tech stack: {project_config.tech_stack}')
        print(f'  Existing modules: {project_config.existing_modules}')
        
        security_config = config_manager.get_security_config()
        print(f'✓ Security Config: encrypt={security_config.encrypt_credentials}')
        print(f'  Audit logging: {security_config.audit_logging}')
        
        # Test presets
        presets = config_manager.list_available_presets()
        if presets:
            print(f'✓ Available presets: {list(presets.keys())}')
        else:
            print('✓ No presets configured')
        
        # Test LLM creation
        try:
            llm = config_manager.create_llm()
            if llm:
                print('✓ LLM instance created successfully')
            else:
                print('⚠ LLM instance creation failed (check API key)')
        except Exception as e:
            print(f'⚠ LLM creation error: {e}')
        
        print('\n=== All tests completed! ===')
        
        # Cleanup
        config_manager.shutdown()
        
    except Exception as e:
        print(f'✗ Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_config()
