{"module_overview": {"module_name": "category", "bounded_context": "Server Management", "primary_responsibility": "Manage server categories for organization and filtering", "business_value": "Enable logical grouping and filtering of MCP servers", "core_capabilities": ["Create and manage server categories", "Ensure unique category names", "Provide category descriptions"], "external_dependencies": ["auth module for authentication", "user module for ownership tracking"]}, "user_stories": [{"id": "US-CAT-001", "title": "Create a new server category", "story": "As a system administrator, I want to create new server categories so that I can organize servers logically", "acceptance_criteria": ["Given valid authentication, when I submit a unique category name, then the category should be created", "Given a duplicate category name, when I try to create it, then I should receive a validation error"], "priority": "高", "story_points": 3, "business_rules": ["Category name must be unique", "Category ID must be UUID"]}, {"id": "US-CAT-002", "title": "List all server categories", "story": "As a server operator, I want to view all available categories so that I can filter servers", "acceptance_criteria": ["Given valid authentication, when I request categories, then I should receive a paginated list", "Given no categories exist, when I request categories, then I should receive an empty list"], "priority": "中", "story_points": 2, "business_rules": ["Response should include pagination metadata"]}], "api_design": {"base_path": "/api/v1/categories", "endpoints": [{"method": "POST", "path": "/", "summary": "Create new category", "description": "Creates a new server category with unique name", "request_schema": "CategoryCreateRequest", "response_schema": "CategoryResponse", "authentication_required": true, "authorization_roles": ["admin"], "business_logic": "Validates name uniqueness before creation"}, {"method": "GET", "path": "/", "summary": "List categories", "description": "Returns paginated list of server categories", "request_schema": null, "response_schema": "PaginatedCategoryResponse", "authentication_required": true, "authorization_roles": ["admin", "operator"], "business_logic": "Applies default pagination if not specified"}], "schemas": [{"name": "CategoryCreateRequest", "type": "request", "fields": [{"name": "name", "type": "string", "required": true, "description": "Unique name for the category", "validation_rules": ["min_length:3", "max_length:50"]}, {"name": "description", "type": "string", "required": false, "description": "Optional description of the category", "validation_rules": ["max_length:255"]}]}, {"name": "CategoryResponse", "type": "response", "fields": [{"name": "id", "type": "UUID", "required": true, "description": "Unique identifier of the category"}, {"name": "name", "type": "string", "required": true, "description": "Name of the category"}, {"name": "description", "type": "string", "required": false, "description": "Description of the category"}, {"name": "created_at", "type": "datetime", "required": true, "description": "Creation timestamp"}]}]}, "data_models": {"tables": [{"name": "categories", "description": "Stores server category information", "columns": [{"name": "id", "type": "UUID", "constraints": ["PRIMARY KEY", "DEFAULT gen_random_uuid()"], "description": "Primary key"}, {"name": "name", "type": "VARCHAR(50)", "constraints": ["NOT NULL", "UNIQUE"], "description": "Unique category name"}, {"name": "description", "type": "VARCHAR(255)", "constraints": [], "description": "Optional description"}, {"name": "created_at", "type": "TIMESTAMP", "constraints": ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"], "description": "Creation timestamp"}], "indexes": [{"name": "idx_categories_name", "columns": ["name"], "type": "btree", "unique": true}], "relationships": []}]}, "business_logic": {"domain_entities": [{"name": "Category", "aggregate": "Category", "attributes": [{"name": "id", "type": "UUID", "validation_rules": ["required"]}, {"name": "name", "type": "str", "validation_rules": ["min_length:3", "max_length:50", "unique"]}, {"name": "description", "type": "Optional[str]", "validation_rules": ["max_length:255"]}], "business_methods": [{"name": "validate_name_uniqueness", "description": "Ensures category name is unique", "parameters": ["name"], "business_rules": ["Name must not exist in repository"], "exceptions": ["DuplicateCategoryError"]}], "invariants": ["Name must be unique across all categories", "ID must be immutable"]}], "application_services": [{"name": "CategoryService", "description": "Handles category management operations", "methods": [{"name": "create_category", "description": "Creates a new category with validation", "use_case": "US-CAT-001", "transaction_boundary": true, "dependencies": ["CategoryRepository"]}, {"name": "list_categories", "description": "Retrieves paginated list of categories", "use_case": "US-CAT-002", "transaction_boundary": false, "dependencies": ["CategoryRepository"]}]}], "repositories": [{"name": "CategoryRepository", "entity": "Category", "interface_methods": [{"name": "add", "description": "Persists a new category", "parameters": ["category"], "return_type": "None"}, {"name": "get_by_name", "description": "Finds category by name", "parameters": ["name"], "return_type": "Optional[Category]"}, {"name": "list_all", "description": "Retrieves paginated categories", "parameters": ["page", "size"], "return_type": "List[Category]"}]}]}, "testing_strategy": {"unit_tests": [{"target": "Category entity", "test_cases": [{"name": "should_raise_error_when_name_exists", "description": "Test name uniqueness validation", "given": "Existing category with same name", "when": "Creating new category with duplicate name", "then": "Should raise DuplicateCategoryError"}]}], "integration_tests": [{"scope": "CategoryService with repository", "scenarios": [{"name": "successful_category_creation", "description": "Test complete creation flow", "test_data": "Valid category data", "expected_outcome": "Category should be persisted"}]}], "api_tests": [{"endpoint": "/api/v1/categories/", "test_cases": [{"name": "create_category_success", "method": "POST", "request_data": {"name": "test-category"}, "expected_status": 201, "expected_response": {"name": "test-category"}}]}]}, "technical_constraints": {"architecture_constraints": ["Must follow DDD four-layer architecture", "Domain layer must not depend on FastAPI", "Use UUID v7 as primary keys", "Pydantic for request/response validation"], "performance_requirements": ["Category listing response time < 100ms for 1000 records", "Database queries must use indexes"], "security_requirements": ["JWT authentication required for all endpoints", "Admin role required for write operations"], "quality_requirements": ["90% code coverage minimum", "PEP 8 compliance", "Type hints for all methods"]}, "implementation_order": ["1. Domain layer - Category entity and repository interface", "2. Infrastructure layer - SQLAlchemy model and repository implementation", "3. Application layer - CategoryService", "4. Interface layer - FastAPI routers", "5. Database migration - Alembic script for categories table", "6. Test implementation - Unit, integration, and API tests"], "project_metadata": {"generation_timestamp": "2025-06-25T10:26:47.684382", "architecture_style": "DDD + FastAPI", "tech_stack": ["FastAPI", "SQLAlchemy", "Pydantic", "Alembic", "<PERSON><PERSON><PERSON>"], "existing_modules": ["user", "auth", "oauth_provider"]}, "dependency_analysis": {"internal_dependencies": [], "external_dependencies": ["auth module for authentication", "user module for ownership tracking"], "cross_module_dependencies": ["user", "auth"]}, "complexity_estimation": {"story_points_total": 5, "api_endpoints_count": 2, "database_tables_count": 1, "domain_entities_count": 1, "estimated_development_days": 2.5}}