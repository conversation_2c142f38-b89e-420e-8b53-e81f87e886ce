```markdown
# AI Development Task Prompt

Generated on: 2025-06-25 10:14:54

## Task Description

### Development Task
Implement a `server_management` module for MCP server lifecycle management including:
- Server registration (POST /servers)
- Server details update (PATCH /servers/{server_id})
- Server query (GET /servers)
- Server categorization functionality

### Expected Deliverables
1. Complete FastAPI implementation with all specified endpoints
2. SQLAlchemy models for mcp_servers and tool_definitions tables
3. Pydantic schemas for request/response validation
4. Alembic migration scripts
5. Unit tests with >80% coverage
6. API documentation in OpenAPI format

### Success Criteria
- All user stories (US-SM-001 to US-SM-003) fully implemented
- API endpoints working as specified
- Integration with existing auth module
- Passing CI/CD pipeline checks

## Project Context

### Architecture Style
- Domain-Driven Design (DDD) 4-layer architecture
- Clean Architecture principles
- REST API with FastAPI

### Technology Stack
- Python 3.10+
- FastAPI (Web framework)
- SQLAlchemy 2.0 (ORM)
- Pydantic (Data validation)
- Alembic (Migrations)
- Pytest (Testing)
- PostgreSQL (Database)

### Coding Standards
- PEP 8 compliance
- Type hints required
- English comments and docstrings
- 120 character line length
- Black code formatting

### Existing Modules
1. `user` module:
   - Contains User model and related services
   - Example: `user/models.py`, `user/schemas.py`
2. `auth` module:
   - Handles authentication and authorization
   - JWT token implementation
3. `oauth_provider` module:
   - OAuth2 integration
   - Reference for external service integration

## Architectural Constraints (MUST FOLLOW)

1. **DDD Layer Structure**:
   - Presentation (FastAPI routes)
   - Application (Services)
   - Domain (Models, business logic)
   - Infrastructure (DB, external services)

2. **Dependency Direction**:
   - Presentation → Application → Domain ← Infrastructure
   - No circular dependencies allowed

3. **Module Boundaries**:
   - Clear separation between server management and other domains
   - Shared kernel only for common types and utilities

4. **Data Modeling**:
   - UUID primary keys for all entities
   - SQLAlchemy 2.0 style models
   - Pydantic v2 for schemas

## Implementation Guidelines

### Development Steps
1. Set up module structure:
   ```
   server_management/
   ├── __init__.py
   ├── domain/
   │   ├── models.py
   │   └── services.py
   ├── application/
   │   ├── services.py
   │   └── schemas.py
   ├── infrastructure/
   │   ├── repositories.py
   │   └── database.py
   └── presentation/
       └── routers.py
   ```

2. Implementation order:
   - Define domain models and value objects
   - Create database models and migrations
   - Implement repository pattern
   - Write application services
   - Create API endpoints
   - Add validation schemas
   - Implement tests

3. Code Organization:
   - Keep business logic in domain layer
   - Put API-specific code in presentation layer
   - Database operations in infrastructure
   - Cross-cutting concerns in application layer

### Best Practices
- Use dependency injection
- Follow repository pattern for data access
- Implement proper error handling
- Write integration tests for API endpoints
- Document all public interfaces

## Quality Requirements

### Code Quality
- Pylint score > 9.0/10
- No code smells detected
- No duplicated code
- All public methods documented

### Testing
- Unit test coverage ≥ 80%
- Integration test coverage ≥ 70%
- Test all edge cases
- Include negative test cases

### Documentation
- Complete API documentation
- Module-level docstrings
- Type hints for all functions
- README with usage examples

## Acceptance Criteria

### Functional
- All specified API endpoints implemented
- CRUD operations working correctly
- Categorization feature functional
- Integration with existing auth system

### Technical
- Proper DDD layer separation
- Clean dependency graph
- Passing all tests
- No security vulnerabilities

### Quality
- Meets all coding standards
- Documentation complete
- Performance benchmarks met
- Error handling implemented

## Reference Examples

### Existing Patterns
1. User module structure:
   ```python
   # Example from user/models.py
   class User(Base):
       __tablename__ = "users"
       id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
       email = Column(String(255), unique=True, nullable=False)
   ```

2. Auth integration:
   ```python
   # Example from auth/dependencies.py
   def get_current_user(token: str = Depends(oauth2_scheme)) -> User:
       # JWT validation logic
   ```

### Architecture Reference
```python
# Example of DDD layer separation
# presentation/routers.py → application/services.py → domain/models.py
```

### Best Practices
1. Repository pattern:
   ```python
   class ServerRepository:
       async def get(self, server_id: UUID) -> Server:
           # DB access logic
   ```

2. Pydantic schemas:
   ```python
   class ServerCreate(BaseModel):
       name: str
       url: HttpUrl
       category: Optional[str]
   ```

## Development Requirements

### Detailed Requirements
1. Implement all specified API endpoints
2. Create complete data model with:
   - Server metadata
   - Tool definitions
   - Categorization system
3. Add proper error handling
4. Implement input validation
5. Write comprehensive tests

### Project Rules
1. **Architecture**:
   - Strict DDD layer separation
   - No business logic in presentation layer

2. **Code Quality**:
   - Type hints mandatory
   - 100% PEP 8 compliance
   - Document all public interfaces

3. **Testing**:
   - Test coverage ≥ 80%
   - Include integration tests
   - Test edge cases

4. **Security**:
   - Input validation
   - Proper auth integration
   - No sensitive data exposure
```