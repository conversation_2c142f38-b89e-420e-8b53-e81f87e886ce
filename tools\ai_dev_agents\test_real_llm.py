#!/usr/bin/env python3
"""
Test BusinessAnalyzerAgent with real LLM
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from ai_dev_agents.agents.business_analyzer import BusinessAnalyzerAgent
from ai_dev_agents.core.base_agent import WorkflowContext
from ai_dev_agents.utils.config_manager import Config<PERSON>ana<PERSON>

def test_real_llm_business_analyzer():
    """Test the business analyzer with real LLM."""
    print("=== Testing Business Analyzer (Real LLM) ===")
    
    try:
        # Initialize config and agent
        config_manager = ConfigManager("config.yaml")
        llm = config_manager.create_llm()
        
        if llm is None:
            print("❌ Failed to create LLM. Please check your configuration.")
            return False
            
        agent = BusinessAnalyzerAgent(llm=llm)
        
        # Create test context
        context = WorkflowContext(
            project_root="./test_project",
            project_rules="DDD + FastAPI architecture rules",
            existing_modules=["auth", "user"],
            tech_stack=["FastAPI", "SQLAlchemy"],
            architecture_style="DDD"
        )
        
        # Test PRD content
        prd_content = """
# MCP Server Market Platform

## 项目概述
构建一个 MCP (Model Context Protocol) 服务器市场平台，让开发者可以发布、发现和使用各种 MCP 服务器。

## 核心功能

### 1. 用户管理
- 用户注册和登录
- 开发者认证
- 用户资料管理

### 2. MCP 服务器管理
- 服务器发布和更新
- 版本管理
- 服务器分类和标签

### 3. 市场功能
- 服务器搜索和发现
- 评分和评论系统
- 下载统计

### 4. 开发者工具
- API 文档生成
- 测试工具
- 使用分析

## 技术要求
- 使用 FastAPI 框架
- PostgreSQL 数据库
- RESTful API 设计
- JWT 认证
"""
        
        print("📝 Testing with sample PRD content...")
        print(f"PRD length: {len(prd_content)} characters")
        
        # Process the PRD
        result = agent.process({"prd_content": prd_content}, context)
        
        if result.success:
            print("✅ Business analysis completed successfully!")
            print(f"📊 Analysis quality score: {result.metadata.get('quality_score', 'N/A')}")
            
            # Display key results
            data = result.data
            if isinstance(data, dict):
                print("\n📋 Analysis Summary:")
                
                # Business overview
                if "business_overview" in data:
                    overview = data["business_overview"]
                    print(f"  Project: {overview.get('project_name', 'N/A')}")
                    print(f"  Purpose: {overview.get('core_purpose', 'N/A')}")
                
                # Core entities
                if "core_entities" in data:
                    entities = data["core_entities"]
                    print(f"  Entities identified: {len(entities)}")
                    for entity in entities[:3]:  # Show first 3
                        print(f"    - {entity.get('name', 'N/A')}")
                
                # Functional requirements
                if "functional_requirements" in data:
                    requirements = data["functional_requirements"]
                    print(f"  Functional requirements: {len(requirements)}")
                
                # User stories
                if "user_stories" in data:
                    stories = data["user_stories"]
                    print(f"  User stories: {len(stories)}")
            
            return True
        else:
            print(f"❌ Business analysis failed: {', '.join(result.errors)}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analysis_quality():
    """Test the quality assessment features."""
    print("\n=== Testing Analysis Quality Features ===")
    
    try:
        config_manager = ConfigManager("config.yaml")
        llm = config_manager.create_llm()
        
        if llm is None:
            print("❌ Failed to create LLM. Skipping quality test.")
            return False
            
        agent = BusinessAnalyzerAgent(llm=llm)
        
        # Test with a more complex PRD
        complex_prd = """
# E-commerce Platform

## Business Context
We are building a comprehensive e-commerce platform to compete with major players in the online retail space.

## Target Users
- End customers (buyers)
- Merchants (sellers)
- Platform administrators

## Core Business Entities
- Products with variants, pricing, inventory
- Orders with multiple items, shipping, payments
- Users with profiles, preferences, history
- Merchants with stores, analytics, payouts

## Business Rules
- Inventory must be checked before order confirmation
- Payment must be processed before order fulfillment
- Returns are allowed within 30 days
- Merchants get 85% of sale price after platform fees

## Functional Requirements
1. Product catalog management
2. Shopping cart and checkout
3. Payment processing
4. Order management
5. User account management
6. Merchant dashboard
7. Admin panel
8. Search and recommendations

## Non-functional Requirements
- Handle 10,000 concurrent users
- 99.9% uptime
- PCI DSS compliance
- GDPR compliance
"""
        
        context = WorkflowContext(
            project_root="./ecommerce_project",
            project_rules="DDD + FastAPI architecture rules",
            existing_modules=["auth", "payment"],
            tech_stack=["FastAPI", "SQLAlchemy", "Redis"],
            architecture_style="DDD"
        )
        
        print("📝 Testing with complex PRD...")
        result = agent.process({"prd_content": complex_prd}, context)
        
        if result.success:
            print("✅ Complex analysis completed!")
            
            # Check quality metrics
            quality_score = result.metadata.get('quality_score', 0)
            print(f"📊 Quality score: {quality_score:.2f}")
            
            if quality_score >= 0.8:
                print("🌟 High quality analysis achieved!")
            elif quality_score >= 0.6:
                print("👍 Good quality analysis")
            else:
                print("⚠️  Analysis quality could be improved")
                
            return True
        else:
            print(f"❌ Complex analysis failed: {', '.join(result.errors)}")
            return False
            
    except Exception as e:
        print(f"❌ Quality test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Real LLM Tests for Business Analyzer\n")
    
    tests = [
        ("Basic Business Analysis", test_real_llm_business_analyzer),
        ("Analysis Quality Assessment", test_analysis_quality),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print('='*60)
        
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print('='*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All real LLM tests passed!")
        return 0
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
