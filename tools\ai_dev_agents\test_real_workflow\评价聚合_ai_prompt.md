```markdown
# AI Development Task Prompt

Generated on: 2025-06-25 10:31:59

## Task Description

### Development Task
Implement a Review module for managing user reviews of MCP servers, including:
1. Submit server review functionality (US-001)
2. View server reviews functionality (US-002)

### Expected Deliverables
1. Complete FastAPI endpoints:
   - POST /api/v1/reviews/
   - GET /api/v1/reviews/servers/{server_id}
2. SQLAlchemy data model for reviews
3. Pydantic schemas for request/response
4. Alembic migration script
5. Unit and integration tests

### Success Criteria
- All user stories implemented
- API endpoints fully functional
- Passing CI/CD pipeline
- 80%+ test coverage

## Project Context

### Architecture Style
- Domain-Driven Design (4-layer architecture)
- Clean Architecture principles
- RESTful API design

### Technology Stack
- Python 3.10+
- FastAPI (API layer)
- SQLAlchemy 2.0 (Data access)
- Pydantic (Data validation)
- Alembic (Migrations)
- Pytest (Testing)

### Coding Standards
- PEP 8 compliance
- Type hints for all functions
- English comments and docstrings
- Black code formatting
- Pre-commit hooks

### Existing Modules
1. `user` module:
   - Path: ./user/
   - Reference pattern: UUID primary keys, DDD structure
2. `auth` module:
   - Path: ./auth/
   - Reference pattern: JWT authentication flow
3. `oauth_provider` module:
   - Path: ./oauth_provider/
   - Reference pattern: Third-party integration

## Architectural Constraints (MUST FOLLOW)

1. DDD Layer Structure:
   - Application Layer: FastAPI routes
   - Domain Layer: Business logic
   - Infrastructure Layer: DB operations
   - Presentation Layer: Pydantic models

2. Dependency Direction:
   - Presentation → Application → Domain ← Infrastructure
   - No circular dependencies

3. Module Boundaries:
   - Review module must be self-contained
   - Can depend on auth for user verification
   - No direct DB access from application layer

## Implementation Guidelines

### Development Steps
1. Create module structure:
   ```
   review/
   ├── __init__.py
   ├── application/
   │   ├── commands.py
   │   └── queries.py
   ├── domain/
   │   ├── models.py
   │   └── services.py
   ├── infrastructure/
   │   ├── repositories.py
   │   └── models.py
   ├── presentation/
   │   ├── schemas.py
   │   └── routes.py
   └── tests/
   ```

2. Implementation Order:
   - Define Pydantic schemas
   - Create SQLAlchemy model
   - Implement repository pattern
   - Write domain services
   - Create application handlers
   - Add FastAPI routes
   - Write migration script
   - Implement tests

### Code Organization
- Use dependency injection pattern
- Keep business logic in domain layer
- Put DB operations in infrastructure
- Validate all inputs with Pydantic

## Quality Requirements

### Code Quality
- 100% PEP 8 compliance (verified by flake8)
- Type hint coverage: 100%
- Docstring coverage: 80%+

### Testing
- Test coverage: ≥80%
- Include:
  - Unit tests for domain logic
  - Integration tests for API
  - Negative test cases
- Use pytest fixtures

### Documentation
- Module-level docstring
- API documentation via FastAPI OpenAPI
- README.md with usage examples

## Acceptance Criteria

### Functional
- POST endpoint creates reviews with validation
- GET endpoint returns paginated reviews
- Proper error handling (400, 401, 404, 500)
- JWT authentication integration

### Technical
- Proper DDD layer separation
- No direct SQLAlchemy in application layer
- Async/await pattern used consistently
- Migration script works correctly

### Quality
- All tests passing
- No critical code smells
- Security vulnerabilities scanned

## Reference Examples

### Existing Patterns
1. User module structure:
   ```python
   # Example domain model
   class User:
       id: UUID
       email: str
       hashed_password: str
   ```

2. Auth module JWT flow:
   ```python
   # Example dependency
   async def get_current_user(token: str = Depends(oauth2_scheme)):
       # verification logic
   ```

### Best Practices
1. Repository pattern:
   ```python
   class ReviewRepository:
       async def create(self, review: Review) -> Review:
           # implementation
   ```

2. Pydantic schema:
   ```python
   class ReviewCreate(BaseModel):
       server_id: UUID
       rating: conint(ge=1, le=5)
       comment: Optional[str] = None
   ```

## Development Requirements

### Detailed Requirements
1. Review Model Fields:
   - id: UUID (PK)
   - user_id: UUID (FK)
   - server_id: UUID
   - rating: int (1-5)
   - comment: str (optional)
   - created_at: datetime

2. API Specifications:
   - POST /reviews/:
     - Requires authentication
     - Validates rating range
   - GET /reviews/servers/{server_id}:
     - Returns paginated results
     - Optional query params: limit, offset

## Project Rules and Standards

### Mandatory Rules
1. Architecture:
   - Strict DDD layer separation
   - No business logic in infrastructure
   - Repository pattern for data access

2. Code Quality:
   - Type hints mandatory
   - 100% PEP 8 compliance
   - No direct DB calls from routes

3. Security:
   - Input validation on all endpoints
   - Authentication for write operations
   - Parameterized queries only

4. Testing:
   - 80% coverage minimum
   - Test pyramid balance
   - CI/CD integration
```