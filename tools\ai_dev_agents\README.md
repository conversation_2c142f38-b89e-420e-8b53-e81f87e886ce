# AI Development Agents

基于 LangChain 的智能AI开发工作流程，将产品需求文档(PRD)转换为高质量的AI开发提示词。

## 🎯 概述

这是一套完整的AI辅助开发工具链，使用LangChain Agent技术实现智能化的需求分析、领域建模、技术需求生成和AI提示词构建。

### 核心优势

- **智能语言理解**: 利用LLM的自然语言理解能力，深度分析PRD文档
- **DDD领域建模**: 自动应用领域驱动设计方法论，创建清晰的领域模型
- **技术需求生成**: 生成详细的技术实现规范，包含API设计、数据模型、测试策略
- **AI提示词构建**: 构建包含完整项目上下文的高质量AI开发提示词
- **质量保证**: 每个阶段都有质量评估和验证机制

## 🏗️ 架构设计

```
PRD文档 → 业务分析Agent → 领域建模Agent → 需求生成Agent → 提示词构建Agent → AI开发提示词
```

### Agent组件

1. **BusinessAnalyzerAgent** - 业务需求分析
   - 提取核心业务概念和实体
   - 识别用户角色和使用场景
   - 分析业务流程和规则

2. **DomainModelerAgent** - DDD领域建模
   - 识别边界上下文(Bounded Context)
   - 设计聚合根和实体
   - 定义值对象和领域服务

3. **RequirementsGeneratorAgent** - 技术需求生成
   - 生成用户故事和验收标准
   - 设计API接口规范
   - 规划数据库模式和测试策略

4. **PromptBuilderAgent** - AI提示词构建
   - 整合项目上下文和约束
   - 构建结构化的开发指令
   - 包含代码示例和参考模式

## 🚀 快速开始

### 安装依赖

```bash
# 安装LangChain (当前版本为模拟实现)
pip install langchain openai

# 或者使用项目的依赖管理
uv add langchain openai
```

### 基本使用

#### 1. 完整工作流程

```bash
# 执行完整的AI开发工作流程
python -m tools.ai_dev_agents.cli workflow design/mcp-market-prd.txt

# 指定输出目录
python -m tools.ai_dev_agents.cli workflow design/mcp-market-prd.txt --output my_output

# 选择特定模块
python -m tools.ai_dev_agents.cli workflow design/mcp-market-prd.txt --modules "mcp_server,server_submission"
```

#### 2. 分步执行

```bash
# 步骤1: 业务需求分析
python -m tools.ai_dev_agents.cli business-analysis design/mcp-market-prd.txt

# 步骤2: 领域建模
python -m tools.ai_dev_agents.cli domain-modeling business_analysis.json

# 步骤3: 生成技术需求
python -m tools.ai_dev_agents.cli generate-requirements domain_model.json --module mcp_server

# 步骤4: 构建AI提示词
python -m tools.ai_dev_agents.cli build-prompt mcp_server_requirements.json
```

## 📋 详细使用指南

### 命令行参数

#### 全局参数
- `--verbose, -v`: 启用详细输出
- `--project-root`: 项目根目录 (默认: 当前目录)

#### workflow 命令
```bash
python -m tools.ai_dev_agents.cli workflow <prd_file> [options]
```

**参数:**
- `prd_file`: PRD文档路径
- `--output, -o`: 输出目录 (默认: output)
- `--modules, -m`: 要处理的模块列表 (逗号分隔)

**示例:**
```bash
# 处理所有模块
python -m tools.ai_dev_agents.cli workflow design/mcp-market-prd.txt

# 只处理特定模块
python -m tools.ai_dev_agents.cli workflow design/mcp-market-prd.txt -m "mcp_server,user_management"

# 自定义输出目录
python -m tools.ai_dev_agents.cli workflow design/mcp-market-prd.txt -o custom_output
```

#### business-analysis 命令
```bash
python -m tools.ai_dev_agents.cli business-analysis <prd_file> [--output output_file]
```

#### domain-modeling 命令
```bash
python -m tools.ai_dev_agents.cli domain-modeling <business_analysis_file> [--output output_file]
```

#### generate-requirements 命令
```bash
python -m tools.ai_dev_agents.cli generate-requirements <domain_model_file> [--module module_name] [--output output_file]
```

#### build-prompt 命令
```bash
python -m tools.ai_dev_agents.cli build-prompt <requirements_file> [--output output_file]
```

### 输出文件说明

#### 业务分析结果 (`business_analysis.json`)
```json
{
  "business_overview": {
    "project_name": "项目名称",
    "core_purpose": "核心目标",
    "target_users": ["用户群体"],
    "business_value": "业务价值"
  },
  "core_entities": [...],
  "business_rules": [...],
  "functional_requirements": [...],
  "user_stories": [...]
}
```

#### 领域模型 (`domain_model.json`)
```json
{
  "bounded_contexts": [...],
  "aggregates": [...],
  "domain_entities": [...],
  "value_objects": [...],
  "domain_services": [...],
  "repositories": [...],
  "domain_events": [...]
}
```

#### 技术需求 (`{module}_requirements.json`)
```json
{
  "module_overview": {...},
  "user_stories": [...],
  "api_design": {...},
  "data_models": {...},
  "business_logic": {...},
  "testing_strategy": {...},
  "technical_constraints": {...},
  "implementation_order": [...]
}
```

#### AI提示词 (`{module}_ai_prompt.md`)
包含完整的AI开发提示词，包括：
- 任务描述和项目上下文
- 架构约束和实现指南
- 质量要求和验收标准
- 参考示例和项目规范

## 🔧 配置和自定义

### LLM配置

当前版本使用模拟实现，实际使用时需要配置LLM：

```python
from langchain.llms import OpenAI
from tools.ai_dev_agents import AIDevWorkflowOrchestrator

# 配置LLM
llm = OpenAI(api_key="your-api-key")

# 创建编排器
orchestrator = AIDevWorkflowOrchestrator(llm=llm, verbose=True)
```

### 项目规则自定义

在项目根目录创建 `.roo/rules/rules.md` 文件来自定义项目规则：

```markdown
# 项目开发规范

## 架构约束
- 使用DDD四层架构
- UUID主键约束
- 英文文档和注释

## 编码标准
- PEP 8代码风格
- 类型提示必需
- 测试覆盖率 > 80%
```

## 🧪 测试和验证

### 运行测试

```bash
# 运行单元测试
pytest tests/tools/ai_dev_agents/

# 运行集成测试
pytest tests/tools/ai_dev_agents/integration/

# 生成覆盖率报告
pytest --cov=tools.ai_dev_agents tests/
```

### 验证输出质量

工具提供多种质量验证机制：

1. **结构验证**: 检查输出JSON格式和必需字段
2. **业务逻辑验证**: 验证业务规则和约束
3. **技术可行性验证**: 检查技术方案的合理性
4. **一致性验证**: 确保各阶段输出的一致性

## 🔍 故障排除

### 常见问题

#### 1. LLM未配置
```
⚠️ Warning: LLM not configured. Agents will run in mock mode.
```
**解决方案**: 配置有效的LLM实例

#### 2. PRD文件格式问题
```
❌ Error: PRD file not found: design/mcp-market-prd.txt
```
**解决方案**: 检查文件路径和格式

#### 3. 输出质量问题
```
❌ Business analysis failed: Missing required field: core_entities
```
**解决方案**: 检查PRD文档内容完整性，或调整Agent提示词

### 调试模式

启用详细输出来调试问题：

```bash
python -m tools.ai_dev_agents.cli workflow design/mcp-market-prd.txt --verbose
```

## 🚧 开发计划

### 当前版本 (v1.0)
- ✅ 基础Agent架构
- ✅ 完整工作流程
- ✅ 命令行接口
- ⚠️ 模拟LLM实现

### 下一版本 (v1.1)
- 🔄 真实LLM集成
- 🔄 提示词优化
- 🔄 质量评估增强
- 🔄 并行处理支持

### 未来版本
- 📋 Web界面
- 📋 插件系统
- 📋 多语言支持
- 📋 云端部署

## 🤝 贡献指南

欢迎贡献代码和建议！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
