#!/usr/bin/env python3
"""
Test script for enhanced BusinessAnalyzerAgent
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_dev_agents.business_analyzer import BusinessAnalyzerAgent, AnalysisContext
from ai_dev_agents.base_agent import WorkflowContext
from ai_dev_agents.config_manager import ConfigManager

def test_enhanced_business_analyzer():
    """Test the enhanced business analyzer with various scenarios."""
    print("=== Testing Enhanced Business Analyzer ===")
    
    # Initialize config and agent
    config_manager = ConfigManager("config.yaml")

    # Create LLM instance directly
    llm = config_manager.create_llm()
    
    # Initialize enhanced agent
    agent = BusinessAnalyzerAgent(llm=llm, verbose=True, quality_threshold=0.7)
    
    # Test PRD content
    test_prd = """
# MCP Server Market Platform

## 项目概述
MCP Server Market Platform 是一个集中化的 Model Context Protocol 服务器市场平台，旨在为开发者提供发现、分享和管理 MCP 服务器的统一平台。

## 目标用户
- 开发者：寻找和使用 MCP 服务器
- 服务器作者：发布和管理自己的 MCP 服务器
- 平台管理员：维护平台运营

## 核心功能

### 1. 服务器发现
用户可以浏览和搜索可用的 MCP 服务器，支持按类别、标签、评分等多维度筛选。

### 2. 服务器提交
认证用户可以提交自己开发的 MCP 服务器，包括元数据、文档、版本管理等。

### 3. 用户管理
支持用户注册、登录、个人资料管理、权限控制等。

### 4. 评价系统
用户可以对使用过的服务器进行评价和评分，帮助其他用户选择。

## 业务规则
- 只有认证用户才能提交服务器
- 服务器必须通过基本验证才能发布
- 用户只能修改自己提交的服务器
- 管理员可以审核和管理所有内容

## 技术要求
- 支持高并发访问（1000+ 用户）
- API 响应时间 < 200ms
- 数据安全和隐私保护
- 支持多种认证方式
"""
    
    # Create workflow context
    workflow_context = WorkflowContext(
        project_root=".",
        project_rules={},
        existing_modules=["auth", "user"],
        tech_stack=["FastAPI", "SQLAlchemy"],
        architecture_style="DDD"
    )

    # Test scenarios
    test_scenarios = [
        {
            "name": "基础分析测试",
            "input_data": {"prd_content": test_prd},
            "context": workflow_context
        },
        {
            "name": "高质量分析测试",
            "input_data": {
                "prd_content": test_prd,
                "analysis_depth": "comprehensive",
                "document_type": "PRD"
            },
            "context": workflow_context
        },
        {
            "name": "聚焦分析测试",
            "input_data": {
                "prd_content": test_prd,
                "focus_areas": ["用户管理", "服务器管理", "安全性"],
                "analysis_depth": "focused"
            },
            "context": workflow_context
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n--- 测试场景 {i}: {scenario['name']} ---")
        
        try:
            result = agent.process(scenario["input_data"], scenario["context"])
            
            if result.success:
                print("✓ 分析成功")
                print(f"  执行时间: {result.execution_time:.2f}s")
                
                # Print metadata
                metadata = result.metadata
                print(f"  分析上下文: {metadata.get('analysis_context', {})}")
                print(f"  质量指标: {metadata.get('quality_metrics', {})}")
                print(f"  文档分析: {metadata.get('document_analysis', {})}")
                print(f"  分析轮次: {metadata.get('analysis_passes', 1)}")
                
                # Print data summary
                data = result.data
                print(f"  业务实体数量: {len(data.get('core_entities', []))}")
                print(f"  功能需求数量: {len(data.get('functional_requirements', []))}")
                print(f"  用户故事数量: {len(data.get('user_stories', []))}")
                print(f"  业务规则数量: {len(data.get('business_rules', []))}")
                
                # Print insights if available
                insights = data.get('analysis_insights', {})
                if insights:
                    print(f"  领域复杂度: {insights.get('domain_complexity', 'N/A')}")
                    print(f"  实现挑战: {len(insights.get('implementation_challenges', []))}")
                    print(f"  业务风险: {len(insights.get('business_risks', []))}")
                
                # Print recommendations
                recommendations = data.get('recommendations', [])
                if recommendations:
                    print(f"  建议数量: {len(recommendations)}")
                    for rec in recommendations[:2]:  # Show first 2 recommendations
                        print(f"    - {rec}")
                
            else:
                print("✗ 分析失败")
                print(f"  错误: {result.errors}")
                if hasattr(result, 'warnings') and result.warnings:
                    print(f"  警告: {result.warnings}")
                
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_enhanced_business_analyzer()
