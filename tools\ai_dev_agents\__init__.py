"""
AI Development Agents Package

A comprehensive toolkit for AI-assisted software development using LangChain agents.
"""

# Core framework
from .core.base_agent import BaseAgent, AgentResult, WorkflowContext
from .core.orchestrator import AIDevWorkflowOrchestrator

# Individual agents
from .agents.business_analyzer import BusinessAnalyzerAgent
from .agents.domain_modeler import DomainModelerAgent
from .agents.requirements_generator import RequirementsGeneratorAgent
from .agents.prompt_builder import PromptBuilderAgent

# Utilities
from .utils.config_manager import ConfigManager

__version__ = "1.0.0"
__author__ = "AI Development Team"

__all__ = [
    # Core framework
    "BaseAgent",
    "AgentResult",
    "WorkflowContext",
    "AIDevWorkflowOrchestrator",

    # Individual agents
    "BusinessAnalyzerAgent",
    "DomainModelerAgent",
    "RequirementsGeneratorAgent",
    "PromptBuilderAgent",

    # Utilities
    "ConfigManager"
]
