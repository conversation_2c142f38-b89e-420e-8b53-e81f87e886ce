```markdown
# AI Development Task Prompt

Generated on: 2023-11-15

## Task Description

### Development Task
Implement a user management module with the following capabilities:
1. User account creation (POST /api/v1/users)
2. User profile retrieval (GET /api/v1/users/{user_id})

### Expected Deliverables
1. Complete FastAPI implementation with:
   - Route handlers
   - Service layer
   - Repository layer
   - Domain models
2. Database migration scripts
3. Unit and integration tests
4. API documentation

### Success Criteria
- Fully functional endpoints meeting requirements
- Properly layered DDD architecture
- Passing CI/CD pipeline with test coverage >80%

## Project Context

### Architecture Style
- Domain-Driven Design (4-layer architecture)
- REST API with FastAPI

### Technology Stack
- Web Framework: FastAPI
- ORM: SQLAlchemy 2.0
- Validation: Pydantic v2
- Migrations: Alembic
- Testing: Pytest

### Coding Standards
- PEP 8 compliance
- Type hints for all functions/methods
- English comments and docstrings
- 100-character line length limit
- Black code formatting

### Existing Modules
1. auth: Handles authentication flows
2. oauth_provider: Manages OAuth integrations
3. user: (Current module) User management

## Architectural Constraints (MUST FOLLOW)

1. **DDD Layer Rules**:
   - Presentation → Application → Domain → Infrastructure
   - No upward dependencies between layers

2. **Entity Requirements**:
   - All domain entities must use UUID primary keys
   - Entities must be pure Python classes (no DB dependencies)

3. **API Contracts**:
   - Strict input validation with Pydantic models
   - Consistent error response format

4. **Database**:
   - Repository pattern implementation
   - SQLAlchemy async session management

## Implementation Guidelines

### Development Steps
1. Create domain models (User aggregate root)
2. Implement repository interface
3. Create SQLAlchemy repository implementation
4. Develop service layer
5. Add API route handlers
6. Write database migrations
7. Implement tests (unit → integration)

### Code Organization
```
user/
├── domain/
│   ├── models.py       # Domain entities
│   └── exceptions.py   # Domain exceptions
├── application/
│   ├── services.py     # Business logic
│   └── dtos.py         # Data transfer objects
├── infrastructure/
│   ├── repositories/   # DB implementations
│   └── database.py     # DB setup
├── presentation/
│   ├── routes.py       # FastAPI routes
│   └── schemas.py      # Pydantic models
└── tests/
```

### Best Practices
- Use dependency injection for all external services
- Keep business logic in domain layer
- Write idempotent operations where possible
- Follow RESTful resource naming conventions

## Quality Requirements

### Code Quality
- Pylint score > 9.0/10
- MyPy type checking with strict mode
- No code duplication (checked by SonarQube)

### Testing
- 80%+ test coverage (measured by pytest-cov)
- Include:
  - Unit tests for domain logic
  - Integration tests for API endpoints
  - Repository contract tests

### Documentation
- Complete API docs with Swagger/OpenAPI
- Module-level docstrings
- All public methods documented
- ADR for architectural decisions

## Acceptance Criteria

### Functional
1. POST /users creates new user account
   - Validates required fields
   - Returns 201 with user data
2. GET /users/{id} retrieves user profile
   - Returns 404 for non-existent users
   - Returns 200 with user data

### Technical
1. Proper DDD layer separation
2. Async database operations
3. Proper error handling
4. Complete type hints

### Quality
1. Passing CI pipeline
2. Meeting coverage requirements
3. Documentation complete

## Reference Examples

### Existing Patterns
1. auth module structure:
   - Clear layer separation
   - JWT service implementation
2. oauth_provider:
   - Repository pattern example
   - Async DB operations

### Architecture Reference
```python
# Domain model example
class User:
    id: UUID
    email: str
    is_active: bool

# Repository interface
class UserRepository(Protocol):
    async def get(self, id: UUID) -> User: ...
```

### Best Practices
1. FastAPI dependency injection:
```python
async def get_user_service() -> UserService:
    return UserService(...)

@router.get("/")
async def get_user(service: UserService = Depends(get_user_service)):
    ...
```

## Development Requirements

1. **Database**:
   - PostgreSQL 14+
   - Alembic migrations
   - Async SQLAlchemy 2.0

2. **API**:
   - FastAPI 0.95+
   - Pydantic v2 models
   - OpenAPI 3.1 docs

3. **Testing**:
   - pytest-asyncio
   - factory-boy for test data
   - pytest-mock

## Project Rules and Standards

1. **Code Structure**:
   - Strict DDD layer separation
   - No business logic in presentation layer
   - Repository pattern for persistence

2. **Quality Gates**:
   - Pre-commit hooks for:
     - Formatting (Black)
     - Linting (Flake8)
     - Type checking (MyPy)
   - CI pipeline must pass before merge

3. **Documentation**:
   - All public APIs documented
   - Module-level docstrings
   - Keep ADRs in docs/architecture
```