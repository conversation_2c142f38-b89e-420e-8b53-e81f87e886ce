{"module_overview": {"module_name": "mcp_server", "bounded_context": "MCP Server Management", "primary_responsibility": "Manage MCP servers and their tool definitions", "business_value": "Provides centralized management of MCP servers and their capabilities", "core_capabilities": ["MCP server registration and management", "Tool definition management", "Quality metrics calculation"], "external_dependencies": ["oauth_provider", "auth", "user"]}, "user_stories": [{"id": "US-001", "title": "Register new MCP server", "story": "As a system administrator, I want to register a new MCP server so that it can be managed by the system", "acceptance_criteria": ["Given valid server details, when I submit the registration form, then the server should be created with a unique slug", "Given duplicate server slug, when I submit the registration, then I should receive an error"], "priority": "高", "story_points": 5, "business_rules": ["Server slug must be unique", "Server must have a name and author"]}, {"id": "US-002", "title": "Add tool definition to server", "story": "As a developer, I want to add tool definitions to an MCP server so that users can discover available tools", "acceptance_criteria": ["Given a valid MCP server, when I add a tool definition, then it should be associated with the server", "Given duplicate tool name, when I try to add it, then I should receive an error"], "priority": "高", "story_points": 3, "business_rules": ["Tool name must be unique per server", "Tool must have a description"]}], "api_design": {"base_path": "/api/v1/mcp", "endpoints": [{"method": "POST", "path": "/servers", "summary": "Create new MCP server", "description": "Register a new MCP server with the system", "request_schema": "MCPServerCreateRequest", "response_schema": "MCPServerResponse", "authentication_required": true, "authorization_roles": ["admin"], "business_logic": "Validates server slug uniqueness, creates server record"}, {"method": "GET", "path": "/servers", "summary": "List all MCP servers", "description": "Retrieve paginated list of all MCP servers", "request_schema": null, "response_schema": "MCPServerListResponse", "authentication_required": true, "authorization_roles": ["user", "admin"], "business_logic": "Returns paginated server list with optional filtering"}, {"method": "POST", "path": "/servers/{server_id}/tools", "summary": "Add tool definition", "description": "Add a new tool definition to an MCP server", "request_schema": "ToolDefinitionCreateRequest", "response_schema": "ToolDefinitionResponse", "authentication_required": true, "authorization_roles": ["admin", "developer"], "business_logic": "Validates tool name uniqueness, associates tool with server"}], "schemas": [{"name": "MCPServerCreateRequest", "type": "request", "fields": [{"name": "slug", "type": "string", "required": true, "description": "Unique identifier for the server", "validation_rules": ["min_length:3", "max_length:50", "regex:^[a-z0-9-]+$"]}, {"name": "name", "type": "string", "required": true, "description": "Human-readable server name", "validation_rules": ["min_length:3", "max_length:100"]}]}, {"name": "MCPServerResponse", "type": "response", "fields": [{"name": "id", "type": "UUID", "required": true, "description": "Server unique identifier"}, {"name": "slug", "type": "string", "required": true, "description": "Server slug"}, {"name": "name", "type": "string", "required": true, "description": "Server name"}, {"name": "author_id", "type": "UUID", "required": true, "description": "User who created the server"}]}, {"name": "ToolDefinitionCreateRequest", "type": "request", "fields": [{"name": "tool_name", "type": "string", "required": true, "description": "Unique name for the tool", "validation_rules": ["min_length:3", "max_length:100"]}, {"name": "description", "type": "string", "required": true, "description": "Tool description", "validation_rules": ["min_length:10", "max_length:500"]}]}]}, "data_models": {"tables": [{"name": "mcp_servers", "description": "Stores MCP server information", "columns": [{"name": "id", "type": "UUID", "constraints": ["PRIMARY KEY"], "description": "Primary key"}, {"name": "slug", "type": "VARCHAR(50)", "constraints": ["UNIQUE", "NOT NULL"], "description": "Unique server identifier"}, {"name": "name", "type": "VARCHAR(100)", "constraints": ["NOT NULL"], "description": "Server name"}, {"name": "author_id", "type": "UUID", "constraints": ["NOT NULL", "REFERENCES users(id)"], "description": "User who created the server"}, {"name": "created_at", "type": "TIMESTAMP", "constraints": ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"], "description": "Creation timestamp"}, {"name": "updated_at", "type": "TIMESTAMP", "constraints": ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"], "description": "Last update timestamp"}], "indexes": [{"name": "idx_mcp_servers_slug", "columns": ["slug"], "type": "btree", "unique": true}, {"name": "idx_mcp_servers_author", "columns": ["author_id"], "type": "btree", "unique": false}], "relationships": [{"type": "foreign_key", "target_table": "users", "columns": ["author_id"], "target_columns": ["id"]}]}, {"name": "tool_definitions", "description": "Stores tool definitions for MCP servers", "columns": [{"name": "id", "type": "UUID", "constraints": ["PRIMARY KEY"], "description": "Primary key"}, {"name": "tool_name", "type": "VARCHAR(100)", "constraints": ["NOT NULL"], "description": "Tool name"}, {"name": "description", "type": "TEXT", "constraints": ["NOT NULL"], "description": "Tool description"}, {"name": "server_id", "type": "UUID", "constraints": ["NOT NULL", "REFERENCES mcp_servers(id)"], "description": "Associated MCP server"}, {"name": "created_at", "type": "TIMESTAMP", "constraints": ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"], "description": "Creation timestamp"}], "indexes": [{"name": "idx_tool_definitions_server_tool", "columns": ["server_id", "tool_name"], "type": "btree", "unique": true}], "relationships": [{"type": "foreign_key", "target_table": "mcp_servers", "columns": ["server_id"], "target_columns": ["id"]}]}]}, "business_logic": {"domain_entities": [{"name": "MCPServer", "aggregate": "MCP_Server", "attributes": [{"name": "id", "type": "UUID", "validation_rules": ["not_null"]}, {"name": "slug", "type": "string", "validation_rules": ["unique", "length:3-50", "alphanumeric_dash"]}, {"name": "name", "type": "string", "validation_rules": ["not_null", "length:3-100"]}, {"name": "author_id", "type": "UUID", "validation_rules": ["not_null", "valid_user"]}], "business_methods": [{"name": "register_tool", "description": "Adds a new tool definition to the server", "parameters": ["tool_name", "description"], "business_rules": ["Tool name must be unique for this server", "Description must be provided"], "exceptions": ["DuplicateToolError", "InvalidToolDefinitionError"]}], "invariants": ["Server must have a unique slug", "Server must have an author"]}, {"name": "ToolDefinition", "aggregate": "MCP_Server", "attributes": [{"name": "id", "type": "UUID", "validation_rules": ["not_null"]}, {"name": "tool_name", "type": "string", "validation_rules": ["not_null", "length:3-100"]}, {"name": "description", "type": "string", "validation_rules": ["not_null", "length:10-500"]}, {"name": "server_id", "type": "UUID", "validation_rules": ["not_null", "valid_server"]}], "business_methods": [], "invariants": ["Tool must belong to a server", "Tool name must be unique within its server"]}], "application_services": [{"name": "MCPServerService", "description": "Handles MCP server management operations", "methods": [{"name": "create_server", "description": "Creates a new MCP server", "use_case": "Server registration", "transaction_boundary": true, "dependencies": ["MCPServerRepository", "UserRepository"]}, {"name": "add_tool_definition", "description": "Adds a tool definition to a server", "use_case": "Tool management", "transaction_boundary": true, "dependencies": ["MCPServerRepository", "ToolDefinitionRepository"]}]}], "repositories": [{"name": "MCPServerRepository", "entity": "MCPServer", "interface_methods": [{"name": "get_by_id", "description": "Retrieve server by ID", "parameters": ["server_id"], "return_type": "Optional[MCPServer]"}, {"name": "get_by_slug", "description": "Retrieve server by slug", "parameters": ["slug"], "return_type": "Optional[MCPServer]"}, {"name": "save", "description": "Save server entity", "parameters": ["server"], "return_type": "None"}]}, {"name": "ToolDefinitionRepository", "entity": "ToolDefinition", "interface_methods": [{"name": "get_by_server_and_name", "description": "Get tool by server and name", "parameters": ["server_id", "tool_name"], "return_type": "Optional[ToolDefinition]"}, {"name": "save", "description": "Save tool definition", "parameters": ["tool_definition"], "return_type": "None"}]}]}, "testing_strategy": {"unit_tests": [{"target": "MCPServer entity", "test_cases": [{"name": "should_raise_error_when_creating_with_duplicate_slug", "description": "Test server creation with duplicate slug", "given": "Existing server with slug 'test-server'", "when": "Creating new server with same slug", "then": "Should raise DuplicateSlugError"}, {"name": "should_successfully_add_tool_when_name_is_unique", "description": "Test adding unique tool to server", "given": "Valid server instance", "when": "Adding tool with unique name", "then": "Tool should be added successfully"}]}, {"target": "MCPServerService", "test_cases": [{"name": "should_create_server_when_input_valid", "description": "Test server creation service", "given": "Valid server creation data", "when": "Calling create_server service", "then": "Should return created server"}]}], "integration_tests": [{"scope": "Server registration flow", "scenarios": [{"name": "successful_server_registration", "description": "Complete server registration flow", "test_data": "Valid server data, authenticated admin user", "expected_outcome": "Server created in database, correct response returned"}]}, {"scope": "Tool management flow", "scenarios": [{"name": "add_tool_to_server", "description": "Complete tool addition flow", "test_data": "Existing server, valid tool data, authenticated developer", "expected_outcome": "Tool added to server in database"}]}], "api_tests": [{"endpoint": "/api/v1/mcp/servers", "test_cases": [{"name": "create_server_unauthorized", "method": "POST", "request_data": "Valid server data", "expected_status": 403, "expected_response": "Permission denied"}, {"name": "create_server_success", "method": "POST", "request_data": "Valid server data", "expected_status": 201, "expected_response": "Server details with ID"}]}]}, "technical_constraints": {"architecture_constraints": ["Strict DDD four-layer architecture", "Domain layer must not depend on infrastructure", "UUID primary keys for all entities", "Pydantic models for API schemas"], "performance_requirements": ["API response time < 300ms for all endpoints", "Database queries must use indexes effectively", "Pagination for list endpoints"], "security_requirements": ["JWT authentication required for all endpoints", "Role-based authorization", "Input validation for all API parameters"], "quality_requirements": ["Code coverage > 85%", "PEP 8 compliance", "Type hints for all Python code", "Comprehensive API documentation"]}, "implementation_order": ["1. Domain layer - MCPServer and ToolDefinition entities", "2. Domain layer - Repository interfaces", "3. Infrastructure layer - SQLAlchemy models and repository implementations", "4. Application layer - MCPServerService", "5. Interface layer - API endpoints and Pydantic models", "6. Database migrations (Alembic)", "7. Unit tests for domain and application layers", "8. Integration tests", "9. API tests", "10. Documentation"], "project_metadata": {"generation_timestamp": "2025-06-25T11:11:34.525697", "architecture_style": "DDD + FastAPI", "tech_stack": ["FastAPI", "SQLAlchemy", "Pydantic", "Alembic", "<PERSON><PERSON><PERSON>"], "existing_modules": ["oauth_provider", "auth", "user"]}, "dependency_analysis": {"internal_dependencies": [], "external_dependencies": ["oauth_provider", "auth", "user"], "cross_module_dependencies": ["oauth_provider", "auth", "user"]}, "complexity_estimation": {"story_points_total": 8, "api_endpoints_count": 3, "database_tables_count": 2, "domain_entities_count": 2, "estimated_development_days": 4.0}}