```markdown
# AI Development Task Prompt

Generated on: 2025-06-25 10:18:05

## Task Description

### Development Task
Implement a user management module (`user_management`) that handles:
1. User registration (US-001)
2. User profile management (US-002)

### Expected Deliverables
1. FastAPI endpoints:
   - POST /api/v1/users (registration)
   - GET /api/v1/users/{user_id} (profile retrieval)
   - PUT /api/v1/users/{user_id} (profile update)
2. SQLAlchemy data model for `users` table
3. Pydantic schemas for request/response validation
4. Complete test suite with >80% coverage
5. Alembic migration scripts

### Success Criteria
- All endpoints work as specified
- Proper input validation and error handling
- Secure password storage
- Follows DDD principles
- Meets all quality requirements

## Project Context

### Architecture Style
- Domain-Driven Design (4-layer architecture)
- Clean Architecture principles
- REST API with FastAPI

### Technology Stack
- Web Framework: FastAPI
- ORM: SQLAlchemy 2.0
- Validation: Pydantic v2
- Database Migrations: Alembic
- Testing: Pytest + HTTPX

### Coding Standards
- PEP 8 compliance
- Type hints for all functions/methods
- English comments and docstrings
- 120 character line length
- Black code formatting

### Existing Modules
1. `auth` module (reference for JWT handling)
2. `oauth_provider` module (reference for external auth integration)
3. `user` module (legacy - use as anti-pattern reference)

## Architectural Constraints (MUST FOLLOW)

1. **DDD Layer Structure**:
   - Presentation (FastAPI routes)
   - Application (use cases/services)
   - Domain (models/value objects)
   - Infrastructure (repositories/DB)

2. **Dependency Direction**:
   - Presentation → Application → Domain
   - Infrastructure → Domain
   - No circular dependencies

3. **Module Boundaries**:
   - User domain logic isolated from auth concerns
   - DB operations only in infrastructure layer
   - No direct DB access from presentation layer

4. **Security Requirements**:
   - Password hashing with Argon2
   - Input validation for all endpoints
   - No sensitive data in responses

## Implementation Guidelines

### Development Sequence
1. Implement domain models (User entity, value objects)
2. Create Pydantic schemas (request/response models)
3. Implement repository interface
4. Create SQLAlchemy model and repository implementation
5. Write application services
6. Implement FastAPI routes
7. Write migration scripts
8. Implement tests

### Code Organization
```
user_management/
├── domain/            # Pure domain models
│   ├── entities.py
│   └── value_objects.py
├── application/       # Use cases/services
│   ├── services.py
│   └── interfaces.py  # Repository interfaces
├── infrastructure/
│   ├── repositories/  # DB implementations
│   └── models.py      # SQLAlchemy models
├── presentation/
│   ├── schemas.py     # Pydantic models
│   └── routes.py      # FastAPI endpoints
└── tests/
```

### Best Practices
- Use dependency injection for services
- Repository pattern for data access
- Unit of Work pattern for transactions
- DTOs for API communication
- Hexagonal architecture principles

## Quality Requirements

### Code Quality
- Pylint score > 9.0
- MyPy type checking with strict mode
- No code duplication (SonarQube check)

### Testing
- 80%+ test coverage (measured by pytest-cov)
- Test pyramid distribution:
  - 70% unit tests
  - 20% integration tests
  - 10% e2e tests
- All edge cases covered

### Documentation
- OpenAPI spec auto-generated
- Module-level docstrings
- All public methods documented
- Decision records for architectural choices

## Acceptance Criteria

### Functional
- [ ] User can register with valid credentials
- [ ] User can retrieve their profile
- [ ] User can update their profile
- [ ] Invalid inputs are properly rejected
- [ ] Proper error messages for all failure cases

### Technical
- [ ] Follows DDD layers correctly
- [ ] Proper separation of concerns
- [ ] All dependencies properly injected
- [ ] Async database operations
- [ ] Configurable through environment variables

### Quality
- [ ] 80%+ test coverage
- [ ] Passing CI pipeline
- [ ] Proper logging
- [ ] Performance benchmarks
- [ ] Security audit passed

## Reference Examples

### Code Patterns
1. Repository pattern (from `auth` module):
```python
class UserRepository(Protocol):
    async def get_by_email(self, email: str) -> User | None: ...
```

2. Service layer (from `oauth_provider` module):
```python
class OAuthService:
    def __init__(self, repo: UserRepository):
        self._repo = repo
```

### Architecture
- See `auth` module for DDD layer implementation
- Use `pydantic.BaseModel` for schemas (not SQLAlchemy models directly)

### Best Practices
- Environment config management (like in `auth` module)
- Error handling middleware pattern
- Test factories for domain objects

## Development Requirements

### Detailed Requirements
1. User Entity:
   - UUID primary key
   - Email (unique)
   - Hashed password
   - Profile fields (name, etc.)
   - Timestamps (created_at, updated_at)

2. Registration:
   - Email validation
   - Password strength check
   - Conflict handling

3. Profile Management:
   - Partial updates supported
   - Audit logging
   - Permission checks

## Project Rules and Standards

1. **General**:
   - No business logic in presentation layer
   - No raw SQL in application layer
   - All exceptions properly handled

2. **Security**:
   - Never log sensitive data
   - Rate limiting on auth endpoints
   - CSRF protection for state-changing ops

3. **Performance**:
   - N+1 query prevention
   - Pagination for large results
   - Async I/O operations

4. **Testing**:
   - Fixtures for common test data
   - Factory pattern for test objects
   - Mock external services
```