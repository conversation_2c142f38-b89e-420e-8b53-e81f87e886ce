```markdown
# AI Development Task Prompt

Generated on: 2023-11-15

## Task Description

### Development Task
Implement a `category` module for managing server categories with the following capabilities:
1. Create new server categories (POST /api/v1/categories)
2. List all available categories (GET /api/v1/categories)

### Expected Deliverables
1. Complete DDD implementation of category module
2. REST API endpoints with proper request/response validation
3. Database migration scripts
4. Unit and integration tests
5. API documentation

### Success Criteria
- Fully functional category management API
- Compliance with project architecture and quality standards
- Passing CI/CD pipeline with test coverage >80%

## Project Context

### Architecture Style
Domain-Driven Design (DDD) with 4-layer architecture:
1. Presentation (FastAPI routes)
2. Application (use cases)
3. Domain (business logic)
4. Infrastructure (persistence)

### Technology Stack
- Web Framework: FastAPI
- ORM: SQLAlchemy 2.0
- Validation: Pydantic v2
- Migrations: Alembic
- Testing: Pytest

### Coding Standards
- PEP 8 compliance
- Type hints for all functions
- English-only comments/docstrings
- UUID primary keys
- Async/await pattern for I/O operations

### Existing Modules
1. `user` module reference patterns:
   - DDD layer separation
   - SQLAlchemy repository pattern
   - Pydantic schemas for API contracts
2. `auth` module reference patterns:
   - FastAPI dependency injection
   - Route protection decorators
3. `oauth_provider` module reference patterns:
   - External service integration pattern
   - Configuration management

## Architectural Constraints (MUST FOLLOW)

1. **DDD Layer Rules**:
   - Presentation layer must not contain business logic
   - Domain layer must be framework-agnostic
   - Infrastructure depends on domain, not vice versa

2. **Dependency Direction**:
   - Presentation → Application → Domain ← Infrastructure
   - No circular dependencies allowed

3. **Module Boundaries**:
   - Clear separation between category and other modules
   - Shared kernel limited to common types and utilities

4. **Persistence Requirements**:
   - SQLAlchemy for database access
   - Alembic for migrations
   - Repository pattern implementation

## Implementation Guidelines

### Development Steps
1. Set up module structure following DDD layers
2. Implement domain model (Category aggregate root)
3. Create SQLAlchemy models and migrations
4. Implement repository interface and implementation
5. Create application services (use cases)
6. Implement FastAPI routes
7. Write unit and integration tests
8. Document API with OpenAPI

### Code Organization
```
category/
├── __init__.py
├── application/
│   ├── commands.py
│   ├── queries.py
│   └── services.py
├── domain/
│   ├── entities.py
│   ├── exceptions.py
│   └── value_objects.py
├── infrastructure/
│   ├── models.py
│   ├── repositories.py
│   └── database.py
└── presentation/
    ├── schemas.py
    ├── dependencies.py
    └── routes.py
```

### Best Practices
- Use dependency injection for all external dependencies
- Implement proper error handling with domain exceptions
- Follow RESTful API design principles
- Use async/await for all I/O operations
- Validate all inputs with Pydantic

## Quality Requirements

### Code Quality
- Pylint score > 9.0
- MyPy type checking with strict mode
- No code duplication (checked by SonarQube)

### Testing
- Unit test coverage > 80%
- Integration test coverage > 70%
- All business rules must have corresponding tests
- Test pyramid compliance (70/20/10 ratio)

### Documentation
- Complete docstrings for all public methods
- API documentation via FastAPI's OpenAPI
- README.md with module overview
- Decision records for architectural choices

## Acceptance Criteria

### Functional
- [ ] POST /categories creates new category
- [ ] GET /categories returns all categories
- [ ] Proper validation for all inputs
- [ ] Proper error responses for invalid requests

### Technical
- [ ] Proper DDD layer separation
- [ ] Async repository implementation
- [ ] Complete type hints
- [ ] Passing CI/CD pipeline

### Quality
- [ ] Test coverage metrics met
- [ ] No critical code smells
- [ ] Documentation complete

## Reference Examples

### Code Patterns
1. User module's repository pattern:
```python
class UserRepository(Protocol):
    async def get(self, user_id: UUID) -> User: ...
```

2. Auth module's route protection:
```python
@router.get("/protected")
async def protected_route(user: User = Depends(get_current_user)):
    ...
```

### Architecture
- Domain event pattern from user module
- Repository pattern from auth module
- Pydantic schema organization from oauth_provider

### Best Practices
- FastAPI dependency injection
- SQLAlchemy async session management
- Pytest fixtures for test data

## Development Requirements

### Detailed Requirements
1. Category entity must have:
   - UUID id
   - name (str, unique)
   - description (optional str)
   - created_at timestamp
   - updated_at timestamp

2. API endpoints must:
   - Use proper HTTP status codes
   - Include request/response validation
   - Support standard filtering/pagination

3. Repository must:
   - Implement async interface
   - Use SQLAlchemy 2.0 style
   - Include proper session management

## Project Rules and Standards

1. **Architecture**:
   - Strict DDD layer separation
   - No business logic in presentation
   - Framework-agnostic domain layer

2. **Code Quality**:
   - Type hints mandatory
   - PEP 8 compliance
   - English documentation

3. **Testing**:
   - Test coverage minimums
   - Test pyramid adherence
   - Mutation testing for critical paths

4. **Security**:
   - Input validation
   - Output sanitization
   - Proper error handling
```