```markdown
# AI Development Task Prompt

Generated on: 2023-11-15

## Task Description

### Development Task
Implement the `mcp_server` module to manage MCP servers and their tool definitions with RESTful APIs. The module should:
1. Allow registration of new MCP servers
2. Enable listing all available MCP servers
3. Support adding tool definitions to specific MCP servers

### Expected Deliverables
- Complete FastAPI implementation with 3 endpoints
- SQLAlchemy models for 2 data tables
- Pydantic schemas for request/response validation
- Alembic migration scripts
- Unit tests with >80% coverage
- API documentation in OpenAPI format

### Success Criteria
- All user stories (US-MCP-001, US-MCP-002) fully implemented
- APIs comply with REST standards
- Integration with existing auth system
- Passing CI/CD pipeline

## Project Context

### Architecture Style
- Domain-Driven Design (4-layer architecture)
- REST API with FastAPI
- Repository pattern for data access

### Technology Stack
- Python 3.10+
- FastAPI (Web framework)
- SQLAlchemy 2.0 (ORM)
- Pydantic (Data validation)
- Alembic (Migrations)
- Pytest (Testing)
- PostgreSQL (Database)

### Coding Standards
- PEP 8 compliance
- Type hints for all functions/methods
- Google-style docstrings
- English-only comments/documentation
- UUID primary keys for all entities

### Existing Modules
1. `user` module (reference for auth integration)
   - JWT authentication
   - User management
2. `auth` module (for dependency injection)
   - Current user dependency
   - Permission checks
3. `oauth_provider` module
   - OAuth2 integration
   - Token handling

## Architectural Constraints (MUST FOLLOW)

1. **DDD Layer Compliance**
   - Presentation: FastAPI routers
   - Application: Service classes
   - Domain: Models and business logic
   - Infrastructure: Repositories and DB operations

2. **Dependency Direction**
   - Presentation → Application → Domain ← Infrastructure
   - No circular dependencies allowed

3. **Module Boundaries**
   - Clear separation between:
     - Server management
     - Tool definition management
   - Shared kernel for common types/utilities

4. **API Standards**
   - RESTful endpoints
   - JSON request/response
   - Standard HTTP status codes
   - Versioned API paths (/api/v1/)

## Implementation Guidelines

### Development Sequence
1. Set up module structure
2. Implement domain models
3. Create database migrations
4. Develop repository interfaces
5. Implement service layer
6. Create API endpoints
7. Write unit tests
8. Document APIs

### Code Organization
```
mcp_server/
├── __init__.py
├── domain/
│   ├── models.py         # Domain models
│   └── exceptions.py     # Domain exceptions
├── application/
│   ├── services.py       # Business logic
│   └── schemas.py        # Pydantic schemas
├── infrastructure/
│   ├── repositories.py   # DB operations
│   └── database.py       # DB session handling
├── presentation/
│   ├── routers.py        # FastAPI routers
│   └── dependencies.py   # API dependencies
├── migrations/           # Alembic scripts
└── tests/
    ├── unit/             # Unit tests
    └── integration/      # Integration tests
```

### Best Practices
- Use dependency injection for services
- Validate all inputs with Pydantic
- Implement proper error handling
- Follow repository pattern for DB access
- Use async/await for I/O operations
- Cache frequently accessed data

## Quality Requirements

### Code Quality
- Pylint score > 9.0/10
- MyPy type checking with strict mode
- No code duplication (checked by SonarQube)

### Testing
- 80%+ test coverage (measured by pytest-cov)
- Unit tests for all domain logic
- Integration tests for API endpoints
- Test all error scenarios

### Documentation
- Complete API docs via FastAPI autogen
- Module-level docstrings
- README.md with setup instructions
- Architecture decision records (ADRs)

## Acceptance Criteria

### Functional
- [ ] POST /servers creates new MCP server
- [ ] GET /servers returns all registered servers
- [ ] POST /servers/{server_id}/tools adds tool definition
- [ ] All endpoints validate inputs
- [ ] Proper error responses for invalid requests

### Technical
- [ ] DDD layers properly separated
- [ ] Async database operations
- [ ] JWT authentication integrated
- [ ] Alembic migrations working
- [ ] Passes all CI/CD checks

### Quality
- [ ] 80%+ test coverage
- [ ] No critical SonarQube issues
- [ ] API docs generated
- [ ] Type checking passes

## Reference Examples

### Code Patterns
1. User module's auth integration:
```python
# presentation/dependencies.py
async def get_current_user(token: str = Depends(oauth2_scheme)):
    # JWT validation logic
```

2. Repository pattern (from auth module):
```python
# infrastructure/repositories.py
class UserRepository:
    async def get_by_id(self, user_id: UUID) -> User:
        # DB query implementation
```

### Architecture
- See `user` module's 4-layer structure
- Follow same dependency injection pattern

### Best Practices
- Use Pydantic models for all API schemas
- Follow FastAPI's async patterns
- Repository pattern for database access

## Development Requirements

### Detailed Requirements
1. **Domain Models**
   - McpServer: id(UUID), name(str), url(str), description(str)
   - ToolDefinition: id(UUID), name(str), version(str), server_id(UUID)

2. **API Specifications**
   - POST /servers: 201 Created with Location header
   - GET /servers: 200 OK with server list
   - POST /servers/{server_id}/tools: 201 Created

3. **Error Handling**
   - 400 for invalid requests
   - 404 for non-existent resources
   - 409 for conflicts

## Project Rules and Standards

### Mandatory Rules
1. **Code Organization**
   - Strict DDD layer separation
   - No business logic in presentation layer
   - No direct DB access outside repositories

2. **Security**
   - All endpoints require authentication
   - Input validation for all parameters
   - No sensitive data in logs

3. **Testing**
   - Mock external dependencies
   - Test both happy and error paths
   - Integration tests for API contracts

4. **Documentation**
   - Swagger UI must show all endpoints
   - All public methods need docstrings
   - Keep CHANGELOG.md updated
```