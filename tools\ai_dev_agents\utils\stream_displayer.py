"""
Stream Displayer for real-time LLM output visualization.

This module provides real-time streaming display capabilities for LLM interactions,
including thinking process visualization and formatted output display.
"""

import re
import sys
import time
from typing import Optional, Dict, Any
from enum import Enum


class ContentType(Enum):
    """Content type enumeration for different display styles."""
    THINKING = "thinking"
    OUTPUT = "output"
    ERROR = "error"
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"


class StreamDisplayer:
    """
    Real-time stream displayer for LLM interactions.
    
    Provides formatted, colored terminal output for different types of content
    including thinking processes, final outputs, and status messages.
    """
    
    # ANSI color codes
    COLORS = {
        'reset': '\033[0m',
        'bold': '\033[1m',
        'dim': '\033[2m',
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'magenta': '\033[95m',
        'cyan': '\033[96m',
        'white': '\033[97m',
        'gray': '\033[90m'
    }
    
    # Box drawing characters
    BOX_CHARS = {
        'top_left': '┌',
        'top_right': '┐',
        'bottom_left': '└',
        'bottom_right': '┘',
        'horizontal': '─',
        'vertical': '│',
        'cross': '┼'
    }
    
    def __init__(self, enabled: bool = True, show_thinking: bool = True):
        """
        Initialize the stream displayer.
        
        Args:
            enabled: Whether to enable streaming display
            show_thinking: Whether to show thinking processes
        """
        self.enabled = enabled
        self.show_thinking = show_thinking
        self.current_content = ""
        self.in_thinking = False
        self.thinking_content = ""
        self.output_content = ""
        
    def start_agent_session(self, agent_name: str, task_description: str):
        """Start a new agent session display."""
        if not self.enabled:
            return
            
        self._print_colored(f"\n🤖 {agent_name} 开始工作...", 'cyan', bold=True)
        self._print_colored(f"📋 任务: {task_description}", 'blue')
        print()
        
    def process_stream_chunk(self, chunk: str) -> str:
        """
        Process a streaming chunk and display it appropriately.
        
        Args:
            chunk: The streaming text chunk
            
        Returns:
            The accumulated content so far
        """
        if not self.enabled:
            self.current_content += chunk
            return self.current_content
            
        self.current_content += chunk
        
        # Check for thinking tags
        if '<thinking>' in chunk:
            self.in_thinking = True
            if self.show_thinking:
                self._start_thinking_display()
            return self.current_content
            
        if '</thinking>' in chunk:
            self.in_thinking = False
            if self.show_thinking:
                self._end_thinking_display()
            return self.current_content
            
        # Display content based on current state
        if self.in_thinking:
            if self.show_thinking:
                self._display_thinking_chunk(chunk)
            self.thinking_content += chunk
        else:
            self._display_output_chunk(chunk)
            self.output_content += chunk
            
        return self.current_content
        
    def _start_thinking_display(self):
        """Start displaying thinking process."""
        title = " 推理过程 "
        border_length = 50
        padding = (border_length - len(title)) // 2
        
        print(f"{self.COLORS['yellow']}{self.BOX_CHARS['top_left']}{self.BOX_CHARS['horizontal'] * padding}{title}{self.BOX_CHARS['horizontal'] * (border_length - padding - len(title))}{self.COLORS['reset']}")
        
    def _end_thinking_display(self):
        """End thinking process display."""
        border_length = 50
        print(f"{self.COLORS['yellow']}{self.BOX_CHARS['bottom_left']}{self.BOX_CHARS['horizontal'] * (border_length - 1)}{self.COLORS['reset']}")
        print()
        
    def _display_thinking_chunk(self, chunk: str):
        """Display a thinking process chunk."""
        # Remove thinking tags from display
        clean_chunk = re.sub(r'</?thinking>', '', chunk)
        if clean_chunk.strip():
            lines = clean_chunk.split('\n')
            for line in lines:
                if line.strip():
                    print(f"{self.COLORS['yellow']}{self.BOX_CHARS['vertical']} {self.COLORS['dim']}{line}{self.COLORS['reset']}")
                    
    def _display_output_chunk(self, chunk: str):
        """Display an output chunk."""
        if chunk.strip():
            # Don't display raw JSON chunks in real-time to avoid clutter
            # Just show a progress indicator
            sys.stdout.write('.')
            sys.stdout.flush()
            
    def complete_agent_session(self, success: bool, result_summary: Optional[str] = None):
        """Complete the agent session display."""
        if not self.enabled:
            return
            
        print()  # New line after progress dots
        
        if success:
            self._print_colored("✅ 处理完成", 'green', bold=True)
            if result_summary:
                self._print_colored(f"📊 结果: {result_summary}", 'green')
        else:
            self._print_colored("❌ 处理失败", 'red', bold=True)
            if result_summary:
                self._print_colored(f"⚠️ 错误: {result_summary}", 'red')
                
        print()
        
    def display_final_output(self, content: str, content_type: ContentType = ContentType.OUTPUT):
        """Display final formatted output."""
        if not self.enabled:
            return
            
        color = self._get_color_for_type(content_type)
        title = self._get_title_for_type(content_type)
        
        print(f"\n💭 {title}...")
        
        # Create bordered output display
        border_length = 60
        title_display = f" {title} "
        padding = (border_length - len(title_display)) // 2
        
        print(f"{color}{self.BOX_CHARS['top_left']}{self.BOX_CHARS['horizontal'] * padding}{title_display}{self.BOX_CHARS['horizontal'] * (border_length - padding - len(title_display))}{self.COLORS['reset']}")
        
        # Display content with proper formatting
        lines = content.split('\n')
        for line in lines:
            if len(line) > border_length - 4:
                # Wrap long lines
                wrapped_lines = self._wrap_text(line, border_length - 4)
                for wrapped_line in wrapped_lines:
                    print(f"{color}{self.BOX_CHARS['vertical']} {wrapped_line:<{border_length-4}} {self.BOX_CHARS['vertical']}{self.COLORS['reset']}")
            else:
                print(f"{color}{self.BOX_CHARS['vertical']} {line:<{border_length-4}} {self.BOX_CHARS['vertical']}{self.COLORS['reset']}")
                
        print(f"{color}{self.BOX_CHARS['bottom_left']}{self.BOX_CHARS['horizontal'] * (border_length - 1)}{self.COLORS['reset']}")
        
    def _get_color_for_type(self, content_type: ContentType) -> str:
        """Get color for content type."""
        color_map = {
            ContentType.THINKING: self.COLORS['yellow'],
            ContentType.OUTPUT: self.COLORS['cyan'],
            ContentType.ERROR: self.COLORS['red'],
            ContentType.INFO: self.COLORS['blue'],
            ContentType.SUCCESS: self.COLORS['green'],
            ContentType.WARNING: self.COLORS['yellow']
        }
        return color_map.get(content_type, self.COLORS['white'])
        
    def _get_title_for_type(self, content_type: ContentType) -> str:
        """Get title for content type."""
        title_map = {
            ContentType.THINKING: "推理过程",
            ContentType.OUTPUT: "输出内容",
            ContentType.ERROR: "错误信息",
            ContentType.INFO: "信息",
            ContentType.SUCCESS: "成功",
            ContentType.WARNING: "警告"
        }
        return title_map.get(content_type, "内容")
        
    def _wrap_text(self, text: str, width: int) -> list:
        """Wrap text to specified width."""
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            if len(current_line + " " + word) <= width:
                current_line += " " + word if current_line else word
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
                
        if current_line:
            lines.append(current_line)
            
        return lines
        
    def _print_colored(self, text: str, color: str, bold: bool = False):
        """Print colored text."""
        color_code = self.COLORS.get(color, self.COLORS['white'])
        bold_code = self.COLORS['bold'] if bold else ''
        print(f"{bold_code}{color_code}{text}{self.COLORS['reset']}")
        
    def get_final_content(self) -> str:
        """Get the final accumulated content."""
        return self.current_content
        
    def reset(self):
        """Reset the displayer state."""
        self.current_content = ""
        self.in_thinking = False
        self.thinking_content = ""
        self.output_content = ""
