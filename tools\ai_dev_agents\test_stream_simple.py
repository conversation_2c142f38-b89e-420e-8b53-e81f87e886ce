#!/usr/bin/env python3
"""
Simple test for streaming functionality.
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from ai_dev_agents.utils.stream_displayer import StreamDisplayer
from ai_dev_agents.utils.config_manager import Config<PERSON>ana<PERSON>


def test_stream_displayer():
    """Test StreamDisplayer directly."""
    
    print("🧪 测试 StreamDisplayer 直接功能...")
    print("=" * 50)
    
    # Create stream displayer
    displayer = StreamDisplayer(enabled=True, show_thinking=True)
    
    # Test agent session
    displayer.start_agent_session("test_agent", "测试流式输出")
    
    # Simulate streaming chunks
    test_chunks = [
        "这是一个",
        "测试的",
        "流式输出",
        "内容。",
        "\n\n我们可以看到",
        "每个chunk",
        "都会实时显示。"
    ]
    
    print("📝 模拟流式输出:")
    for chunk in test_chunks:
        displayer.process_stream_chunk(chunk)
        import time
        time.sleep(0.2)  # 模拟网络延迟
    
    displayer.complete_agent_session(True, "测试完成")
    
    print("\n" + "=" * 50)
    print("✅ StreamDisplayer 测试完成!")


def test_thinking_process():
    """Test thinking process display."""
    
    print("\n🧠 测试推理过程显示...")
    print("=" * 50)
    
    displayer = StreamDisplayer(enabled=True, show_thinking=True)
    displayer.start_agent_session("thinking_agent", "推理过程测试")
    
    # Simulate thinking process
    thinking_chunks = [
        "<thinking>",
        "让我分析一下这个问题...",
        "\n首先，我需要理解用户的需求。",
        "\n然后，我会制定一个解决方案。",
        "\n最后，我会提供具体的实现步骤。",
        "</thinking>",
        "\n\n基于我的分析，",
        "我建议采用以下方案：",
        "\n1. 分析需求",
        "\n2. 设计方案", 
        "\n3. 实施计划"
    ]
    
    print("🤔 模拟推理过程:")
    for chunk in thinking_chunks:
        displayer.process_stream_chunk(chunk)
        import time
        time.sleep(0.3)
    
    displayer.complete_agent_session(True, "推理完成")
    
    print("\n" + "=" * 50)
    print("✅ 推理过程测试完成!")


def test_llm_integration():
    """Test with actual LLM."""
    
    print("\n🤖 测试真实 LLM 集成...")
    print("=" * 50)
    
    try:
        # Create config manager and LLM
        config_manager = ConfigManager()
        llm = config_manager.create_llm()
        
        if llm is None:
            print("❌ 无法创建 LLM，跳过集成测试")
            return
        
        # Create displayer
        displayer = StreamDisplayer(enabled=True, show_thinking=True)
        displayer.start_agent_session("real_llm", "真实 LLM 测试")
        
        # Simple prompt
        from langchain_core.messages import HumanMessage
        messages = [HumanMessage(content="请简单介绍一下什么是人工智能，用中文回答，大约50字。")]
        
        print("💬 LLM 实时响应:")
        
        # Stream response
        full_response = ""
        for chunk in llm.stream(messages):
            if hasattr(chunk, 'content'):
                chunk_content = chunk.content
                if chunk_content:
                    full_response = displayer.process_stream_chunk(chunk_content)
        
        displayer.complete_agent_session(True, f"LLM 响应完成 ({len(full_response)} 字符)")
        
        print(f"\n📄 完整响应: {full_response}")
        
    except Exception as e:
        print(f"❌ LLM 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("✅ LLM 集成测试完成!")


if __name__ == "__main__":
    test_stream_displayer()
    test_thinking_process()
    test_llm_integration()
