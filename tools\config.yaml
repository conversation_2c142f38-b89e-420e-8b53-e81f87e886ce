# AI Development Agents Configuration
# 配置文件用于设置大模型和运行参数

# LLM 配置
llm:
  # 提供商类型: openrouter, openai, anthropic
  provider: "openrouter"

  # OpenRouter 配置
  openrouter:
    api_key: "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"
    base_url: "https://openrouter.ai/api/v1"
    model: "deepseek/deepseek-r1-0528:free"
    temperature: 0.1
    max_tokens: 4000
    timeout: 60

  # OpenAI 配置（备选）
  openai:
    api_key: "${OPENAI_API_KEY}"
    model: "gpt-4-turbo"
    temperature: 0.1
    max_tokens: 4000

  # Anthropic 配置（备选）
  anthropic:
    api_key: "${ANTHROPIC_API_KEY}"
    model: "claude-3-sonnet-20240229"
    temperature: 0.1
    max_tokens: 4000

# 系统运行参数
system:
  # 日志级别: DEBUG, INFO, WARNING, ERROR
  log_level: "INFO"

  # 是否启用详细输出
  verbose: true

# 项目配置
project:
  # 架构风格
  architecture_style: "DDD + FastAPI"

  # 技术栈
  tech_stack:
    - "FastAPI"
    - "SQLAlchemy"
    - "Pydantic"
    - "Alembic"
    - "Pytest"

  # 现有模块
  existing_modules:
    - "auth"
    - "user"
    - "oauth_provider"

# 模型预设配置
model_presets:
  # 默认选择（DeepSeek R1 免费版）
  default:
    provider: "openrouter"
    model: "deepseek/deepseek-r1-0528:free"
    temperature: 0.1
    max_tokens: 4000
    description: "DeepSeek R1 免费版，推理能力强"

  # 高质量分析
  high_quality:
    provider: "openrouter"
    model: "deepseek/deepseek-r1-0528:free"
    temperature: 0.05
    max_tokens: 6000
    description: "DeepSeek R1 高质量模式，更低温度"

  # 创意模式
  creative:
    provider: "openrouter"
    model: "deepseek/deepseek-r1-0528:free"
    temperature: 0.3
    max_tokens: 4000
    description: "DeepSeek R1 创意模式，更高温度"
