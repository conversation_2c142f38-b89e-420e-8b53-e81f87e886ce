# AI Development Agents Configuration
# 配置文件用于设置大模型和系统参数

# LLM 配置
llm:
  # 提供商类型: openrouter, openai, anthropic, azure
  provider: "openrouter"
  
  # OpenRouter 配置
  openrouter:
    api_key: "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"
    base_url: "https://openrouter.ai/api/v1"
    model: "deepseek/deepseek-r1-0528:free"
    temperature: 0.1
    max_tokens: 4000
    timeout: 60
    # 可选的请求头
    headers:
      HTTP-Referer: "https://github.com/agilemetrics-tech/ai4se-mcp-hub"
      X-Title: "AI4SE MCP Hub Development"
  
  # OpenAI 配置（备选）
  openai:
    api_key: "${OPENAI_API_KEY}"
    base_url: "https://api.openai.com/v1"
    model: "gpt-4-turbo"
    temperature: 0.1
    max_tokens: 4000
    timeout: 60
  
  # Anthropic 配置（备选）
  anthropic:
    api_key: "${ANTHROPIC_API_KEY}"
    base_url: "https://api.anthropic.com"
    model: "claude-3-sonnet-20240229"
    temperature: 0.1
    max_tokens: 4000
    timeout: 60

# 系统配置
system:
  # 日志级别: DEBUG, INFO, WARNING, ERROR
  log_level: "INFO"
  
  # 是否启用详细输出
  verbose: true
  
  # 重试配置
  retry:
    max_attempts: 3
    delay_seconds: 1
    exponential_backoff: true
  
  # 输出配置
  output:
    # 结果保存目录
    results_dir: "results"
    # 是否保存中间结果
    save_intermediate: true
    # 输出格式: json, yaml
    format: "json"
    # 是否美化输出
    pretty_print: true

# 项目配置
project:
  # 项目根目录（相对于配置文件）
  root_dir: ".."
  
  # 架构风格
  architecture_style: "DDD + FastAPI"
  
  # 技术栈
  tech_stack:
    - "FastAPI"
    - "SQLAlchemy"
    - "Pydantic"
    - "Alembic"
    - "Pytest"
  
  # 现有模块
  existing_modules:
    - "auth"
    - "user"
    - "oauth_provider"
  
  # 代码质量要求
  quality:
    # 是否强制类型提示
    enforce_type_hints: true
    # 是否遵循 PEP 8
    follow_pep8: true
    # 是否使用 UUID 主键
    use_uuid_primary_key: true
    # 文档语言
    documentation_language: "english"

# Agent 特定配置
agents:
  # 业务分析器配置
  business_analyzer:
    # 是否启用复杂度评分
    enable_complexity_scoring: true
    # 是否生成关系映射
    generate_relationship_mapping: true
    # 最大实体数量
    max_entities: 20
  
  # 领域建模器配置
  domain_modeler:
    # 是否验证聚合一致性
    validate_aggregate_consistency: true
    # 是否识别 DDD 模式
    identify_ddd_patterns: true
    # 最大聚合数量
    max_aggregates: 10
  
  # 需求生成器配置
  requirements_generator:
    # 是否生成依赖分析
    generate_dependency_analysis: true
    # 是否估算复杂度
    estimate_complexity: true
    # 是否包含测试策略
    include_testing_strategy: true
  
  # 提示构建器配置
  prompt_builder:
    # 提示模板类型: detailed, concise, technical
    template_type: "detailed"
    # 是否包含示例
    include_examples: true
    # 最大提示长度
    max_prompt_length: 8000

# 模型预设配置
model_presets:
  # 默认选择（DeepSeek R1 免费版）
  default:
    provider: "openrouter"
    model: "deepseek/deepseek-r1-0528:free"
    temperature: 0.1
    max_tokens: 4000
    description: "DeepSeek R1 免费版，推理能力强"

  # 高质量分析
  high_quality:
    provider: "openrouter"
    model: "deepseek/deepseek-r1-0528:free"
    temperature: 0.05
    max_tokens: 6000
    description: "DeepSeek R1 高质量模式，更低温度"

  # 创意模式
  creative:
    provider: "openrouter"
    model: "deepseek/deepseek-r1-0528:free"
    temperature: 0.3
    max_tokens: 4000
    description: "DeepSeek R1 创意模式，更高温度"

  # 经济选择（备用）
  economical:
    provider: "openrouter"
    model: "anthropic/claude-3-haiku"
    temperature: 0.2
    max_tokens: 2000
    description: "Claude Haiku 快速且经济的选择"

  # OpenAI 选择（备用）
  openai_latest:
    provider: "openrouter"
    model: "openai/gpt-4-turbo"
    temperature: 0.1
    max_tokens: 4000
    description: "OpenAI 最新模型"

# 环境特定配置
environments:
  development:
    log_level: "DEBUG"
    verbose: true
    save_intermediate: true
  
  production:
    log_level: "INFO"
    verbose: false
    save_intermediate: false
  
  testing:
    log_level: "WARNING"
    verbose: false
    save_intermediate: false
