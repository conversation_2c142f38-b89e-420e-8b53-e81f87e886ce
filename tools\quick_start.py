#!/usr/bin/env python3
"""
Quick Start Script for AI Development Agents

This script provides an interactive way to configure and test the AI agents.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """Check if required dependencies are installed."""
    try:
        import langchain_openai
        return True
    except ImportError:
        return False

def get_user_config():
    """Get configuration from user input."""
    print("🔧 OpenRouter Configuration Setup")
    print("=" * 40)
    
    config = {}
    
    # API Key
    api_key = os.getenv("OPENROUTER_API_KEY", "")
    if not api_key:
        print("\n📝 Please enter your OpenRouter API Key:")
        print("   (You can get one from https://openrouter.ai/)")
        api_key = input("   API Key: ").strip()
        
        if not api_key:
            print("❌ API Key is required!")
            return None
    
    config["api_key"] = api_key
    
    # Model selection
    print("\n🤖 Select a model:")
    models = [
        ("anthropic/claude-3-sonnet", "Claude 3 Sonnet (Recommended - balanced quality/cost)"),
        ("anthropic/claude-3-haiku", "Claude 3 Haiku (Fast and economical)"),
        ("anthropic/claude-3-opus", "Claude 3 Opus (Highest quality, higher cost)"),
        ("openai/gpt-4-turbo", "GPT-4 Turbo (OpenAI latest)"),
        ("openai/gpt-3.5-turbo", "GPT-3.5 Turbo (Economical)"),
        ("custom", "Enter custom model name")
    ]
    
    for i, (model_id, description) in enumerate(models, 1):
        print(f"   {i}. {description}")
    
    choice = input(f"\n   Select model (1-{len(models)}, default: 1): ").strip()
    
    if not choice:
        choice = "1"
    
    try:
        choice_idx = int(choice) - 1
        if 0 <= choice_idx < len(models):
            if models[choice_idx][0] == "custom":
                custom_model = input("   Enter custom model name: ").strip()
                config["model"] = custom_model if custom_model else "anthropic/claude-3-sonnet"
            else:
                config["model"] = models[choice_idx][0]
        else:
            config["model"] = "anthropic/claude-3-sonnet"
    except ValueError:
        config["model"] = "anthropic/claude-3-sonnet"
    
    # Other settings
    config["base_url"] = "https://openrouter.ai/api/v1"
    config["temperature"] = 0.1
    config["max_tokens"] = 2000
    
    return config

def run_test(config):
    """Run the test with given configuration."""
    try:
        from langchain_openai import ChatOpenAI
        from tools.ai_dev_agents.business_analyzer import BusinessAnalyzerAgent
        from tools.ai_dev_agents.base_agent import WorkflowContext
        import json
        
        print("\n🚀 Running AI Agent Test")
        print("=" * 30)
        
        # Create LLM
        print("🤖 Creating LLM connection...")
        llm = ChatOpenAI(
            api_key=config["api_key"],
            model=config["model"],
            base_url=config["base_url"],
            temperature=config["temperature"],
            max_tokens=config["max_tokens"],
            default_headers={
                "HTTP-Referer": "https://github.com/agilemetrics-tech/ai4se-mcp-hub",
                "X-Title": "AI4SE MCP Hub Development"
            }
        )
        print(f"✅ Connected to {config['model']}")
        
        # Create agent and context
        agent = BusinessAnalyzerAgent(llm=llm)
        context = WorkflowContext(
            project_root=str(project_root),
            architecture_style="DDD + FastAPI",
            tech_stack=["FastAPI", "SQLAlchemy", "Pydantic"],
            existing_modules=["auth", "user"]
        )
        
        # Sample PRD
        sample_prd = """
# E-Commerce Platform

## Overview
Build a modern e-commerce platform for online retail.

## Features
1. Product Catalog - Browse and search products
2. Shopping Cart - Add/remove items, calculate totals
3. User Accounts - Registration, login, profiles
4. Order Management - Place orders, track status
5. Payment Processing - Secure payment handling

## User Stories
- As a customer, I want to browse products by category
- As a customer, I want to add items to my cart
- As a customer, I want to place orders securely
- As an admin, I want to manage product inventory
"""
        
        print("📊 Analyzing business requirements...")
        result = agent.execute({"prd_content": sample_prd}, context)
        
        if result.success:
            print("✅ Analysis completed successfully!")
            
            data = result.data
            print(f"\n📋 Results:")
            print(f"   - Project: {data.get('business_overview', {}).get('project_name', 'N/A')}")
            print(f"   - Entities: {len(data.get('core_entities', []))}")
            print(f"   - Requirements: {len(data.get('functional_requirements', []))}")
            print(f"   - User Stories: {len(data.get('user_stories', []))}")
            
            # Save results
            output_file = project_root / "quick_start_result.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Results saved to: {output_file}")
            print("\n🎉 Test successful! Your AI agents are working correctly.")
            
            return True
        else:
            print(f"❌ Analysis failed: {result.errors}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main function."""
    print("🚀 AI Development Agents - Quick Start")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        print("❌ Missing dependencies. Please install:")
        print("   pip install langchain-openai")
        print("   # or")
        print("   uv add langchain-openai")
        return 1
    
    print("✅ Dependencies available")
    
    # Get configuration
    config = get_user_config()
    if not config:
        return 1
    
    print(f"\n✅ Configuration complete:")
    print(f"   - Model: {config['model']}")
    print(f"   - API Key: {'*' * (len(config['api_key']) - 4) + config['api_key'][-4:]}")
    
    # Run test
    if run_test(config):
        print("\n🎯 Next Steps:")
        print("   1. Check the generated quick_start_result.json file")
        print("   2. Run: python tools/simple_test.py")
        print("   3. Try the CLI: python -m tools.ai_dev_agents.cli --help")
        print("   4. Process your own PRD files!")
        return 0
    else:
        print("\n❌ Test failed. Please check your configuration and try again.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
