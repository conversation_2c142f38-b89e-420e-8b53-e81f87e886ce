"""
Business Analyzer Agent

Intelligent agent for analyzing PRD documents and extracting business requirements.
"""

import re
from typing import Any, Dict, List
from datetime import datetime

from .base_agent import BaseAgent, AgentResult, WorkflowContext


class BusinessAnalyzerAgent(BaseAgent):
    """Agent for analyzing business requirements from PRD documents."""
    
    def __init__(self, llm=None, verbose: bool = False):
        super().__init__("business_analyzer", llm, verbose)
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for business analysis."""
        return """
你是一个资深的业务分析师，专门分析产品需求文档(PRD)。你的任务是深度理解业务需求并提取结构化信息。

你需要分析PRD文档并提取以下信息：

1. **业务概览** (business_overview)
   - 产品/项目的核心目标和价值主张
   - 目标用户群体
   - 主要业务场景

2. **核心业务实体** (core_entities)
   - 识别主要的业务对象和概念
   - 每个实体的关键属性
   - 实体间的关系

3. **业务规则** (business_rules)
   - 业务约束和限制
   - 业务流程规则
   - 数据验证规则

4. **功能需求** (functional_requirements)
   - 具体的功能点
   - 用户操作流程
   - 系统行为描述

5. **非功能需求** (non_functional_requirements)
   - 性能要求
   - 安全要求
   - 可用性要求

6. **用户故事** (user_stories)
   - 以"作为...我希望...以便..."格式
   - 包含验收标准

请严格按照以下JSON格式输出分析结果：

```json
{
  "business_overview": {
    "project_name": "项目名称",
    "core_purpose": "核心目标描述",
    "target_users": ["用户群体1", "用户群体2"],
    "business_value": "业务价值描述"
  },
  "core_entities": [
    {
      "name": "实体名称",
      "description": "实体描述",
      "key_attributes": ["属性1", "属性2"],
      "relationships": ["与其他实体的关系"]
    }
  ],
  "business_rules": [
    {
      "category": "规则类别",
      "rule": "具体规则描述",
      "impact": "影响范围"
    }
  ],
  "functional_requirements": [
    {
      "id": "FR-001",
      "title": "功能标题",
      "description": "功能描述",
      "priority": "高/中/低",
      "user_scenarios": ["场景1", "场景2"]
    }
  ],
  "non_functional_requirements": [
    {
      "category": "性能/安全/可用性",
      "requirement": "具体要求",
      "acceptance_criteria": "验收标准"
    }
  ],
  "user_stories": [
    {
      "id": "US-001",
      "story": "作为[用户角色]，我希望[功能]，以便[价值]",
      "acceptance_criteria": ["标准1", "标准2"],
      "priority": "高/中/低"
    }
  ]
}
```

分析时请注意：
- 深度理解业务语义，不要只是简单提取文字
- 识别隐含的业务规则和约束
- 考虑用户的真实使用场景
- 确保提取的信息完整且结构化
"""
    
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Process PRD document and extract business requirements."""
        try:
            # Get PRD content
            prd_content = input_data.get("prd_content", "")
            if not prd_content:
                return AgentResult(
                    success=False,
                    data={},
                    metadata={"agent_name": self.name},
                    errors=["No PRD content provided"],
                    execution_time=0.0,
                    timestamp=datetime.now()
                )
            
            # Prepare input for LLM
            user_input = f"""
请分析以下PRD文档并提取业务需求信息：

=== PRD文档内容 ===
{prd_content}

=== 分析要求 ===
请按照系统提示中的JSON格式，深度分析文档内容并提取结构化的业务需求信息。
特别注意识别：
1. 核心业务概念和实体
2. 隐含的业务规则
3. 用户的真实需求和痛点
4. 系统的功能边界

请确保输出的JSON格式正确且信息完整。
"""
            
            # Execute LLM call
            system_prompt = self.get_system_prompt()
            messages = self._create_messages(system_prompt, user_input)
            response = self._execute_with_retry(messages)
            
            # Parse response
            parsed_data = self._parse_json_response(response)
            
            # Validate required fields
            required_fields = [
                "business_overview", "core_entities", "business_rules",
                "functional_requirements", "non_functional_requirements", "user_stories"
            ]
            errors = self._validate_required_fields(parsed_data, required_fields)
            
            if errors:
                return AgentResult(
                    success=False,
                    data=parsed_data,
                    metadata={"agent_name": self.name, "raw_response": response},
                    errors=errors,
                    execution_time=0.0,
                    timestamp=datetime.now()
                )
            
            # Post-process and enrich data
            enriched_data = self._enrich_business_analysis(parsed_data, prd_content)
            
            return AgentResult(
                success=True,
                data=enriched_data,
                metadata={
                    "agent_name": self.name,
                    "raw_response": response,
                    "entities_count": len(enriched_data.get("core_entities", [])),
                    "requirements_count": len(enriched_data.get("functional_requirements", [])),
                    "user_stories_count": len(enriched_data.get("user_stories", []))
                },
                errors=[],
                execution_time=0.0,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                data={},
                metadata={"agent_name": self.name},
                errors=[f"Business analysis failed: {str(e)}"],
                execution_time=0.0,
                timestamp=datetime.now()
            )
    
    def _enrich_business_analysis(self, data: Dict[str, Any], prd_content: str) -> Dict[str, Any]:
        """Enrich the business analysis with additional insights."""
        enriched = data.copy()
        
        # Add document metadata
        enriched["document_metadata"] = {
            "content_length": len(prd_content),
            "analysis_timestamp": datetime.now().isoformat(),
            "complexity_score": self._calculate_complexity_score(data)
        }
        
        # Add entity relationships map
        enriched["entity_relationships"] = self._extract_entity_relationships(data.get("core_entities", []))
        
        # Add priority matrix
        enriched["priority_matrix"] = self._create_priority_matrix(
            data.get("functional_requirements", []),
            data.get("user_stories", [])
        )
        
        return enriched
    
    def _calculate_complexity_score(self, data: Dict[str, Any]) -> int:
        """Calculate complexity score based on analysis results."""
        score = 0
        score += len(data.get("core_entities", [])) * 2
        score += len(data.get("functional_requirements", [])) * 1
        score += len(data.get("business_rules", [])) * 3
        score += len(data.get("user_stories", [])) * 1
        return min(score, 100)  # Cap at 100
    
    def _extract_entity_relationships(self, entities: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """Extract relationships between entities."""
        relationships = {}
        for entity in entities:
            entity_name = entity.get("name", "")
            entity_relationships = entity.get("relationships", [])
            if entity_name:
                relationships[entity_name] = entity_relationships
        return relationships
    
    def _create_priority_matrix(self, requirements: List[Dict[str, Any]], user_stories: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """Create priority matrix for requirements and user stories."""
        matrix = {"high": [], "medium": [], "low": []}
        
        # Process functional requirements
        for req in requirements:
            priority = req.get("priority", "medium").lower()
            if priority in matrix:
                matrix[priority].append(f"FR: {req.get('title', 'Unknown')}")
        
        # Process user stories
        for story in user_stories:
            priority = story.get("priority", "medium").lower()
            if priority in matrix:
                matrix[priority].append(f"US: {story.get('id', 'Unknown')}")
        
        return matrix

    def _get_mock_response(self) -> str:
        """Get mock response for testing."""
        return '''
{
  "business_overview": {
    "project_name": "MCP Server Market Platform",
    "core_purpose": "Centralized marketplace for Model Context Protocol servers",
    "target_users": ["Developers", "Server Authors", "Platform Admins"],
    "business_value": "Enable efficient discovery and sharing of MCP servers",
    "key_success_metrics": ["Server adoption rate", "User engagement", "Platform growth"]
  },
  "core_entities": [
    {
      "name": "MCPServer",
      "description": "A Model Context Protocol server with metadata and functionality",
      "attributes": ["id", "name", "description", "version", "category", "author"],
      "relationships": ["belongs_to_author", "has_versions", "categorized_by"]
    },
    {
      "name": "User",
      "description": "Platform user who can discover or publish servers",
      "attributes": ["id", "username", "email", "profile", "role"],
      "relationships": ["owns_servers", "has_profile", "belongs_to_role"]
    },
    {
      "name": "Category",
      "description": "Classification for organizing servers",
      "attributes": ["id", "name", "description", "parent_category"],
      "relationships": ["contains_servers", "has_subcategories"]
    }
  ],
  "business_rules": [
    "Only authenticated users can submit servers",
    "Server metadata must be complete before publication",
    "Users can only modify their own servers",
    "Categories must be approved by administrators"
  ],
  "functional_requirements": [
    {
      "id": "FR-001",
      "title": "Server Discovery",
      "description": "Users can browse and search for MCP servers",
      "priority": "High",
      "acceptance_criteria": ["Search by keywords", "Filter by category", "View server details"]
    },
    {
      "id": "FR-002",
      "title": "Server Submission",
      "description": "Authors can submit new servers to the marketplace",
      "priority": "High",
      "acceptance_criteria": ["Upload server metadata", "Version management", "Publication workflow"]
    }
  ],
  "non_functional_requirements": [
    {
      "category": "Performance",
      "requirements": ["API response time < 200ms", "Support 1000+ concurrent users"]
    },
    {
      "category": "Security",
      "requirements": ["Secure authentication", "Data encryption", "Input validation"]
    }
  ],
  "user_stories": [
    {
      "id": "US-001",
      "title": "Discover MCP Servers",
      "story": "As a developer, I want to browse available MCP servers so that I can find tools that match my needs",
      "acceptance_criteria": [
        "Given I am on the marketplace homepage, when I browse servers, then I see a list of available servers"
      ],
      "priority": "High",
      "story_points": 5
    },
    {
      "id": "US-002",
      "title": "Submit New Server",
      "story": "As a server author, I want to submit my MCP server so that other developers can discover and use it",
      "acceptance_criteria": [
        "Given I am an authenticated user, when I submit server metadata, then my server is added to the marketplace"
      ],
      "priority": "High",
      "story_points": 8
    }
  ]
}
'''
