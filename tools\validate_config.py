#!/usr/bin/env python3
"""
Configuration Validation Script

This script validates the AI Development Agents configuration and provides
detailed information about the setup.

Usage:
    python tools/validate_config.py [preset]

Examples:
    python tools/validate_config.py                    # Validate default config
    python tools/validate_config.py high_quality       # Validate with preset
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Validate configuration and show detailed information."""
    preset = sys.argv[1] if len(sys.argv) > 1 else None
    
    print("🔍 AI Development Agents - Configuration Validation")
    print("=" * 60)
    
    try:
        from tools.ai_dev_agents.config_manager import ConfigManager
        print("✅ Configuration manager imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import configuration manager: {e}")
        return 1
    
    # Initialize configuration manager
    config_manager = ConfigManager()
    print(f"✅ Configuration loaded from: {config_manager.config_path}")
    
    # Validate configuration
    print("\n🔧 Configuration Validation:")
    print("-" * 40)
    
    validation = config_manager.validate_config()
    
    if validation["valid"]:
        print("✅ Configuration is valid!")
    else:
        print("❌ Configuration validation failed!")
        
    # Show errors
    if validation["errors"]:
        print("\n🚨 Errors:")
        for error in validation["errors"]:
            print(f"   - {error}")
    
    # Show warnings
    if validation["warnings"]:
        print("\n⚠️ Warnings:")
        for warning in validation["warnings"]:
            print(f"   - {warning}")
    
    # Show LLM configuration
    print(f"\n🤖 LLM Configuration:")
    print("-" * 40)
    
    llm_config = config_manager.get_llm_config(preset)
    print(f"Provider: {llm_config.provider}")
    print(f"Model: {llm_config.model}")
    print(f"Base URL: {llm_config.base_url}")
    print(f"Temperature: {llm_config.temperature}")
    print(f"Max Tokens: {llm_config.max_tokens}")
    print(f"Timeout: {llm_config.timeout}")
    print(f"API Key: {'✅ Set (' + llm_config.api_key[-8:] + ')' if llm_config.api_key else '❌ Not set'}")
    
    if llm_config.headers:
        print("Headers:")
        for key, value in llm_config.headers.items():
            print(f"  - {key}: {value}")
    
    # Show available presets
    print(f"\n📋 Available Model Presets:")
    print("-" * 40)
    
    presets = config_manager.list_available_presets()
    if presets:
        for name, description in presets.items():
            marker = "👉" if name == preset else "  "
            print(f"{marker} {name}: {description}")
    else:
        print("No presets configured")
    
    # Show system configuration
    print(f"\n⚙️ System Configuration:")
    print("-" * 40)
    
    system_config = config_manager.get_system_config()
    print(f"Log Level: {system_config.get('log_level', 'INFO')}")
    print(f"Verbose: {system_config.get('verbose', False)}")
    
    retry_config = system_config.get('retry', {})
    print(f"Max Retry Attempts: {retry_config.get('max_attempts', 3)}")
    print(f"Retry Delay: {retry_config.get('delay_seconds', 1)}s")
    
    output_config = system_config.get('output', {})
    print(f"Results Directory: {output_config.get('results_dir', 'results')}")
    print(f"Output Format: {output_config.get('format', 'json')}")
    
    # Show project configuration
    print(f"\n🏗️ Project Configuration:")
    print("-" * 40)
    
    project_config = config_manager.get_project_config()
    print(f"Architecture Style: {project_config.get('architecture_style', 'N/A')}")
    
    tech_stack = project_config.get('tech_stack', [])
    if tech_stack:
        print(f"Tech Stack: {', '.join(tech_stack)}")
    
    existing_modules = project_config.get('existing_modules', [])
    if existing_modules:
        print(f"Existing Modules: {', '.join(existing_modules)}")
    
    quality_config = project_config.get('quality', {})
    if quality_config:
        print("Quality Requirements:")
        print(f"  - Type Hints: {quality_config.get('enforce_type_hints', False)}")
        print(f"  - PEP 8: {quality_config.get('follow_pep8', False)}")
        print(f"  - UUID Primary Keys: {quality_config.get('use_uuid_primary_key', False)}")
        print(f"  - Documentation Language: {quality_config.get('documentation_language', 'english')}")
    
    # Test LLM creation
    print(f"\n🧪 LLM Creation Test:")
    print("-" * 40)
    
    try:
        llm = config_manager.create_llm(preset)
        if llm:
            print("✅ LLM instance created successfully!")
            print(f"   Type: {type(llm).__name__}")
            print(f"   Model: {getattr(llm, 'model_name', 'Unknown')}")
        else:
            print("❌ Failed to create LLM instance")
            print("   Check your API key and configuration")
    except Exception as e:
        print(f"❌ LLM creation failed: {e}")
    
    # Environment variables check
    print(f"\n🌍 Environment Variables:")
    print("-" * 40)
    
    env_vars = [
        "OPENROUTER_API_KEY",
        "OPENROUTER_MODEL", 
        "OPENROUTER_BASE_URL",
        "OPENAI_API_KEY",
        "ANTHROPIC_API_KEY"
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            # Show only last 8 characters for security
            display_value = "*" * (len(value) - 8) + value[-8:] if len(value) > 8 else "*" * len(value)
            print(f"✅ {var}: {display_value}")
        else:
            print(f"❌ {var}: Not set")
    
    # Summary
    print(f"\n📊 Summary:")
    print("-" * 40)
    
    if validation["valid"]:
        print("✅ Configuration is ready to use!")
        print("🚀 You can now run:")
        print("   python tools/simple_test.py")
        if preset:
            print(f"   python tools/simple_test.py {preset}")
        print("   python -m tools.ai_dev_agents.cli --help")
        return 0
    else:
        print("❌ Configuration needs attention!")
        print("📖 Please check:")
        print("   1. tools/OPENROUTER_CONFIG.md for setup instructions")
        print("   2. tools/config.yaml for configuration options")
        print("   3. Environment variables (especially API keys)")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
