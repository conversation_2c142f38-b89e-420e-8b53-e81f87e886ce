"""
Base Agent Classes and Utilities

This module provides the foundation for all AI development agents with enhanced
type safety, error handling, and monitoring capabilities.
"""

import json
import logging
import traceback
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Protocol, runtime_checkable, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

try:
    from langchain.schema import BaseMessage, HumanMessage, SystemMessage
    from langchain.callbacks.base import BaseCallbackHandler
    from langchain.schema.output import LLMResult
    from langchain.schema.language_model import BaseLanguageModel
    LANGCHAIN_AVAILABLE = True
except ImportError:
    # Fallback for when langchain is not installed
    class BaseMessage:
        def __init__(self, content: str):
            self.content = content

    class HumanMessage(BaseMessage):
        pass

    class SystemMessage(BaseMessage):
        pass

    class BaseCallbackHandler:
        pass

    class LLMResult:
        pass

    class BaseLanguageModel:
        pass

    LANGCHAIN_AVAILABLE = False

try:
    from ..utils.config_manager import ConfigManager
except ImportError:
    # Fallback when config manager is not available
    ConfigManager = None


class AgentStatus(Enum):
    """Agent execution status."""
    IDLE = "idle"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"


class LogLevel(Enum):
    """Logging levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@runtime_checkable
class LLMProtocol(Protocol):
    """Protocol for LLM instances."""

    def invoke(self, messages: List[BaseMessage]) -> Any:
        """Invoke the LLM with messages."""
        ...


class AgentError(Exception):
    """Base exception for agent errors."""

    def __init__(
        self,
        message: str,
        agent_name: str = "",
        details: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(message)
        self.agent_name = agent_name
        self.details = details or {}
        self.original_error = original_error
        self.timestamp = datetime.now()


class ValidationError(AgentError):
    """Exception for validation errors."""
    pass


class ProcessingError(AgentError):
    """Exception for processing errors."""
    pass


class ConfigurationError(AgentError):
    """Exception for configuration errors."""
    pass


class TimeoutError(AgentError):
    """Exception for timeout errors."""
    pass


@dataclass
class AgentResult:
    """
    Result from an agent execution with comprehensive metadata.

    Attributes:
        success: Whether the agent execution was successful
        data: The main output data from the agent
        metadata: Additional metadata about the execution
        errors: List of error messages if any
        warnings: List of warning messages if any
        execution_time: Time taken for execution in seconds
        timestamp: When the execution completed
        status: Current status of the agent
        retry_count: Number of retries attempted
        memory_usage: Memory usage during execution (if available)
    """
    success: bool
    data: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    execution_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    status: AgentStatus = AgentStatus.IDLE
    retry_count: int = 0
    memory_usage: Optional[float] = None

    def add_error(self, error: str) -> None:
        """Add an error message."""
        self.errors.append(error)
        self.success = False
        self.status = AgentStatus.FAILED

    def add_warning(self, warning: str) -> None:
        """Add a warning message."""
        self.warnings.append(warning)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "success": self.success,
            "data": self.data,
            "metadata": self.metadata,
            "errors": self.errors,
            "warnings": self.warnings,
            "execution_time": self.execution_time,
            "timestamp": self.timestamp.isoformat(),
            "status": self.status.value,
            "retry_count": self.retry_count,
            "memory_usage": self.memory_usage
        }


@dataclass
class WorkflowContext:
    """
    Context shared across all agents in the workflow with enhanced tracking.

    Attributes:
        project_root: Root directory of the project
        project_rules: Project-specific rules and constraints
        existing_modules: List of existing modules in the project
        tech_stack: Technology stack being used
        architecture_style: Architecture pattern (e.g., DDD + FastAPI)
        intermediate_results: Results from previous agents
        global_config: Global configuration settings
        execution_history: History of all agent executions
        start_time: When the workflow started
    """
    project_root: str
    project_rules: str
    existing_modules: List[str]
    tech_stack: List[str]
    architecture_style: str
    intermediate_results: Dict[str, Any] = field(default_factory=dict)
    global_config: Dict[str, Any] = field(default_factory=dict)
    execution_history: List[Dict[str, Any]] = field(default_factory=list)
    start_time: datetime = field(default_factory=datetime.now)

    def add_result(self, agent_name: str, result: AgentResult) -> None:
        """Add agent result to context with history tracking."""
        self.intermediate_results[agent_name] = result.data
        self.execution_history.append({
            "agent_name": agent_name,
            "timestamp": result.timestamp.isoformat(),
            "success": result.success,
            "execution_time": result.execution_time,
            "errors": result.errors,
            "warnings": result.warnings
        })

    def get_result(self, agent_name: str) -> Optional[Dict[str, Any]]:
        """Get result from previous agent."""
        return self.intermediate_results.get(agent_name)

    def get_execution_summary(self) -> Dict[str, Any]:
        """Get summary of all executions."""
        total_time = sum(h["execution_time"] for h in self.execution_history)
        success_count = sum(1 for h in self.execution_history if h["success"])

        return {
            "total_agents": len(self.execution_history),
            "successful_agents": success_count,
            "failed_agents": len(self.execution_history) - success_count,
            "total_execution_time": total_time,
            "workflow_duration": (datetime.now() - self.start_time).total_seconds()
        }

    def has_errors(self) -> bool:
        """Check if any agent has errors."""
        return any(h["errors"] for h in self.execution_history)

    def get_all_errors(self) -> List[str]:
        """Get all errors from all agents."""
        all_errors = []
        for history in self.execution_history:
            all_errors.extend(history["errors"])
        return all_errors


class AgentCallbackHandler(BaseCallbackHandler):
    """Custom callback handler for agent monitoring."""
    
    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.logger = logging.getLogger(f"agent.{agent_name}")
    
    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        self.logger.info(f"[{self.agent_name}] LLM started with {len(prompts)} prompts")
    
    def on_llm_end(self, response, **kwargs) -> None:
        self.logger.info(f"[{self.agent_name}] LLM completed")
    
    def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs) -> None:
        self.logger.error(f"[{self.agent_name}] LLM error: {error}")


class BaseAgent(ABC):
    """
    Base class for all AI development agents with enhanced capabilities.

    Features:
    - Comprehensive error handling and recovery
    - Performance monitoring and metrics
    - Configurable retry logic
    - Input/output validation
    - Memory usage tracking
    """

    def __init__(
        self,
        name: str,
        llm: Optional[Union[BaseLanguageModel, LLMProtocol]] = None,
        verbose: bool = False,
        max_retries: int = 3,
        timeout_seconds: int = 300,
        enable_metrics: bool = True
    ):
        self.name = name
        self.llm = llm
        self.verbose = verbose
        self.max_retries = max_retries
        self.timeout_seconds = timeout_seconds
        self.enable_metrics = enable_metrics

        # Logging setup
        self.logger = logging.getLogger(f"agent.{name}")
        self.callback_handler = AgentCallbackHandler(name)

        # Metrics tracking
        self.execution_count = 0
        self.total_execution_time = 0.0
        self.success_count = 0
        self.error_count = 0

        # Setup logging
        if verbose:
            logging.basicConfig(level=logging.INFO)

        # Validate LLM if provided
        if llm is not None and not isinstance(llm, (BaseLanguageModel, LLMProtocol)):
            self.logger.warning(f"LLM instance may not be compatible: {type(llm)}")

    @property
    def success_rate(self) -> float:
        """Calculate success rate of agent executions."""
        if self.execution_count == 0:
            return 0.0
        return self.success_count / self.execution_count

    @property
    def average_execution_time(self) -> float:
        """Calculate average execution time."""
        if self.execution_count == 0:
            return 0.0
        return self.total_execution_time / self.execution_count
    
    @abstractmethod
    def get_system_prompt(self) -> str:
        """Get the system prompt for this agent."""
        pass
    
    @abstractmethod
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Process input data and return result."""
        pass
    
    def _create_messages(self, system_prompt: str, user_input: str) -> List[BaseMessage]:
        """Create message list for LLM."""
        return [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_input)
        ]
    
    def _parse_json_response(self, response: str) -> Dict[str, Any]:
        """
        Parse JSON response from LLM with enhanced error handling.

        Args:
            response: Raw response string from LLM

        Returns:
            Parsed JSON data or error information

        Raises:
            ValidationError: If response format is invalid
        """
        if not response or not response.strip():
            raise ValidationError("Empty response received", self.name)

        try:
            # Try to parse as direct JSON first
            return json.loads(response.strip())
        except json.JSONDecodeError:
            pass

        try:
            # Try to extract JSON from markdown code blocks
            import re
            json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
            match = re.search(json_pattern, response, re.DOTALL)
            if match:
                return json.loads(match.group(1))
        except (json.JSONDecodeError, AttributeError):
            pass

        try:
            # Try to find JSON object in response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1

            if start_idx != -1 and end_idx > start_idx:
                json_str = response[start_idx:end_idx]
                return json.loads(json_str)
        except json.JSONDecodeError:
            pass

        # If all parsing attempts fail, log and return structured error
        self.logger.warning(f"Failed to parse JSON from response: {response[:200]}...")
        return {
            "content": response,
            "parse_error": "Could not extract valid JSON from response",
            "raw_response": response
        }
    
    def _validate_required_fields(self, data: Dict[str, Any], required_fields: List[str]) -> List[str]:
        """Validate that required fields are present in data."""
        errors = []
        for field in required_fields:
            if field not in data or not data[field]:
                errors.append(f"Missing required field: {field}")
        return errors
    
    def _execute_with_retry(self, messages: List[BaseMessage], max_retries: int = 3) -> str:
        """Execute LLM call with retry logic."""
        for attempt in range(max_retries):
            try:
                if self.llm is None:
                    # Return mock response for testing
                    self.logger.info("Using mock response (LLM not configured)")
                    return self._get_mock_response()

                response = self.llm.invoke(messages)

                # Handle different response types
                if hasattr(response, 'content'):
                    return response.content
                elif isinstance(response, str):
                    return response
                else:
                    return str(response)

            except Exception as e:
                self.logger.warning(f"Attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    # Return mock response as fallback
                    self.logger.info("Using mock response as fallback")
                    return self._get_mock_response()

        raise RuntimeError(f"Failed to execute after {max_retries} attempts")

    def _get_mock_response(self) -> str:
        """Get mock response for testing when LLM is not available."""
        # This should be overridden by subclasses to provide agent-specific mock responses
        return '{"mock": true, "message": "This is a mock response for testing"}'

    def _log_execution(self, input_data: Dict[str, Any], result: AgentResult) -> None:
        """Log agent execution details."""
        self.logger.info(f"Agent {self.name} executed successfully: {result.success}")
        if result.errors:
            self.logger.warning(f"Errors: {result.errors}")
        self.logger.info(f"Execution time: {result.execution_time:.2f}s")
    
    def execute(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """
        Execute the agent with comprehensive error handling and monitoring.

        Args:
            input_data: Input data for the agent
            context: Workflow context

        Returns:
            AgentResult with execution details
        """
        start_time = datetime.now()
        self.execution_count += 1

        # Initialize result with default values
        result = AgentResult(
            success=False,
            data={},
            metadata={"agent_name": self.name, "start_time": start_time.isoformat()},
            status=AgentStatus.RUNNING
        )

        try:
            self.logger.info(f"Starting agent {self.name} (execution #{self.execution_count})")

            # Validate input data
            self._validate_input(input_data)

            # Execute the main processing
            result = self.process(input_data, context)

            # Ensure result has proper metadata
            execution_time = (datetime.now() - start_time).total_seconds()
            result.execution_time = execution_time
            result.timestamp = datetime.now()
            result.status = AgentStatus.SUCCESS if result.success else AgentStatus.FAILED

            # Update metrics
            self.total_execution_time += execution_time
            if result.success:
                self.success_count += 1
            else:
                self.error_count += 1

            # Log execution details
            self._log_execution(input_data, result)

            # Add result to context if successful
            if result.success:
                context.add_result(self.name, result)

            return result

        except ValidationError as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self.error_count += 1
            self.logger.error(f"Validation error in agent {self.name}: {e}")

            result.success = False
            result.status = AgentStatus.FAILED
            result.execution_time = execution_time
            result.timestamp = datetime.now()
            result.add_error(f"Validation error: {str(e)}")
            result.metadata.update({"error_type": "validation", "original_error": str(e)})

            return result

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self.error_count += 1
            self.logger.error(f"Unexpected error in agent {self.name}: {e}")

            if self.verbose:
                self.logger.error(f"Traceback: {traceback.format_exc()}")

            result.success = False
            result.status = AgentStatus.FAILED
            result.execution_time = execution_time
            result.timestamp = datetime.now()
            result.add_error(f"Unexpected error: {str(e)}")
            result.metadata.update({
                "error_type": "unexpected",
                "original_error": str(e),
                "traceback": traceback.format_exc() if self.verbose else None
            })

            return result

    def _validate_input(self, input_data: Dict[str, Any]) -> None:
        """
        Validate input data before processing.

        Args:
            input_data: Input data to validate

        Raises:
            ValidationError: If input data is invalid
        """
        if not isinstance(input_data, dict):
            raise ValidationError(f"Input data must be a dictionary, got {type(input_data)}", self.name)

        # Subclasses can override this method for specific validation

    def get_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for this agent."""
        return {
            "agent_name": self.name,
            "execution_count": self.execution_count,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "success_rate": self.success_rate,
            "total_execution_time": self.total_execution_time,
            "average_execution_time": self.average_execution_time
        }


class AgentChain:
    """
    Chain multiple agents together with enhanced execution control.

    Features:
    - Sequential execution with dependency management
    - Error handling and recovery strategies
    - Progress tracking and monitoring
    - Conditional execution based on results
    """

    def __init__(
        self,
        agents: List[BaseAgent],
        stop_on_failure: bool = True,
        enable_recovery: bool = False
    ):
        self.agents = agents
        self.stop_on_failure = stop_on_failure
        self.enable_recovery = enable_recovery
        self.logger = logging.getLogger("agent.chain")

        # Validate agents
        if not agents:
            raise ValueError("Agent chain cannot be empty")

        for i, agent in enumerate(agents):
            if not isinstance(agent, BaseAgent):
                raise TypeError(f"Agent at index {i} is not a BaseAgent instance")

    def execute(
        self,
        initial_input: Dict[str, Any],
        context: WorkflowContext,
        progress_callback: Optional[Callable[[int, int, str], None]] = None
    ) -> List[AgentResult]:
        """
        Execute all agents in sequence with enhanced monitoring.

        Args:
            initial_input: Initial input data
            context: Workflow context
            progress_callback: Optional callback for progress updates

        Returns:
            List of agent results
        """
        results = []
        current_input = initial_input
        total_agents = len(self.agents)

        self.logger.info(f"Starting agent chain with {total_agents} agents")

        for i, agent in enumerate(self.agents):
            try:
                # Progress callback
                if progress_callback:
                    progress_callback(i, total_agents, f"Executing {agent.name}")

                self.logger.info(f"Executing agent {i+1}/{total_agents}: {agent.name}")

                # Execute agent
                result = agent.execute(current_input, context)
                results.append(result)

                # Check result
                if not result.success:
                    self.logger.error(f"Agent {agent.name} failed with errors: {result.errors}")

                    if self.stop_on_failure:
                        self.logger.error("Stopping chain due to agent failure")
                        break
                    elif self.enable_recovery:
                        # Try to recover by using previous successful result
                        if results and len(results) > 1:
                            last_success = next((r for r in reversed(results[:-1]) if r.success), None)
                            if last_success:
                                current_input = last_success.data
                                self.logger.info(f"Recovered using data from previous successful agent")
                                continue

                # Use agent output as input for next agent
                current_input = result.data

                self.logger.info(f"Agent {agent.name} completed successfully")

            except Exception as e:
                self.logger.error(f"Unexpected error executing agent {agent.name}: {e}")

                # Create error result
                error_result = AgentResult(
                    success=False,
                    data={},
                    metadata={"agent_name": agent.name, "chain_position": i},
                    errors=[f"Chain execution error: {str(e)}"],
                    timestamp=datetime.now(),
                    status=AgentStatus.FAILED
                )
                results.append(error_result)

                if self.stop_on_failure:
                    break

        # Final progress callback
        if progress_callback:
            successful_count = sum(1 for r in results if r.success)
            progress_callback(total_agents, total_agents, f"Completed: {successful_count}/{total_agents} successful")

        self.logger.info(f"Agent chain completed. {sum(1 for r in results if r.success)}/{len(results)} agents successful")

        return results

    def get_chain_summary(self, results: List[AgentResult]) -> Dict[str, Any]:
        """Get summary of chain execution."""
        if not results:
            return {"status": "no_execution", "agents": 0}

        successful = sum(1 for r in results if r.success)
        total_time = sum(r.execution_time for r in results)

        return {
            "status": "success" if successful == len(results) else "partial_failure" if successful > 0 else "failure",
            "total_agents": len(self.agents),
            "executed_agents": len(results),
            "successful_agents": successful,
            "failed_agents": len(results) - successful,
            "total_execution_time": total_time,
            "average_execution_time": total_time / len(results) if results else 0,
            "agent_names": [agent.name for agent in self.agents],
            "execution_order": [r.metadata.get("agent_name", "unknown") for r in results]
        }
