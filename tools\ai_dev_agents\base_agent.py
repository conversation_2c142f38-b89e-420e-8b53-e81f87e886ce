"""
Base Agent Class

Foundation class for all AI development agents.
"""

import json
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime

try:
    from langchain.schema import BaseMessage, HumanMessage, SystemMessage
    from langchain.callbacks.base import BaseCallbackHandler
except ImportError:
    # Fallback for when langchain is not installed
    class BaseMessage:
        def __init__(self, content: str):
            self.content = content

    class HumanMessage(BaseMessage):
        pass

    class SystemMessage(BaseMessage):
        pass

    class BaseCallbackHandler:
        pass


@dataclass
class AgentResult:
    """Result from an agent execution."""
    success: bool
    data: Dict[str, Any]
    metadata: Dict[str, Any]
    errors: List[str]
    execution_time: float
    timestamp: datetime


@dataclass
class WorkflowContext:
    """Context shared across all agents in the workflow."""
    project_root: str
    project_rules: str
    existing_modules: List[str]
    tech_stack: List[str]
    architecture_style: str
    intermediate_results: Dict[str, Any]
    
    def add_result(self, agent_name: str, result: AgentR<PERSON>ult) -> None:
        """Add agent result to context."""
        self.intermediate_results[agent_name] = result.data
    
    def get_result(self, agent_name: str) -> Optional[Dict[str, Any]]:
        """Get result from previous agent."""
        return self.intermediate_results.get(agent_name)


class AgentCallbackHandler(BaseCallbackHandler):
    """Custom callback handler for agent monitoring."""
    
    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.logger = logging.getLogger(f"agent.{agent_name}")
    
    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        self.logger.info(f"[{self.agent_name}] LLM started with {len(prompts)} prompts")
    
    def on_llm_end(self, response, **kwargs) -> None:
        self.logger.info(f"[{self.agent_name}] LLM completed")
    
    def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs) -> None:
        self.logger.error(f"[{self.agent_name}] LLM error: {error}")


class BaseAgent(ABC):
    """Base class for all AI development agents."""
    
    def __init__(self, name: str, llm=None, verbose: bool = False):
        self.name = name
        self.llm = llm
        self.verbose = verbose
        self.logger = logging.getLogger(f"agent.{name}")
        self.callback_handler = AgentCallbackHandler(name)
        
        # Setup logging
        if verbose:
            logging.basicConfig(level=logging.INFO)
    
    @abstractmethod
    def get_system_prompt(self) -> str:
        """Get the system prompt for this agent."""
        pass
    
    @abstractmethod
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Process input data and return result."""
        pass
    
    def _create_messages(self, system_prompt: str, user_input: str) -> List[BaseMessage]:
        """Create message list for LLM."""
        return [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_input)
        ]
    
    def _parse_json_response(self, response: str) -> Dict[str, Any]:
        """Parse JSON response from LLM."""
        try:
            # Try to extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx != -1 and end_idx != 0:
                json_str = response[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # If no JSON found, return the response as text
                return {"content": response}
                
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse JSON response: {e}")
            return {"content": response, "parse_error": str(e)}
    
    def _validate_required_fields(self, data: Dict[str, Any], required_fields: List[str]) -> List[str]:
        """Validate that required fields are present in data."""
        errors = []
        for field in required_fields:
            if field not in data or not data[field]:
                errors.append(f"Missing required field: {field}")
        return errors
    
    def _execute_with_retry(self, messages: List[BaseMessage], max_retries: int = 3) -> str:
        """Execute LLM call with retry logic."""
        for attempt in range(max_retries):
            try:
                if self.llm is None:
                    # Return mock response for testing
                    self.logger.info("Using mock response (LLM not configured)")
                    return self._get_mock_response()

                response = self.llm.invoke(messages)

                # Handle different response types
                if hasattr(response, 'content'):
                    return response.content
                elif isinstance(response, str):
                    return response
                else:
                    return str(response)

            except Exception as e:
                self.logger.warning(f"Attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    # Return mock response as fallback
                    self.logger.info("Using mock response as fallback")
                    return self._get_mock_response()

        raise RuntimeError(f"Failed to execute after {max_retries} attempts")

    def _get_mock_response(self) -> str:
        """Get mock response for testing when LLM is not available."""
        # This should be overridden by subclasses to provide agent-specific mock responses
        return '{"mock": true, "message": "This is a mock response for testing"}'

    def _log_execution(self, input_data: Dict[str, Any], result: AgentResult) -> None:
        """Log agent execution details."""
        self.logger.info(f"Agent {self.name} executed successfully: {result.success}")
        if result.errors:
            self.logger.warning(f"Errors: {result.errors}")
        self.logger.info(f"Execution time: {result.execution_time:.2f}s")
    
    def execute(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Execute the agent with error handling and logging."""
        start_time = datetime.now()
        
        try:
            self.logger.info(f"Starting agent {self.name}")
            result = self.process(input_data, context)
            
            # Add execution metadata
            result.execution_time = (datetime.now() - start_time).total_seconds()
            result.timestamp = datetime.now()
            
            # Log execution
            self._log_execution(input_data, result)
            
            # Add result to context
            if result.success:
                context.add_result(self.name, result)
            
            return result
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self.logger.error(f"Agent {self.name} failed: {e}")
            
            return AgentResult(
                success=False,
                data={},
                metadata={"agent_name": self.name},
                errors=[str(e)],
                execution_time=execution_time,
                timestamp=datetime.now()
            )


class AgentChain:
    """Chain multiple agents together."""
    
    def __init__(self, agents: List[BaseAgent]):
        self.agents = agents
        self.logger = logging.getLogger("agent.chain")
    
    def execute(self, initial_input: Dict[str, Any], context: WorkflowContext) -> List[AgentResult]:
        """Execute all agents in sequence."""
        results = []
        current_input = initial_input
        
        for agent in self.agents:
            self.logger.info(f"Executing agent: {agent.name}")
            result = agent.execute(current_input, context)
            results.append(result)
            
            if not result.success:
                self.logger.error(f"Agent {agent.name} failed, stopping chain")
                break
            
            # Use agent output as input for next agent
            current_input = result.data
        
        return results
