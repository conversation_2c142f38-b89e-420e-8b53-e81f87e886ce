#!/usr/bin/env python3
"""
AI Development Workflow CLI

Command-line interface for the AI development workflow tools.
"""

import argparse
import sys
from pathlib import Path

# Add the parent directory to the path so we can import the modules
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scripts.ai_dev_workflow.workflow import AIDevWorkflow
from scripts.ai_dev_workflow.prd_analyzer import PRDAnalyzer
from scripts.ai_dev_workflow.domain_identifier import DomainIdentifier
from scripts.ai_dev_workflow.requirements_generator import RequirementsGenerator
from scripts.ai_dev_workflow.prompt_builder import PromptBuilder


def cmd_analyze_prd(args):
    """Analyze PRD document."""
    analyzer = PRDAnalyzer()
    analyzer.load_prd(args.prd_file)
    result = analyzer.analyze()
    analyzer.export_analysis(result, args.output)
    print(f"PRD analysis completed: {args.output}")


def cmd_identify_domains(args):
    """Identify domain modules."""
    identifier = DomainIdentifier()
    identifier.load_analysis(args.analysis_file)
    identifier.scan_existing_modules(args.modules_dir)
    
    contexts = identifier.identify_bounded_contexts()
    modules = identifier.create_domain_modules(contexts)
    identifier.export_domain_analysis(modules, args.output)
    
    print(f"Domain analysis completed: {args.output}")
    print(f"Identified {len(modules)} modules:")
    for module in modules:
        print(f"  - {module.name}: {module.bounded_context.description}")


def cmd_generate_requirements(args):
    """Generate development requirements."""
    generator = RequirementsGenerator()
    generator.load_domain_analysis(args.analysis_file)
    requirement = generator.generate_requirements(args.module_name)
    generator.export_requirements(requirement, args.output)
    print(f"Requirements generated for {args.module_name}: {args.output}")


def cmd_build_prompt(args):
    """Build AI development prompt."""
    builder = PromptBuilder()
    prompt = builder.build_prompt(args.requirements_file, args.task)
    builder.export_prompt(prompt, args.output)
    print(f"AI prompt created: {args.output}")


def cmd_run_workflow(args):
    """Run complete workflow."""
    selected_modules = None
    if args.modules:
        selected_modules = [m.strip() for m in args.modules.split(',')]
    
    workflow = AIDevWorkflow(args.output_dir)
    workflow.run_complete_workflow(args.prd_file, selected_modules)


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description='AI Development Workflow Tools',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run complete workflow
  python -m scripts.ai_dev_workflow.cli workflow design/mcp-market-prd.txt

  # Analyze PRD only
  python -m scripts.ai_dev_workflow.cli analyze-prd design/mcp-market-prd.txt

  # Generate requirements for specific module
  python -m scripts.ai_dev_workflow.cli generate-req domain_analysis.json server_catalog
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Workflow command
    workflow_parser = subparsers.add_parser('workflow', help='Run complete AI development workflow')
    workflow_parser.add_argument('prd_file', help='Path to PRD document')
    workflow_parser.add_argument('-o', '--output-dir', default='output', help='Output directory')
    workflow_parser.add_argument('-m', '--modules', help='Comma-separated list of modules to process')
    workflow_parser.set_defaults(func=cmd_run_workflow)
    
    # Analyze PRD command
    prd_parser = subparsers.add_parser('analyze-prd', help='Analyze PRD document')
    prd_parser.add_argument('prd_file', help='Path to PRD document')
    prd_parser.add_argument('-o', '--output', default='prd_analysis.json', help='Output JSON file')
    prd_parser.set_defaults(func=cmd_analyze_prd)
    
    # Identify domains command
    domain_parser = subparsers.add_parser('identify-domains', help='Identify domain modules')
    domain_parser.add_argument('analysis_file', help='Path to PRD analysis JSON file')
    domain_parser.add_argument('-o', '--output', default='domain_analysis.json', help='Output JSON file')
    domain_parser.add_argument('-m', '--modules-dir', default='modules', help='Existing modules directory')
    domain_parser.set_defaults(func=cmd_identify_domains)
    
    # Generate requirements command
    req_parser = subparsers.add_parser('generate-req', help='Generate development requirements')
    req_parser.add_argument('analysis_file', help='Path to domain analysis JSON file')
    req_parser.add_argument('module_name', help='Name of module to generate requirements for')
    req_parser.add_argument('-o', '--output', help='Output markdown file')
    req_parser.set_defaults(func=cmd_generate_requirements)
    
    # Build prompt command
    prompt_parser = subparsers.add_parser('build-prompt', help='Build AI development prompt')
    prompt_parser.add_argument('requirements_file', help='Path to requirements markdown file')
    prompt_parser.add_argument('-t', '--task', default='Implement module according to requirements', 
                              help='Task description')
    prompt_parser.add_argument('-o', '--output', help='Output prompt file')
    prompt_parser.set_defaults(func=cmd_build_prompt)
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Set default output paths if not provided
    if hasattr(args, 'output') and not args.output:
        if args.command == 'generate-req':
            args.output = f"{args.module_name}_requirements.md"
        elif args.command == 'build-prompt':
            module_name = Path(args.requirements_file).stem.replace('_requirements', '')
            args.output = f"{module_name}_ai_prompt.md"
    
    # Execute the command
    try:
        args.func(args)
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
