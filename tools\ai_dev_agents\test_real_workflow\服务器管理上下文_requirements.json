{"module_overview": {"module_name": "server_management", "bounded_context": "Server Management Context", "primary_responsibility": "MCP server lifecycle management including submission, update, deletion and query", "business_value": "Centralized management of MCP servers with quality metrics and categorization", "core_capabilities": ["MCP server registration and management", "Server categorization", "Quality metrics calculation"], "external_dependencies": ["user", "auth", "oauth_provider"]}, "user_stories": [{"id": "US-SM-001", "title": "Register new MCP server", "story": "As a developer, I want to register a new MCP server so that it becomes available for use", "acceptance_criteria": ["Given valid server details, when POST /api/v1/servers is called, then server is created with unique slug", "Given duplicate slug, when POST /api/v1/servers is called, then return 409 Conflict"], "priority": "高", "story_points": 5, "business_rules": ["Server slug must be unique", "Server must have at least one tool definition"]}, {"id": "US-SM-002", "title": "Update server details", "story": "As a developer, I want to update my MCP server details to keep information current", "acceptance_criteria": ["Given valid server ID, when PATCH /api/v1/servers/{server_id} is called, then server details are updated", "Given invalid server ID, when PATCH is called, then return 404 Not Found"], "priority": "中", "story_points": 3, "business_rules": ["Only server owner can update details", "Slug cannot be changed after creation"]}, {"id": "US-SM-003", "title": "Categorize servers", "story": "As an admin, I want to categorize MCP servers to improve discoverability", "acceptance_criteria": ["Given unique category name, when POST /api/v1/categories is called, then category is created", "Given duplicate category name, when POST is called, then return 409 Conflict"], "priority": "中", "story_points": 3, "business_rules": ["Category names must be unique", "Only admins can create categories"]}], "api_design": {"base_path": "/api/v1", "endpoints": [{"method": "POST", "path": "/servers", "summary": "Create new MCP server", "description": "Register a new MCP server with required details and tool definitions", "request_schema": "ServerCreateRequest", "response_schema": "ServerDetailResponse", "authentication_required": true, "authorization_roles": ["developer"], "business_logic": "Validates slug uniqueness, creates server with tools, calculates initial quality metrics"}, {"method": "GET", "path": "/servers", "summary": "List MCP servers", "description": "Get paginated list of MCP servers with filtering options", "request_schema": "ServerListRequest", "response_schema": "ServerListResponse", "authentication_required": false, "authorization_roles": [], "business_logic": "Applies filters, pagination and sorting"}, {"method": "PATCH", "path": "/servers/{server_id}", "summary": "Update server details", "description": "Update non-slug fields of an MCP server", "request_schema": "ServerUpdateRequest", "response_schema": "ServerDetailResponse", "authentication_required": true, "authorization_roles": ["developer"], "business_logic": "Validates ownership, updates fields, recalculates quality metrics if needed"}, {"method": "POST", "path": "/categories", "summary": "Create server category", "description": "Create a new category for organizing MCP servers", "request_schema": "CategoryCreateRequest", "response_schema": "CategoryResponse", "authentication_required": true, "authorization_roles": ["admin"], "business_logic": "Validates category name uniqueness"}], "schemas": [{"name": "ServerCreateRequest", "type": "request", "fields": [{"name": "slug", "type": "string", "required": true, "description": "Unique URL-friendly identifier", "validation_rules": ["min_length:3", "max_length:50", "regex:^[a-z0-9-]+$"]}, {"name": "name", "type": "string", "required": true, "description": "Human-readable server name", "validation_rules": ["min_length:3", "max_length:100"]}, {"name": "tool_definitions", "type": "array[ToolDefinition]", "required": true, "description": "List of tools provided by this server", "validation_rules": ["min_items:1"]}]}, {"name": "ToolDefinition", "type": "object", "fields": [{"name": "tool_name", "type": "string", "required": true, "description": "Unique name of the tool within server", "validation_rules": ["min_length:3", "max_length:50"]}, {"name": "description", "type": "string", "required": false, "description": "Tool description", "validation_rules": ["max_length:500"]}]}]}, "data_models": {"tables": [{"name": "mcp_servers", "description": "Core table for MCP server instances", "columns": [{"name": "server_id", "type": "UUID", "constraints": ["PRIMARY KEY", "DEFAULT gen_random_uuid()"], "description": "Unique identifier"}, {"name": "slug", "type": "VARCHAR(50)", "constraints": ["NOT NULL", "UNIQUE"], "description": "URL-friendly unique identifier"}, {"name": "name", "type": "VARCHAR(100)", "constraints": ["NOT NULL"], "description": "Human-readable name"}, {"name": "author_id", "type": "UUID", "constraints": ["NOT NULL", "REFERENCES users(user_id)"], "description": "Owner of the server"}, {"name": "date_added", "type": "TIMESTAMPTZ", "constraints": ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"], "description": "Creation timestamp"}, {"name": "last_updated", "type": "TIMESTAMPTZ", "constraints": ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"], "description": "Last modification timestamp"}], "indexes": [{"name": "idx_mcp_servers_slug", "columns": ["slug"], "type": "btree", "unique": true}, {"name": "idx_mcp_servers_author", "columns": ["author_id"], "type": "btree", "unique": false}]}, {"name": "tool_definitions", "description": "Tools provided by MCP servers", "columns": [{"name": "tool_id", "type": "UUID", "constraints": ["PRIMARY KEY", "DEFAULT gen_random_uuid()"], "description": "Unique identifier"}, {"name": "server_id", "type": "UUID", "constraints": ["NOT NULL", "REFERENCES mcp_servers(server_id)"], "description": "Parent server"}, {"name": "tool_name", "type": "VARCHAR(50)", "constraints": ["NOT NULL"], "description": "Unique within server"}, {"name": "description", "type": "TEXT", "constraints": [], "description": "Tool description"}], "indexes": [{"name": "idx_tool_definitions_server", "columns": ["server_id"], "type": "btree", "unique": false}, {"name": "idx_tool_definitions_unique_name", "columns": ["server_id", "tool_name"], "type": "btree", "unique": true}]}]}, "business_logic": {"domain_entities": [{"name": "MCP_Server", "aggregate": "MCP_Server", "attributes": [{"name": "server_id", "type": "UUID", "validation_rules": ["required"]}, {"name": "slug", "type": "string", "validation_rules": ["required", "min_length:3", "max_length:50", "pattern:^[a-z0-9-]+$"]}, {"name": "tool_definitions", "type": "List[ToolDefinition]", "validation_rules": ["min_length:1"]}], "business_methods": [{"name": "add_tool_definition", "description": "Add new tool to server", "parameters": ["tool_name", "description"], "business_rules": ["Tool name must be unique within server", "Tool name must be valid"], "exceptions": ["DuplicateToolError", "InvalidToolNameError"]}, {"name": "calculate_quality_metrics", "description": "Calculate quality score based on various factors", "parameters": [], "business_rules": ["Score must be between 0 and 100", "Must consider completeness, documentation, and activity"], "exceptions": []}], "invariants": ["Slug must remain immutable after creation", "Must have at least one tool definition"]}, {"name": "ToolDefinition", "aggregate": "MCP_Server", "attributes": [{"name": "tool_name", "type": "string", "validation_rules": ["required", "min_length:3", "max_length:50"]}], "business_methods": [], "invariants": ["Tool name must be unique within parent server"]}], "application_services": [{"name": "ServerManagementService", "description": "Handles core server operations", "methods": [{"name": "register_server", "description": "Register new MCP server with tools", "use_case": "US-SM-001", "transaction_boundary": true, "dependencies": ["ServerRepository", "UserRepository"]}, {"name": "update_server_details", "description": "Update non-slug server fields", "use_case": "US-SM-002", "transaction_boundary": true, "dependencies": ["ServerRepository", "AuthorizationService"]}]}], "repositories": [{"name": "ServerRepository", "entity": "MCP_Server", "interface_methods": [{"name": "get_by_id", "description": "Find server by ID", "parameters": ["server_id"], "return_type": "Optional[MCP_Server]"}, {"name": "get_by_slug", "description": "Find server by slug", "parameters": ["slug"], "return_type": "Optional[MCP_Server]"}, {"name": "add", "description": "Persist new server", "parameters": ["server"], "return_type": "None"}]}]}, "testing_strategy": {"unit_tests": [{"target": "MCP_Server entity", "test_cases": [{"name": "should_throw_when_adding_duplicate_tool", "description": "Test tool name uniqueness within server", "given": "Server with existing tool 'test-tool'", "when": "Adding another tool with name 'test-tool'", "then": "Throws DuplicateToolError"}]}], "integration_tests": [{"scope": "Server registration", "scenarios": [{"name": "successful_server_registration", "description": "Complete flow of registering new server", "test_data": "Valid server data with unique slug and tools", "expected_outcome": "Server created in DB with all tools"}]}], "api_tests": [{"endpoint": "/api/v1/servers", "test_cases": [{"name": "create_server_unauthorized", "method": "POST", "request_data": "Valid server data", "expected_status": 401, "expected_response": "Authentication required"}]}]}, "technical_constraints": {"architecture_constraints": ["Strict DDD layers: domain, application, infrastructure, interface", "Domain models must not depend on external frameworks", "Use UUIDv7 for all primary keys", "Pydantic for API schema validation"], "performance_requirements": ["Server list API response < 300ms at 1000 servers", "Database queries must use proper indexes"], "security_requirements": ["All mutating endpoints require authentication", "Input validation for all API parameters", "Owner checks for update/delete operations"], "quality_requirements": ["90% code coverage for domain layer", "100% OpenAPI schema coverage", "PEP 8 compliance with flake8"]}, "implementation_order": ["1. Domain layer - MCP_Server and ToolDefinition entities with validation", "2. Infrastructure layer - SQLAlchemy models and ServerRepository implementation", "3. Application layer - ServerManagementService with transaction management", "4. Interface layer - FastAPI routers and Pydantic schemas", "5. Database migrations for server and tool tables", "6. Unit tests for domain entities", "7. Integration tests for service layer", "8. API tests with authentication scenarios"], "project_metadata": {"generation_timestamp": "2025-06-25T10:14:06.684580", "architecture_style": "DDD + FastAPI", "tech_stack": ["FastAPI", "SQLAlchemy", "Pydantic", "Alembic", "<PERSON><PERSON><PERSON>"], "existing_modules": ["user", "auth", "oauth_provider"]}, "dependency_analysis": {"internal_dependencies": [], "external_dependencies": ["user", "auth", "oauth_provider"], "cross_module_dependencies": ["user", "auth", "oauth_provider"]}, "complexity_estimation": {"story_points_total": 11, "api_endpoints_count": 4, "database_tables_count": 2, "domain_entities_count": 2, "estimated_development_days": 5.5}}