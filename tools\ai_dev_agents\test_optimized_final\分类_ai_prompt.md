```markdown
# AI 开发任务提示词

生成时间: 2023-11-15

## 任务描述

### 开发任务
开发一个功能分类管理模块(category_service)，用于管理MCP服务器功能分类，提供以下功能：
1. 创建新的功能分类
2. 列出所有功能分类

### 预期交付成果
1. 完整的DDD四层架构实现
2. 2个REST API端点(POST /, GET /)
3. 分类数据模型及持久化实现
4. 单元测试和集成测试套件
5. 中文API文档和代码注释

### 成功标准
1. 所有功能通过验收测试
2. 代码符合DDD架构规范
3. 测试覆盖率≥80%
4. API文档完整准确

## 项目上下文

### 架构风格
- 领域驱动设计 (DDD) 四层架构
- 整洁架构原则
- REST API with FastAPI

### 技术栈
- Python 3.10+
- FastAPI (Web框架)
- SQLAlchemy 2.0 (ORM)
- Pydantic v2 (数据验证)
- Alembic (数据库迁移)
- Pytest (测试框架)
- UUID (主键生成)

### 编码标准
- 严格遵循 PEP 8 规范
- 强制类型提示 (Type Hinting)
- 中文注释和文档字符串
- 120 字符行长度限制
- Black 代码格式化

### 现有模块
- oauth_provider: OAuth2认证模块
- auth: 认证授权基础模块  
- user: 用户管理模块

## 架构约束 (必须严格遵循)

### DDD 四层架构
```
modules/category_service/
├── __init__.py
├── interfaces/          
│   ├── category_api.py
│   └── schemas.py
├── application/         
│   ├── services.py
│   └── dtos.py
├── domain/             
│   ├── models.py
│   └── repositories.py
└── infrastructure/     
    ├── repositories.py
    └── orm.py
```

### 依赖方向规则
- interfaces → application → domain ← infrastructure
- 严禁循环依赖
- Domain 层不依赖任何外部框架

### 模块边界
- 通过Application层服务进行模块间通信
- 禁止直接访问其他模块的Domain或Infrastructure层

### 数据建模约束
- 使用UUID主键
- SQLAlchemy 2.0风格ORM模型
- Pydantic v2用于接口数据验证

## 实现指南

### 开发步骤 (Domain First 原则)

1. **领域层设计**
   ```python
   # modules/category_service/domain/models.py
   from uuid import UUID
   from dataclasses import dataclass

   @dataclass
   class Category:
       id: UUID
       name: str
       description: str | None
       created_at: datetime
   ```

   ```python
   # modules/category_service/domain/repositories.py
   from abc import ABC, abstractmethod
   from .models import Category

   class CategoryRepository(ABC):
       @abstractmethod
       async def create(self, category: Category) -> None: ...
       
       @abstractmethod
       async def list_all(self) -> list[Category]: ...
   ```

2. **应用层实现**
   ```python
   # modules/category_service/application/services.py
   from uuid import UUID, uuid4
   from datetime import datetime
   from domain.models import Category
   from domain.repositories import CategoryRepository

   class CategoryService:
       def __init__(self, repo: CategoryRepository):
           self._repo = repo

       async def create_category(self, name: str, description: str | None) -> UUID:
           category = Category(
               id=uuid4(),
               name=name,
               description=description,
               created_at=datetime.utcnow()
           )
           await self._repo.create(category)
           return category.id

       async def list_categories(self) -> list[Category]:
           return await self._repo.list_all()
   ```

3. **基础设施层实现**
   ```python
   # modules/category_service/infrastructure/orm.py
   from sqlalchemy import Column, String, DateTime
   from sqlalchemy.dialects.postgresql import UUID
   from infrastructure.database import Base

   class CategoryORM(Base):
       __tablename__ = "categories"
       
       id = Column(UUID(as_uuid=True), primary_key=True)
       name = Column(String(100), nullable=False)
       description = Column(String(500))
       created_at = Column(DateTime, nullable=False)
   ```

4. **接口层实现**
   ```python
   # modules/category_service/interfaces/category_api.py
   from fastapi import APIRouter, Depends
   from application.services import CategoryService
   from infrastructure.repositories import CategoryRepositoryImpl
   from .schemas import CategoryCreateRequest, CategoryResponse

   router = APIRouter(prefix="/categories")

   @router.post("/", response_model=UUID)
   async def create_category(
       request: CategoryCreateRequest,
       service: CategoryService = Depends(lambda: CategoryService(CategoryRepositoryImpl()))
   ):
       return await service.create_category(request.name, request.description)

   @router.get("/", response_model=list[CategoryResponse])
   async def list_categories(
       service: CategoryService = Depends(lambda: CategoryService(CategoryRepositoryImpl()))
   ):
       return await service.list_categories()
   ```

## 质量要求

### 代码质量标准
- 单元测试覆盖率 ≥ 80%
- 所有公共方法有类型提示
- 中文文档字符串覆盖率 100%
- 通过所有静态代码检查

### 测试要求
- Domain层: 纯单元测试
- Application层: Mock测试
- Infrastructure层: 集成测试
- API层: 端到端测试

### 文档要求
- OpenAPI文档自动生成
- 所有业务方法有中文文档字符串
- README包含模块使用说明

## 验收标准

### 功能验收
1. 成功创建分类并返回UUID
2. 正确列出所有分类
3. 输入验证符合要求

### 技术验收
1. 严格遵循DDD四层架构
2. 依赖方向正确
3. 类型提示完整

### 质量验收
1. 测试覆盖率达标
2. 代码风格一致
3. 文档完整准确
```