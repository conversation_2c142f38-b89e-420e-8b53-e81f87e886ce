```markdown
# AI 开发任务提示词

生成时间: 2023-11-15

## 任务描述

### 开发任务
开发 server_management 模块，实现 MCP 服务器的全生命周期管理功能，包括：
1. 新服务器注册 (POST /servers)
2. 服务器信息查询 (GET /servers)
3. 服务器信息更新 (PATCH /servers/{server_id})
4. 服务器分类管理

### 预期交付成果
1. 符合 DDD 四层架构的 server_management 模块
2. 完整的 REST API 实现
3. 集成测试和单元测试套件
4. 中文 API 文档和代码注释

### 成功标准
1. 所有用户故事 (US-SM-001 至 US-SM-003) 实现并通过测试
2. 代码符合项目架构约束和质量标准
3. API 通过 OpenAPI 规范验证

## 项目上下文

### 架构风格
- 领域驱动设计 (DDD) 四层架构
- 整洁架构原则
- REST API with FastAPI

### 技术栈
- Python 3.10+
- FastAPI
- SQLAlchemy 2.0
- Pydantic v2
- Alembic (数据库迁移)
- Pytest (测试框架)

### 编码标准
- 严格遵循 PEP 8 规范
- 强制类型提示 (Type Hinting)
- 中文注释和文档字符串
- 120 字符行长度限制
- Black 代码格式化

### 现有模块
- user: 用户管理模块
- oauth_provider: OAuth 认证提供者
- auth: 认证授权模块

## 架构约束 (必须严格遵循)

### DDD 四层架构
```
modules/server_management/
├── __init__.py
├── interfaces/          # 接口层
│   ├── server_management_api.py
│   └── schemas.py
├── application/         # 应用层
│   ├── services.py
│   └── dtos.py
├── domain/             # 领域层
│   ├── models.py
│   └── repositories.py
└── infrastructure/     # 基础设施层
    ├── repositories.py
    └── orm.py
```

### 依赖方向规则
- interfaces → application → domain ← infrastructure
- 严禁循环依赖
- Domain 层保持纯粹，不依赖任何外部框架

### 模块边界
- 模块间通信必须通过 Application 层服务接口
- 严禁直接访问其他模块的 Domain 或 Infrastructure 层
- 通用代码放置在 common/ 目录下

### 数据建模约束
- 所有实体 ID 字段使用 UUID 类型
- SQLAlchemy 2.0 风格模型
- Pydantic v2 用于数据验证

## 实现指南

### 开发步骤 (Domain First 原则)
1. **领域层设计** (modules/server_management/domain/)
   - 在 models.py 中定义 McpServer 和 ToolDefinition 实体
   - 在 repositories.py 中定义 ServerRepository 抽象接口

2. **应用层实现** (modules/server_management/application/)
   - 在 services.py 中创建 ServerManagementService
   - 实现服务器注册、查询、更新等用例逻辑
   - 在 dtos.py 中定义数据传输对象

3. **基础设施层实现** (modules/server_management/infrastructure/)
   - 在 orm.py 中创建 SQLAlchemy ORM 模型
   - 在 repositories.py 中实现 ServerRepositoryImpl

4. **接口层实现** (modules/server_management/interfaces/)
   - 在 schemas.py 中创建请求/响应模型
   - 在 server_management_api.py 中实现 FastAPI 路由

5. **集成到主应用**
   - 在 main.py 中注册路由
   - 配置依赖注入

### 代码组织原则
- 业务逻辑集中在 Domain 层
- API 相关代码放在 interfaces 层
- 数据库操作在 infrastructure 层
- 跨领域关注点在 application 层

## 质量要求

### 代码质量标准
- 单元测试覆盖率 > 80%
- 所有公共方法必须有类型提示
- 中文注释和文档字符串
- 通过 CI 代码质量检查

### 测试要求
- Domain 层：纯单元测试，验证业务规则
- Application 层：Mock 仓库测试用例逻辑
- Infrastructure 层：集成测试，验证数据库操作
- interfaces 层：API 端到端测试

### 文档要求
- 所有 API 端点包含完整的 OpenAPI 文档
- 业务逻辑方法包含中文文档字符串
- README 文档更新模块说明

## 开发需求详情

### 领域模型设计
```python
# domain/models.py
class McpServer:
    id: UUID
    name: str
    endpoint: str
    status: ServerStatus
    categories: List[str]
    metrics: ServerMetrics

class ToolDefinition:
    id: UUID
    name: str
    version: str
    server_id: UUID
```

### API 端点规范
```python
# interfaces/schemas.py
class ServerCreateSchema(BaseModel):
    name: str
    endpoint: str
    categories: List[str]

class ServerUpdateSchema(BaseModel):
    name: Optional[str]
    categories: Optional[List[str]]
```

### 应用服务示例
```python
# application/services.py
class ServerManagementService:
    def __init__(self, server_repo: ServerRepository):
        self.server_repo = server_repo

    def register_server(self, dto: ServerCreateDTO) -> McpServer:
        # 实现注册逻辑
        pass
```

## 验收标准

### 功能验收标准
1. 成功实现所有 API 端点 (POST/GET/PATCH)
2. 支持服务器分类管理功能
3. 符合 OpenAPI 3.0 规范

### 技术验收标准
1. 严格遵循 DDD 四层架构
2. 通过所有单元测试和集成测试
3. 代码覆盖率 >= 80%

### 质量验收标准
1. 代码通过静态类型检查
2. 文档完整且符合标准
3. 无重大代码异味
```