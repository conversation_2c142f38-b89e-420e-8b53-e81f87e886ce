#!/usr/bin/env python3
"""
Simple test script for AI Development Agents with Configuration

Usage:
    python tools/simple_test.py [preset]

Examples:
    python tools/simple_test.py                    # Use default config
    python tools/simple_test.py high_quality       # Use high_quality preset
    python tools/simple_test.py economical         # Use economical preset

Configuration:
    Edit tools/config.yaml or set environment variables:
    - OPENROUTER_API_KEY: Your OpenRouter API key
    - OPENROUTER_MODEL: Model to use (optional)
"""

import sys
import json
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Simple test with configuration-based LLM."""
    # Parse command line arguments
    preset = sys.argv[1] if len(sys.argv) > 1 else None

    print("🚀 Simple AI Agent Test with Configuration")
    print("=" * 50)

    # Check dependencies
    try:
        from tools.ai_dev_agents.config_manager import ConfigManager
        from tools.ai_dev_agents import AIDevWorkflowOrchestrator
        print("✅ AI Development Agents available")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Please ensure all dependencies are installed")
        return 1
    
    # Initialize configuration manager
    print(f"\n🔧 Loading configuration...")
    config_manager = ConfigManager()

    # Validate configuration
    validation = config_manager.validate_config()
    if not validation["valid"]:
        print("❌ Configuration validation failed:")
        for error in validation["errors"]:
            print(f"   - {error}")
        print("\n💡 Please check your configuration:")
        print("   1. Edit tools/config.yaml")
        print("   2. Set environment variables (OPENROUTER_API_KEY)")
        print("   3. See tools/OPENROUTER_CONFIG.md for detailed instructions")
        return 1

    if validation["warnings"]:
        for warning in validation["warnings"]:
            print(f"⚠️ {warning}")

    # Show configuration info
    llm_config = config_manager.get_llm_config(preset)
    print(f"✅ Configuration loaded:")
    print(f"   - Provider: {llm_config.provider}")
    print(f"   - Model: {llm_config.model}")
    print(f"   - Preset: {preset or 'default'}")
    print(f"   - API Key: {'✅ Set' if llm_config.api_key else '❌ Not set'}")

    if preset:
        available_presets = config_manager.list_available_presets()
        if preset in available_presets:
            print(f"   - Description: {available_presets[preset]}")
        else:
            print(f"⚠️ Warning: Preset '{preset}' not found. Using default configuration.")
            print(f"   Available presets: {', '.join(available_presets.keys())}")
    
    try:
        # Create orchestrator with configuration
        print("\n🤖 Creating AI Development Orchestrator...")
        orchestrator = AIDevWorkflowOrchestrator(
            config_path=None,  # Use default config
            model_preset=preset,
            verbose=True
        )
        print(f"✅ Orchestrator created successfully")

        # Create context
        print("\n🏗️ Creating workflow context...")
        context = orchestrator.create_context(str(project_root))
        print(f"✅ Context created:")
        print(f"   - Existing modules: {context.existing_modules}")
        print(f"   - Tech stack: {', '.join(context.tech_stack[:3])}...")
        print(f"   - Architecture: {context.architecture_style}")
        
        # Sample PRD for testing
        sample_prd = """
# MCP Server Market Platform

## Project Overview
The MCP Server Market Platform is a centralized marketplace for Model Context Protocol (MCP) servers.

## Core Features
1. **Server Discovery**: Browse and search MCP servers
2. **Server Submission**: Authors can submit their servers
3. **User Management**: Registration and authentication

## User Stories
- As a developer, I want to discover MCP servers that match my needs
- As a server author, I want to publish my MCP server to reach more users
- As a platform admin, I want to manage server quality and user accounts

## Requirements
- RESTful API for all operations
- Database persistence for servers and users
- Authentication and authorization
- Search and filtering capabilities
"""
        
        # Test business analysis
        print("\n🔍 Running business analysis...")
        result = orchestrator._execute_business_analysis(sample_prd)
        
        if result.success:
            print("✅ Business analysis completed successfully!")
            
            # Display key results
            data = result.data
            business_overview = data.get('business_overview', {})
            print(f"\n📋 Results Summary:")
            print(f"   - Project: {business_overview.get('project_name', 'N/A')}")
            print(f"   - Core Entities: {len(data.get('core_entities', []))}")
            print(f"   - Functional Requirements: {len(data.get('functional_requirements', []))}")
            print(f"   - User Stories: {len(data.get('user_stories', []))}")
            print(f"   - Business Rules: {len(data.get('business_rules', []))}")
            
            # Save detailed results
            output_file = project_root / "business_analysis_result.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Detailed results saved to: {output_file}")
            
            # Show sample entities
            entities = data.get('core_entities', [])
            if entities:
                print(f"\n🏗️ Sample Core Entities:")
                for i, entity in enumerate(entities[:3]):  # Show first 3
                    print(f"   {i+1}. {entity.get('name', 'Unknown')}: {entity.get('description', 'No description')}")
            
            print("\n🎉 Test completed successfully!")
            print("📄 Check the business_analysis_result.json file for complete results.")
            return 0
            
        else:
            print(f"❌ Business analysis failed:")
            for error in result.errors:
                print(f"   - {error}")
            return 1
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
