#!/usr/bin/env python3
"""
Simple test script for AI Development Agents with OpenRouter

Usage:
    python tools/simple_test.py

Configuration:
    Set environment variables or modify the config in this script:
    - OPENROUTER_API_KEY: Your OpenRouter API key
    - OPENROUTER_MODEL: Model to use (default: anthropic/claude-3-sonnet)
    - OPENROUTER_BASE_URL: API endpoint (default: https://openrouter.ai/api/v1)
"""

import sys
import json
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Simple test with OpenRouter LLM."""
    print("🚀 Simple AI Agent Test with OpenRouter")
    print("=" * 50)
    
    # Check LangChain availability
    try:
        from langchain_openai import ChatOpenAI
        print("✅ LangChain available")
    except ImportError:
        print("❌ LangChain not available. Please install:")
        print("   pip install langchain-openai")
        return 1
    
    # Configuration
    config = {
        "api_key": os.getenv("OPENROUTER_API_KEY", ""),
        "model": os.getenv("OPENROUTER_MODEL", "anthropic/claude-3-sonnet"),
        "base_url": os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1"),
        "temperature": 0.1,
        "max_tokens": 2000
    }
    
    print(f"\n🔧 Configuration:")
    print(f"   - API Key: {'✅ Set' if config['api_key'] else '❌ Not set'}")
    print(f"   - Model: {config['model']}")
    print(f"   - Base URL: {config['base_url']}")
    
    if not config["api_key"]:
        print("\n❌ Please provide your OpenRouter API key:")
        print("   Option 1: Set environment variable:")
        print("     export OPENROUTER_API_KEY='your-api-key-here'")
        print("   Option 2: Modify this script and set api_key directly")
        
        # Uncomment and set your API key here if you prefer:
        # config["api_key"] = "your-api-key-here"
        
        return 1
    
    try:
        # Create LLM
        print("\n🤖 Creating OpenRouter LLM...")
        llm = ChatOpenAI(
            api_key=config["api_key"],
            model=config["model"],
            base_url=config["base_url"],
            temperature=config["temperature"],
            max_tokens=config["max_tokens"],
            default_headers={
                "HTTP-Referer": "https://github.com/agilemetrics-tech/ai4se-mcp-hub",
                "X-Title": "AI4SE MCP Hub Development"
            }
        )
        print(f"✅ LLM created successfully")
        
        # Import and test business analyzer
        print("\n📊 Testing Business Analyzer Agent...")
        from tools.ai_dev_agents.business_analyzer import BusinessAnalyzerAgent
        from tools.ai_dev_agents.base_agent import WorkflowContext
        
        # Create agent
        agent = BusinessAnalyzerAgent(llm=llm)
        print("✅ Business Analyzer Agent created")
        
        # Create context
        context = WorkflowContext(
            project_root=str(project_root),
            architecture_style="DDD + FastAPI",
            tech_stack=["FastAPI", "SQLAlchemy", "Pydantic", "Alembic"],
            existing_modules=["auth", "user", "oauth_provider"]
        )
        print("✅ Workflow context created")
        
        # Sample PRD for testing
        sample_prd = """
# MCP Server Market Platform

## Project Overview
The MCP Server Market Platform is a centralized marketplace for Model Context Protocol (MCP) servers.

## Core Features
1. **Server Discovery**: Browse and search MCP servers
2. **Server Submission**: Authors can submit their servers
3. **User Management**: Registration and authentication

## User Stories
- As a developer, I want to discover MCP servers that match my needs
- As a server author, I want to publish my MCP server to reach more users
- As a platform admin, I want to manage server quality and user accounts

## Requirements
- RESTful API for all operations
- Database persistence for servers and users
- Authentication and authorization
- Search and filtering capabilities
"""
        
        # Test the agent
        print("\n🔍 Running business analysis...")
        input_data = {"prd_content": sample_prd}
        result = agent.execute(input_data, context)
        
        if result.success:
            print("✅ Business analysis completed successfully!")
            
            # Display key results
            data = result.data
            business_overview = data.get('business_overview', {})
            print(f"\n📋 Results Summary:")
            print(f"   - Project: {business_overview.get('project_name', 'N/A')}")
            print(f"   - Core Entities: {len(data.get('core_entities', []))}")
            print(f"   - Functional Requirements: {len(data.get('functional_requirements', []))}")
            print(f"   - User Stories: {len(data.get('user_stories', []))}")
            print(f"   - Business Rules: {len(data.get('business_rules', []))}")
            
            # Save detailed results
            output_file = project_root / "business_analysis_result.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Detailed results saved to: {output_file}")
            
            # Show sample entities
            entities = data.get('core_entities', [])
            if entities:
                print(f"\n🏗️ Sample Core Entities:")
                for i, entity in enumerate(entities[:3]):  # Show first 3
                    print(f"   {i+1}. {entity.get('name', 'Unknown')}: {entity.get('description', 'No description')}")
            
            print("\n🎉 Test completed successfully!")
            print("📄 Check the business_analysis_result.json file for complete results.")
            return 0
            
        else:
            print(f"❌ Business analysis failed:")
            for error in result.errors:
                print(f"   - {error}")
            return 1
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
