```markdown
# AI 开发任务提示词

生成时间: 2023-11-15

## 任务描述

### 开发任务
开发一个评论管理模块(review)，用于管理用户对MCP服务器的评价。主要功能包括：
1. 允许注册用户提交服务器评价
2. 允许所有用户查看指定服务器的评价列表

### 预期交付成果
1. 完整的DDD四层架构实现
2. 2个REST API端点实现
3. 数据库迁移脚本
4. 单元测试和集成测试
5. API文档和模块文档

### 成功标准
1. 所有API端点按设计实现并通过测试
2. 代码符合DDD架构规范
3. 测试覆盖率≥80%
4. 通过所有CI检查

## 项目上下文

### 架构风格
- 领域驱动设计 (DDD) 四层架构
- 整洁架构原则
- REST API with FastAPI

### 技术栈
- Python 3.10+
- FastAPI (接口层)
- SQLAlchemy 2.0 (基础设施层)
- Pydantic v2 (数据验证)
- Alembic (数据库迁移)
- Pytest (测试框架)

### 编码标准
- 严格遵循 PEP 8 规范
- 强制类型提示 (Type Hinting)
- 中文注释和文档字符串
- 120 字符行长度限制
- Black 代码格式化

### 现有模块
- oauth_provider: OAuth2认证提供者
- auth: 用户认证模块
- user: 用户管理模块

## 架构约束 (必须严格遵循)

### DDD 四层架构
```
modules/review/
├── __init__.py
├── interfaces/          # 接口层
│   ├── review_api.py
│   └── schemas.py
├── application/         # 应用层
│   ├── services.py
│   └── dtos.py
├── domain/             # 领域层
│   ├── models.py
│   └── repositories.py
└── infrastructure/     # 基础设施层
    ├── repositories.py
    └── orm.py
```

### 依赖方向规则
- interfaces → application → domain ← infrastructure
- 严禁循环依赖
- Domain 层保持纯粹，不依赖任何外部框架

### 模块边界
- 与user模块交互必须通过应用层服务
- 使用现有auth模块进行权限验证
- 通用代码放置在common/目录下

### 数据建模约束
- 使用UUID主键
- SQLAlchemy 2.0风格ORM模型
- Pydantic v2用于API数据验证

## 实现指南

### 开发步骤 (Domain First 原则)
1. **领域层设计** (modules/review/domain/)
   - 在models.py中定义Review实体和评分值对象
   - 在repositories.py中定义ReviewRepository接口

2. **应用层实现** (modules/review/application/)
   - 在services.py中创建ReviewService
   - 实现提交评价和查询评价列表用例
   - 在dtos.py中定义数据传输对象

3. **基础设施层实现** (modules/review/infrastructure/)
   - 在orm.py中创建ReviewORM模型
   - 在repositories.py中实现ReviewRepositoryImpl
   - 编写Alembic迁移脚本

4. **接口层实现** (modules/review/interfaces/)
   - 在schemas.py中定义请求/响应模型
   - 在review_api.py中实现FastAPI路由
   - 集成auth模块的权限验证

5. **集成到主应用**
   - 在main.py中注册/reviews路由
   - 配置依赖注入

### 关键实现要点
1. 评价实体应包含:
   - 评分(1-5)
   - 评论内容
   - 关联用户ID
   - 关联服务器ID
   - 创建时间

2. API端点:
   - POST /api/v1/reviews - 提交评价(需认证)
   - GET /api/v1/reviews/servers/{server_id} - 获取服务器评价列表

## 质量要求

### 代码质量标准
- 单元测试覆盖率 > 80%
- 所有公共方法必须有类型提示
- 中文注释和文档字符串
- 通过CI代码质量检查

### 测试要求
1. 领域层测试:
   - 验证评价实体业务规则
   - 测试评分值对象有效性

2. 应用层测试:
   - 使用mock测试服务逻辑
   - 验证异常处理

3. 接口层测试:
   - API端点测试
   - 权限验证测试

4. 基础设施层测试:
   - 数据库操作测试
   - 使用测试数据库

### 文档要求
- 所有API端点包含OpenAPI文档
- 业务方法包含中文文档字符串
- 模块README说明使用方式

## 验收标准

### 功能验收标准
1. 注册用户可以成功提交评价
2. 所有用户可以查看服务器评价列表
3. 评价数据持久化存储

### 技术验收标准
1. 严格遵循DDD四层架构
2. 正确实现模块间依赖关系
3. 通过所有自动化测试

### 质量验收标准
1. 代码覆盖率≥80%
2. 无PEP 8违规
3. 文档完整且准确
```