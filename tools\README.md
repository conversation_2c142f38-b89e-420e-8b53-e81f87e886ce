# AI Development Agents

AI-powered development workflow for generating code from Product Requirements Documents (PRD) using Domain-Driven Design (DDD) principles.

## 🚀 Quick Start

### 1. Install Dependencies

```bash
# Install required packages
python tools/install_deps.py

# Or manually with uv/pip
uv add pyyaml langchain-openai langchain
# pip install pyyaml langchain-openai langchain
```

### 2. Configure API Key

Set your OpenRouter API key:

```bash
# Windows
set OPENROUTER_API_KEY=your-api-key-here

# macOS/Linux
export OPENROUTER_API_KEY=your-api-key-here
```

### 3. Validate Configuration

```bash
python tools/validate_config.py
```

### 4. Run a Test

```bash
# Basic test
python tools/simple_test.py

# With specific model preset
python tools/simple_test.py high_quality
```

## 📋 Configuration

### Configuration File

The system uses `tools/config.yaml` for configuration. Key sections:

```yaml
llm:
  provider: "openrouter"
  openrouter:
    api_key: "${OPENROUTER_API_KEY}"
    model: "anthropic/claude-3-sonnet"
    temperature: 0.1
    max_tokens: 4000

model_presets:
  high_quality:
    model: "anthropic/claude-3-opus"
    temperature: 0.05
    max_tokens: 8000
  
  economical:
    model: "meta-llama/llama-3.1-8b-instruct"
    temperature: 0.2
    max_tokens: 2000

project:
  architecture_style: "Domain-Driven Design (DDD) with 4-layer architecture"
  tech_stack: ["FastAPI", "SQLAlchemy", "Pydantic", "PostgreSQL"]
  existing_modules: ["auth", "user", "oauth_provider"]
```

### Environment Variables

- `OPENROUTER_API_KEY`: Your OpenRouter API key (required)
- `OPENROUTER_MODEL`: Override default model
- `OPENROUTER_BASE_URL`: Override API endpoint
- `OPENAI_API_KEY`: For direct OpenAI usage
- `ANTHROPIC_API_KEY`: For direct Anthropic usage

### Model Presets

Available presets in default configuration:

- **`default`**: Balanced performance and cost
- **`high_quality`**: Best quality, higher cost
- **`economical`**: Lower cost, faster responses
- **`creative`**: Higher temperature for creative tasks

## 🛠️ Usage

### Command Line Interface

```bash
# Full workflow
python -m tools.ai_dev_agents.cli workflow design/mcp-market-prd.txt

# With configuration options
python -m tools.ai_dev_agents.cli workflow design/mcp-market-prd.txt \
  --preset high_quality \
  --config custom_config.yaml \
  --verbose

# Step by step
python -m tools.ai_dev_agents.cli business-analysis design/mcp-market-prd.txt
python -m tools.ai_dev_agents.cli domain-modeling business_analysis.json
python -m tools.ai_dev_agents.cli generate-requirements domain_model.json --module mcp_server
python -m tools.ai_dev_agents.cli build-prompt mcp_server_requirements.json
```

### Python API

```python
from tools.ai_dev_agents import AIDevWorkflowOrchestrator

# Create orchestrator with configuration
orchestrator = AIDevWorkflowOrchestrator(
    config_path="tools/config.yaml",  # Optional
    model_preset="high_quality",      # Optional
    verbose=True
)

# Execute full workflow
result = orchestrator.execute_full_workflow(
    prd_content=prd_text,
    project_root=".",
    output_dir="output"
)
```

### Simple Testing

```python
# Simple test script
python tools/simple_test.py

# With specific preset
python tools/simple_test.py economical
```

## 🔧 Tools

### Configuration Validation

```bash
python tools/validate_config.py [preset]
```

Validates configuration and shows:
- LLM configuration status
- Available model presets
- Environment variables
- System settings
- Project configuration

### Dependency Installation

```bash
python tools/install_deps.py
```

Installs required and optional dependencies with compatibility checks.

## 📊 Workflow Stages

1. **Business Analysis**: Extract business requirements from PRD
2. **Domain Modeling**: Create DDD domain models and bounded contexts
3. **Requirements Generation**: Generate technical requirements and specifications
4. **Prompt Building**: Create AI development prompts for code generation

Each stage produces structured JSON output that feeds into the next stage.

## 🏗️ Architecture

The system follows a modular architecture:

```
tools/
├── config.yaml                 # Configuration file
├── ai_dev_agents/              # Core agent system
│   ├── base_agent.py          # Base agent class
│   ├── config_manager.py      # Configuration management
│   ├── orchestrator.py        # Workflow orchestrator
│   ├── business_analyzer.py   # Business analysis agent
│   ├── domain_modeler.py      # Domain modeling agent
│   ├── requirements_generator.py # Requirements generation agent
│   ├── prompt_builder.py      # Prompt building agent
│   └── cli.py                 # Command line interface
├── simple_test.py             # Simple testing script
├── validate_config.py         # Configuration validation
├── install_deps.py            # Dependency installer
└── OPENROUTER_CONFIG.md       # Detailed setup guide
```

## 🔍 Troubleshooting

### Common Issues

1. **API Key Not Set**
   ```bash
   python tools/validate_config.py
   # Check environment variables section
   ```

2. **Import Errors**
   ```bash
   python tools/install_deps.py
   # Reinstall dependencies
   ```

3. **Configuration Issues**
   ```bash
   python tools/validate_config.py
   # Check validation errors and warnings
   ```

4. **LLM Creation Failed**
   - Verify API key is correct
   - Check network connectivity
   - Validate model name in configuration

### Debug Mode

Enable verbose logging:

```bash
python tools/simple_test.py --verbose
python -m tools.ai_dev_agents.cli workflow prd.txt --verbose
```

## 📖 Documentation

- `tools/OPENROUTER_CONFIG.md`: Detailed OpenRouter setup guide
- `tools/config.yaml`: Configuration file with comments
- `design/mcp-market-prd.txt`: Sample PRD for testing

## 🤝 Contributing

1. Follow the existing code structure
2. Add tests for new functionality
3. Update configuration documentation
4. Validate changes with `python tools/validate_config.py`

## 📄 License

This project is part of the AI4SE MCP Hub and follows the same license terms.
