{"module_overview": {"module_name": "mcp_server", "bounded_context": "Model Context Protocol", "primary_responsibility": "Manage MCP servers and their tool definitions", "business_value": "Enable discovery and utilization of MCP servers with standardized tool definitions", "core_capabilities": ["MCP server registration and management", "Tool definition management", "Quality metrics calculation"], "external_dependencies": ["user module for authentication", "auth module for authorization", "oauth_provider module for OAuth integration"]}, "user_stories": [{"id": "US-MCP-001", "title": "Register new MCP server", "story": "As a developer, I want to register a new MCP server so that others can discover and use it", "acceptance_criteria": ["Given valid authentication, when I submit server details with unique slug, then server is registered", "Given duplicate slug, when I try to register, then receive 409 Conflict error"], "priority": "高", "story_points": 3, "business_rules": ["Server slug must be unique across system", "All required fields must be provided"]}, {"id": "US-MCP-002", "title": "Add tool definition to server", "story": "As a developer, I want to add tool definitions to my MCP server so users understand available capabilities", "acceptance_criteria": ["Given valid server ID, when I add tool with unique name, then tool is added", "Given duplicate tool name in same server, when I try to add, then receive 409 Conflict error"], "priority": "高", "story_points": 2, "business_rules": ["Tool name must be unique within server", "Tool must belong to exactly one server"]}], "api_design": {"base_path": "/api/v1/mcp", "endpoints": [{"method": "POST", "path": "/servers", "summary": "Register new MCP server", "description": "Creates a new MCP server with provided details", "request_schema": "MCPCreateRequest", "response_schema": "MCPServerResponse", "authentication_required": true, "authorization_roles": ["developer"], "business_logic": "Validates slug uniqueness, creates server record"}, {"method": "GET", "path": "/servers", "summary": "List all MCP servers", "description": "Returns paginated list of registered MCP servers", "request_schema": null, "response_schema": "MCPServerListResponse", "authentication_required": false, "authorization_roles": [], "business_logic": "Fetches all active servers with optional filtering"}, {"method": "POST", "path": "/servers/{server_id}/tools", "summary": "Add tool to server", "description": "Adds a new tool definition to specified MCP server", "request_schema": "ToolCreateRequest", "response_schema": "ToolResponse", "authentication_required": true, "authorization_roles": ["developer"], "business_logic": "Validates tool name uniqueness within server, creates tool record"}], "schemas": [{"name": "MCPCreateRequest", "type": "request", "fields": [{"name": "slug", "type": "string", "required": true, "description": "Unique human-readable identifier for server", "validation_rules": ["min_length:3", "max_length:50", "regex:^[a-z0-9-]+$"]}, {"name": "name", "type": "string", "required": true, "description": "Display name of the server", "validation_rules": ["min_length:3", "max_length:100"]}, {"name": "description", "type": "string", "required": false, "description": "Detailed description of server capabilities", "validation_rules": ["max_length:1000"]}]}, {"name": "MCPServerResponse", "type": "response", "fields": [{"name": "server_id", "type": "UUID", "required": true, "description": "Unique identifier for the server", "validation_rules": []}, {"name": "slug", "type": "string", "required": true, "description": "Server slug", "validation_rules": []}, {"name": "name", "type": "string", "required": true, "description": "Server name", "validation_rules": []}, {"name": "description", "type": "string", "required": false, "description": "Server description", "validation_rules": []}, {"name": "date_added", "type": "datetime", "required": true, "description": "Creation timestamp", "validation_rules": []}]}, {"name": "ToolCreateRequest", "type": "request", "fields": [{"name": "tool_name", "type": "string", "required": true, "description": "Unique name for tool within server", "validation_rules": ["min_length:3", "max_length:50"]}, {"name": "description", "type": "string", "required": false, "description": "Detailed description of tool functionality", "validation_rules": ["max_length:500"]}]}]}, "data_models": {"tables": [{"name": "mcp_servers", "description": "Stores MCP server metadata", "columns": [{"name": "server_id", "type": "UUID", "constraints": ["PRIMARY KEY"], "description": "Unique identifier"}, {"name": "slug", "type": "VARCHAR(50)", "constraints": ["UNIQUE", "NOT NULL"], "description": "Unique human-readable identifier"}, {"name": "name", "type": "VARCHAR(100)", "constraints": ["NOT NULL"], "description": "Display name"}, {"name": "description", "type": "TEXT", "constraints": [], "description": "Detailed description"}, {"name": "author_id", "type": "UUID", "constraints": ["NOT NULL", "REFERENCES users(user_id)"], "description": "Creator reference"}, {"name": "date_added", "type": "TIMESTAMP", "constraints": ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"], "description": "Creation timestamp"}, {"name": "last_updated", "type": "TIMESTAMP", "constraints": ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"], "description": "Last modification timestamp"}], "indexes": [{"name": "idx_mcp_servers_slug", "columns": ["slug"], "type": "btree", "unique": true}, {"name": "idx_mcp_servers_author", "columns": ["author_id"], "type": "btree", "unique": false}], "relationships": [{"type": "foreign_key", "target_table": "users", "columns": ["author_id"], "target_columns": ["user_id"]}]}, {"name": "tool_definitions", "description": "Stores tool definitions for MCP servers", "columns": [{"name": "tool_id", "type": "UUID", "constraints": ["PRIMARY KEY"], "description": "Unique identifier"}, {"name": "server_id", "type": "UUID", "constraints": ["NOT NULL", "REFERENCES mcp_servers(server_id)"], "description": "Parent server reference"}, {"name": "tool_name", "type": "VARCHAR(50)", "constraints": ["NOT NULL"], "description": "Tool identifier within server"}, {"name": "description", "type": "TEXT", "constraints": [], "description": "Detailed description"}], "indexes": [{"name": "idx_tool_definitions_server_tool", "columns": ["server_id", "tool_name"], "type": "btree", "unique": true}], "relationships": [{"type": "foreign_key", "target_table": "mcp_servers", "columns": ["server_id"], "target_columns": ["server_id"]}]}]}, "business_logic": {"domain_entities": [{"name": "MCP_Server", "aggregate": "MCP服务器聚合", "attributes": [{"name": "server_id", "type": "UUID", "validation_rules": ["not_null"]}, {"name": "slug", "type": "String", "validation_rules": ["unique", "not_null", "length:3-50", "format:slug"]}, {"name": "name", "type": "String", "validation_rules": ["not_null", "length:3-100"]}, {"name": "description", "type": "String", "validation_rules": ["length:0-1000"]}], "business_methods": [{"name": "add_tool", "description": "Adds new tool definition to server", "parameters": ["tool_name", "description"], "business_rules": ["Tool name must be unique within server", "Tool name must be 3-50 characters"], "exceptions": ["DuplicateToolError", "InvalidToolNameError"]}], "invariants": ["Server must have unique slug", "Server must have non-empty name"]}, {"name": "ToolDefinition", "aggregate": "MCP服务器聚合", "attributes": [{"name": "tool_name", "type": "String", "validation_rules": ["not_null", "length:3-50"]}, {"name": "description", "type": "String", "validation_rules": ["length:0-500"]}], "business_methods": [], "invariants": ["Tool must belong to exactly one server", "Tool name must be unique within server"]}], "application_services": [{"name": "MCPServerService", "description": "Handles MCP server lifecycle operations", "methods": [{"name": "register_server", "description": "Registers new MCP server", "use_case": "Developer registers new server", "transaction_boundary": true, "dependencies": ["MCPServerRepository", "UserRepository"]}, {"name": "add_tool_definition", "description": "Adds tool to existing server", "use_case": "Developer adds tool to their server", "transaction_boundary": true, "dependencies": ["MCPServerRepository", "ToolDefinitionRepository"]}]}], "repositories": [{"name": "MCPServerRepository", "entity": "MCP_Server", "interface_methods": [{"name": "get_by_id", "description": "Finds server by ID", "parameters": ["server_id"], "return_type": "Optional[MCP_Server]"}, {"name": "get_by_slug", "description": "Finds server by slug", "parameters": ["slug"], "return_type": "Optional[MCP_Server]"}, {"name": "add", "description": "Persists new server", "parameters": ["server"], "return_type": "None"}]}, {"name": "ToolDefinitionRepository", "entity": "ToolDefinition", "interface_methods": [{"name": "get_by_server_and_name", "description": "Finds tool by server and name", "parameters": ["server_id", "tool_name"], "return_type": "Optional[ToolDefinition]"}, {"name": "add", "description": "Persists new tool", "parameters": ["tool"], "return_type": "None"}]}]}, "testing_strategy": {"unit_tests": [{"target": "MCP_Server entity", "test_cases": [{"name": "should_raise_error_when_adding_duplicate_tool", "description": "Test duplicate tool prevention", "given": "Server with existing tool 'test-tool'", "when": "Adding another tool with same name", "then": "Raises DuplicateToolError"}]}, {"target": "MCPServerService", "test_cases": [{"name": "should_create_server_when_valid_data", "description": "Test successful server creation", "given": "Valid server data with unique slug", "when": "Registering server", "then": "Returns server details with generated ID"}]}], "integration_tests": [{"scope": "Server registration", "scenarios": [{"name": "Register server with duplicate slug", "description": "Ensure slug uniqueness enforcement", "test_data": "Existing server with slug 'test-server'", "expected_outcome": "409 Conflict error when trying to register duplicate slug"}]}], "api_tests": [{"endpoint": "/api/v1/mcp/servers", "test_cases": [{"name": "Create server - success", "method": "POST", "request_data": {"slug": "test-server", "name": "Test Server", "description": "Test description"}, "expected_status": 201, "expected_response": {"slug": "test-server", "name": "Test Server"}}]}]}, "technical_constraints": {"architecture_constraints": ["Strict DDD layers: domain, application, interface, infrastructure", "Domain layer must not depend on external frameworks", "Use UUID v7 for all primary keys", "Follow FastAPI best practices for API design"], "performance_requirements": ["API response time < 300ms for 95% of requests", "Database queries must use indexes effectively", "Paginate all list endpoints"], "security_requirements": ["All write operations require authentication", "Server modification restricted to owner", "Input validation for all API parameters"], "quality_requirements": ["90% code coverage for domain layer", "100% OpenAPI documentation coverage", "PEP 8 compliance with flake8 enforcement"]}, "implementation_order": ["1. Domain layer - MCP_Server and ToolDefinition entities", "2. Domain layer - Repository interfaces", "3. Infrastructure layer - SQLAlchemy models and repository implementations", "4. Application layer - MCPServerService", "5. Interface layer - FastAPI routers and schemas", "6. Database migrations (Alembic)", "7. Unit tests for domain and application layers", "8. Integration and API tests", "9. Documentation (OpenAPI)"], "project_metadata": {"generation_timestamp": "2025-06-25T10:24:24.282751", "architecture_style": "DDD + FastAPI", "tech_stack": ["FastAPI", "SQLAlchemy", "Pydantic", "Alembic", "<PERSON><PERSON><PERSON>"], "existing_modules": ["user", "auth", "oauth_provider"]}, "dependency_analysis": {"internal_dependencies": [], "external_dependencies": ["user module for authentication", "auth module for authorization", "oauth_provider module for OAuth integration"], "cross_module_dependencies": ["user", "auth", "oauth_provider"]}, "complexity_estimation": {"story_points_total": 5, "api_endpoints_count": 3, "database_tables_count": 2, "domain_entities_count": 2, "estimated_development_days": 2.5}}