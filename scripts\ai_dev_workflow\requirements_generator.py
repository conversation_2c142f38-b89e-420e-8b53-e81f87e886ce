"""
Development Requirements Generator

This module generates detailed development requirements from domain analysis.
"""

import json
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime


@dataclass
class APIEndpoint:
    """Represents an API endpoint specification."""
    method: str
    path: str
    description: str
    request_schema: str
    response_schema: str
    auth_required: bool


@dataclass
class DatabaseTable:
    """Represents a database table specification."""
    name: str
    description: str
    columns: List[Dict[str, str]]
    indexes: List[str]
    constraints: List[str]


@dataclass
class TestCase:
    """Represents a test case specification."""
    name: str
    description: str
    test_type: str  # unit, integration, api
    target_component: str


@dataclass
class DevelopmentRequirement:
    """Complete development requirement for a module."""
    module_name: str
    business_overview: str
    user_stories: List[str]
    domain_models: List[str]
    api_endpoints: List[APIEndpoint]
    database_tables: List[DatabaseTable]
    test_cases: List[TestCase]
    dependencies: List[str]
    environment_variables: List[str]
    implementation_order: List[str]
    acceptance_criteria: List[str]


class RequirementsGenerator:
    """Generates development requirements from domain analysis."""
    
    def __init__(self):
        self.domain_analysis = None
        self.project_rules = self._load_project_rules()
    
    def _load_project_rules(self) -> Dict:
        """Load project rules and constraints."""
        rules_path = Path(".roo/rules/rules.md")
        if rules_path.exists():
            with open(rules_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return {"content": content}
        return {}
    
    def load_domain_analysis(self, analysis_file: str) -> None:
        """Load domain analysis from JSON file."""
        with open(analysis_file, 'r', encoding='utf-8') as f:
            self.domain_analysis = json.load(f)
    
    def generate_requirements(self, module_name: str) -> DevelopmentRequirement:
        """Generate development requirements for a specific module."""
        if not self.domain_analysis:
            raise ValueError("No domain analysis loaded")
        
        # Find the module in analysis
        module_data = None
        for module in self.domain_analysis['modules']:
            if module['name'] == module_name:
                module_data = module
                break
        
        if not module_data:
            raise ValueError(f"Module {module_name} not found in domain analysis")
        
        bounded_context = module_data['bounded_context']
        
        return DevelopmentRequirement(
            module_name=module_name,
            business_overview=self._generate_business_overview(bounded_context),
            user_stories=self._generate_user_stories(bounded_context),
            domain_models=self._generate_domain_models(bounded_context),
            api_endpoints=self._generate_api_endpoints(bounded_context),
            database_tables=self._generate_database_tables(bounded_context),
            test_cases=self._generate_test_cases(bounded_context),
            dependencies=self._identify_dependencies(module_data),
            environment_variables=self._identify_environment_variables(bounded_context),
            implementation_order=self._generate_implementation_order(),
            acceptance_criteria=self._generate_acceptance_criteria(bounded_context)
        )
    
    def _generate_business_overview(self, bounded_context: Dict) -> str:
        """Generate business overview for the module."""
        return f"""
## Business Overview

**Module Purpose**: {bounded_context['description']}

**Core Responsibility**: {bounded_context['core_responsibility']}

**Business Value**: This module provides essential functionality for {bounded_context['name'].replace('_', ' ')} 
operations, enabling users to {', '.join(bounded_context['use_cases'][:3])}.

**Key Entities**: {', '.join([entity['name'] for entity in bounded_context['entities']])}

**External Dependencies**: {', '.join(bounded_context['external_dependencies']) if bounded_context['external_dependencies'] else 'None'}
"""
    
    def _generate_user_stories(self, bounded_context: Dict) -> List[str]:
        """Generate user stories for the module."""
        stories = []
        
        for i, use_case in enumerate(bounded_context['use_cases'], 1):
            story = f"""
**US-{i:03d}: {use_case}**
- **Description**: As a user, I want to {use_case.lower()}, so that I can achieve my business goals.
- **Acceptance Criteria**:
  - [ ] The system should validate all input data
  - [ ] The operation should complete within acceptable time limits
  - [ ] Appropriate error messages should be displayed for invalid operations
  - [ ] The result should be properly formatted and returned
"""
            stories.append(story.strip())
        
        return stories
    
    def _generate_domain_models(self, bounded_context: Dict) -> List[str]:
        """Generate domain model specifications."""
        models = []
        
        for entity in bounded_context['entities']:
            model_spec = f"""
```python
# Domain Entity: {entity['name']}
class {entity['name']}:
    \"\"\"
    {entity['description']}
    
    Business Rules:
    {chr(10).join(f'    - {rule}' for rule in entity['invariants'])}
    \"\"\"
    
    # Core attributes
    id: UUID
    {chr(10).join(f'    {attr}: str  # TODO: Define proper type' for attr in entity['attributes'])}
    created_at: datetime
    updated_at: datetime
    
    # Business methods
    {chr(10).join(f'    def {method}(self) -> None:' + chr(10) + f'        \"\"\"TODO: Implement {method}\"\"\"' + chr(10) + '        pass' for method in entity['business_methods'])}
```
"""
            models.append(model_spec.strip())
        
        return models
    
    def _generate_api_endpoints(self, bounded_context: Dict) -> List[APIEndpoint]:
        """Generate API endpoint specifications."""
        endpoints = []
        
        for entity in bounded_context['entities']:
            entity_name = entity['name'].lower()
            entity_plural = f"{entity_name}s"  # Simple pluralization
            
            # CRUD endpoints
            endpoints.extend([
                APIEndpoint(
                    method="POST",
                    path=f"/{entity_plural}",
                    description=f"Create a new {entity['name']}",
                    request_schema=f"{entity['name']}CreateRequest",
                    response_schema=f"{entity['name']}Response",
                    auth_required=True
                ),
                APIEndpoint(
                    method="GET",
                    path=f"/{entity_plural}/{{id}}",
                    description=f"Get {entity['name']} by ID",
                    request_schema="None",
                    response_schema=f"{entity['name']}Response",
                    auth_required=False
                ),
                APIEndpoint(
                    method="PUT",
                    path=f"/{entity_plural}/{{id}}",
                    description=f"Update {entity['name']}",
                    request_schema=f"{entity['name']}UpdateRequest",
                    response_schema=f"{entity['name']}Response",
                    auth_required=True
                ),
                APIEndpoint(
                    method="DELETE",
                    path=f"/{entity_plural}/{{id}}",
                    description=f"Delete {entity['name']}",
                    request_schema="None",
                    response_schema="DeleteResponse",
                    auth_required=True
                ),
                APIEndpoint(
                    method="GET",
                    path=f"/{entity_plural}",
                    description=f"List {entity['name']} entities with pagination",
                    request_schema="PaginationParams",
                    response_schema=f"{entity['name']}ListResponse",
                    auth_required=False
                )
            ])
        
        return endpoints
    
    def _generate_database_tables(self, bounded_context: Dict) -> List[DatabaseTable]:
        """Generate database table specifications."""
        tables = []
        
        for entity in bounded_context['entities']:
            table_name = f"{entity['name'].lower()}s"
            
            # Base columns following project UUID constraint
            columns = [
                {"name": "id", "type": "UUID", "constraints": "PRIMARY KEY DEFAULT uuid_generate_v4()"},
                {"name": "created_at", "type": "TIMESTAMP", "constraints": "NOT NULL DEFAULT CURRENT_TIMESTAMP"},
                {"name": "updated_at", "type": "TIMESTAMP", "constraints": "NOT NULL DEFAULT CURRENT_TIMESTAMP"}
            ]
            
            # Add entity-specific columns
            for attr in entity['attributes']:
                columns.append({
                    "name": attr.lower(),
                    "type": "VARCHAR(255)",  # Default type, should be refined
                    "constraints": "NOT NULL"
                })
            
            # Generate indexes
            indexes = [
                f"CREATE INDEX idx_{table_name}_created_at ON {table_name}(created_at)",
                f"CREATE INDEX idx_{table_name}_updated_at ON {table_name}(updated_at)"
            ]
            
            # Generate constraints based on invariants
            constraints = []
            for invariant in entity['invariants']:
                if 'unique' in invariant.lower():
                    # Extract field name from invariant (simplified)
                    if 'name' in invariant.lower():
                        constraints.append(f"UNIQUE(name)")
                    elif 'email' in invariant.lower():
                        constraints.append(f"UNIQUE(email)")
            
            table = DatabaseTable(
                name=table_name,
                description=f"Table for storing {entity['name']} entities",
                columns=columns,
                indexes=indexes,
                constraints=constraints
            )
            
            tables.append(table)
        
        return tables
    
    def _generate_test_cases(self, bounded_context: Dict) -> List[TestCase]:
        """Generate test case specifications."""
        test_cases = []
        
        # Domain model tests
        for entity in bounded_context['entities']:
            test_cases.extend([
                TestCase(
                    name=f"should_create_{entity['name'].lower()}_with_valid_data",
                    description=f"Test creating {entity['name']} with valid input data",
                    test_type="unit",
                    target_component=f"domain.{entity['name'].lower()}_models"
                ),
                TestCase(
                    name=f"should_validate_{entity['name'].lower()}_business_rules",
                    description=f"Test {entity['name']} business rule validation",
                    test_type="unit",
                    target_component=f"domain.{entity['name'].lower()}_models"
                )
            ])
        
        # API tests
        for entity in bounded_context['entities']:
            entity_name = entity['name'].lower()
            test_cases.extend([
                TestCase(
                    name=f"should_create_{entity_name}_via_api",
                    description=f"Test creating {entity['name']} through API endpoint",
                    test_type="api",
                    target_component=f"interfaces.{bounded_context['name']}_api"
                ),
                TestCase(
                    name=f"should_get_{entity_name}_by_id",
                    description=f"Test retrieving {entity['name']} by ID",
                    test_type="api",
                    target_component=f"interfaces.{bounded_context['name']}_api"
                )
            ])
        
        # Integration tests
        test_cases.append(
            TestCase(
                name=f"should_integrate_{bounded_context['name']}_with_database",
                description=f"Test {bounded_context['name']} module database integration",
                test_type="integration",
                target_component=f"infrastructure.repositories"
            )
        )
        
        return test_cases
    
    def _identify_dependencies(self, module_data: Dict) -> List[str]:
        """Identify module dependencies."""
        dependencies = module_data.get('dependencies', [])
        
        # Add standard dependencies based on project rules
        standard_deps = [
            "fastapi",
            "pydantic",
            "sqlalchemy",
            "uuid",
            "datetime"
        ]
        
        return list(set(dependencies + standard_deps))
    
    def _identify_environment_variables(self, bounded_context: Dict) -> List[str]:
        """Identify required environment variables."""
        env_vars = []
        
        # Check for external dependencies that need configuration
        for dep in bounded_context['external_dependencies']:
            if 'github' in dep.lower():
                env_vars.append("AI4SE_MCP_HUB_GITHUB_API_TOKEN")
            elif 'oauth' in dep.lower():
                env_vars.extend([
                    "AI4SE_MCP_HUB_OAUTH_CLIENT_ID",
                    "AI4SE_MCP_HUB_OAUTH_CLIENT_SECRET"
                ])
            elif 'email' in dep.lower():
                env_vars.extend([
                    "AI4SE_MCP_HUB_SMTP_HOST",
                    "AI4SE_MCP_HUB_SMTP_PORT",
                    "AI4SE_MCP_HUB_SMTP_USERNAME",
                    "AI4SE_MCP_HUB_SMTP_PASSWORD"
                ])
        
        return env_vars
    
    def _generate_implementation_order(self) -> List[str]:
        """Generate recommended implementation order."""
        return [
            "1. Domain Layer - Create entity models and repository interfaces",
            "2. Infrastructure Layer - Implement ORM models and repository implementations",
            "3. Application Layer - Create application services and use cases",
            "4. Interface Layer - Implement API endpoints and schemas",
            "5. Database Migration - Create Alembic migration scripts",
            "6. Unit Tests - Implement domain and application layer tests",
            "7. Integration Tests - Implement API and database integration tests",
            "8. Documentation - Update API documentation and README"
        ]
    
    def _generate_acceptance_criteria(self, bounded_context: Dict) -> List[str]:
        """Generate acceptance criteria for the module."""
        criteria = [
            "All domain entities follow UUID primary key constraint",
            "All API endpoints include proper OpenAPI documentation",
            "All business rules are enforced in domain layer",
            "All database changes are managed through Alembic migrations",
            "Test coverage is above 80% for all components",
            "All code follows project naming conventions",
            "All external dependencies are properly configured",
            "Error handling is implemented for all failure scenarios"
        ]
        
        # Add entity-specific criteria
        for entity in bounded_context['entities']:
            for invariant in entity['invariants']:
                criteria.append(f"{entity['name']} enforces: {invariant}")
        
        return criteria
    
    def export_requirements(self, requirement: DevelopmentRequirement, output_path: str) -> None:
        """Export development requirements to markdown file."""
        content = f"""# {requirement.module_name.title()} Module Development Requirements

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{requirement.business_overview}

## User Stories

{chr(10).join(requirement.user_stories)}

## Domain Models

{chr(10).join(requirement.domain_models)}

## API Endpoints

{chr(10).join([
    f"### {endpoint.method} {endpoint.path}" + chr(10) +
    f"- **Description**: {endpoint.description}" + chr(10) +
    f"- **Request Schema**: {endpoint.request_schema}" + chr(10) +
    f"- **Response Schema**: {endpoint.response_schema}" + chr(10) +
    f"- **Authentication Required**: {endpoint.auth_required}" + chr(10)
    for endpoint in requirement.api_endpoints
])}

## Database Design

{chr(10).join([
    f"### Table: {table.name}" + chr(10) +
    f"**Description**: {table.description}" + chr(10) +
    "**Columns**:" + chr(10) +
    chr(10).join([f"- {col['name']}: {col['type']} {col['constraints']}" for col in table.columns]) + chr(10) +
    "**Indexes**:" + chr(10) +
    chr(10).join([f"- {idx}" for idx in table.indexes]) + chr(10)
    for table in requirement.database_tables
])}

## Test Cases

{chr(10).join([
    f"### {test.name}" + chr(10) +
    f"- **Type**: {test.test_type}" + chr(10) +
    f"- **Target**: {test.target_component}" + chr(10) +
    f"- **Description**: {test.description}" + chr(10)
    for test in requirement.test_cases
])}

## Dependencies

{chr(10).join([f"- {dep}" for dep in requirement.dependencies])}

## Environment Variables

{chr(10).join([f"- {var}" for var in requirement.environment_variables])}

## Implementation Order

{chr(10).join(requirement.implementation_order)}

## Acceptance Criteria

{chr(10).join([f"- [ ] {criteria}" for criteria in requirement.acceptance_criteria])}
"""
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)


def main():
    """Main function for command line usage."""
    import argparse

    parser = argparse.ArgumentParser(description='Generate development requirements from domain analysis')
    parser.add_argument('analysis_file', help='Path to domain analysis JSON file')
    parser.add_argument('module_name', help='Name of the module to generate requirements for')
    parser.add_argument('-o', '--output', help='Output markdown file path')

    args = parser.parse_args()

    if not args.output:
        args.output = f"{args.module_name}_requirements.md"

    generator = RequirementsGenerator()
    generator.load_domain_analysis(args.analysis_file)
    requirement = generator.generate_requirements(args.module_name)
    generator.export_requirements(requirement, args.output)

    print(f"Development requirements generated for {args.module_name}")
    print(f"Results saved to {args.output}")


if __name__ == "__main__":
    main()
