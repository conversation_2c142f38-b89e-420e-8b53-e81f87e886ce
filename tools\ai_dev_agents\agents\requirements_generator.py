"""
Requirements Generator Agent

Intelligent agent for generating detailed technical development requirements.
"""

from typing import Any, Dict, List
from datetime import datetime

from ..core.base_agent import BaseAgent, AgentResult, WorkflowContext


class RequirementsGeneratorAgent(BaseAgent):
    """Agent for generating detailed technical development requirements."""
    
    def __init__(self, llm=None, verbose: bool = False):
        super().__init__("requirements_generator", llm, verbose)
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for requirements generation."""
        return """
你是一个技术架构师，专门将领域模型转换为详细的技术开发需求。你的任务是基于DDD领域模型，生成符合项目架构标准的技术实现规范。

你需要基于领域模型生成以下技术需求：

1. **模块概览** (module_overview)
   - 模块名称和职责
   - 业务价值和目标
   - 核心功能描述

2. **用户故事** (user_stories)
   - 详细的用户故事
   - 验收标准
   - 优先级

3. **API设计** (api_design)
   - RESTful API端点
   - 请求/响应模式
   - 认证要求

4. **数据模型** (data_models)
   - 数据库表设计
   - 字段定义和约束
   - 索引策略

5. **业务逻辑** (business_logic)
   - 领域实体实现
   - 业务规则验证
   - 服务层设计

6. **测试策略** (testing_strategy)
   - 单元测试用例
   - 集成测试场景
   - API测试规范

7. **技术约束** (technical_constraints)
   - 架构约束
   - 性能要求
   - 安全要求

请严格按照以下JSON格式输出技术需求：

```json
{
  "module_overview": {
    "module_name": "模块名称",
    "bounded_context": "所属边界上下文",
    "primary_responsibility": "主要职责",
    "business_value": "业务价值",
    "core_capabilities": ["能力1", "能力2"],
    "external_dependencies": ["依赖1", "依赖2"]
  },
  "user_stories": [
    {
      "id": "US-001",
      "title": "故事标题",
      "story": "作为[角色]，我希望[功能]，以便[价值]",
      "acceptance_criteria": [
        "给定[前置条件]，当[操作]，那么[结果]"
      ],
      "priority": "高/中/低",
      "story_points": 3,
      "business_rules": ["规则1", "规则2"]
    }
  ],
  "api_design": {
    "base_path": "/api/v1",
    "endpoints": [
      {
        "method": "POST",
        "path": "/entities",
        "summary": "创建实体",
        "description": "详细描述",
        "request_schema": "RequestSchemaName",
        "response_schema": "ResponseSchemaName",
        "authentication_required": true,
        "authorization_roles": ["role1", "role2"],
        "business_logic": "业务逻辑描述"
      }
    ],
    "schemas": [
      {
        "name": "SchemaName",
        "type": "request/response",
        "fields": [
          {
            "name": "字段名",
            "type": "字段类型",
            "required": true,
            "description": "字段描述",
            "validation_rules": ["规则1", "规则2"]
          }
        ]
      }
    ]
  },
  "data_models": {
    "tables": [
      {
        "name": "表名",
        "description": "表描述",
        "columns": [
          {
            "name": "列名",
            "type": "数据类型",
            "constraints": ["约束1", "约束2"],
            "description": "列描述"
          }
        ],
        "indexes": [
          {
            "name": "索引名",
            "columns": ["列1", "列2"],
            "type": "btree/hash",
            "unique": false
          }
        ],
        "relationships": [
          {
            "type": "foreign_key",
            "target_table": "目标表",
            "columns": ["本表列"],
            "target_columns": ["目标表列"]
          }
        ]
      }
    ]
  },
  "business_logic": {
    "domain_entities": [
      {
        "name": "实体名",
        "aggregate": "所属聚合",
        "attributes": [
          {
            "name": "属性名",
            "type": "属性类型",
            "validation_rules": ["规则1", "规则2"]
          }
        ],
        "business_methods": [
          {
            "name": "方法名",
            "description": "方法描述",
            "parameters": ["参数1", "参数2"],
            "business_rules": ["规则1", "规则2"],
            "exceptions": ["异常1", "异常2"]
          }
        ],
        "invariants": ["不变量1", "不变量2"]
      }
    ],
    "application_services": [
      {
        "name": "服务名",
        "description": "服务描述",
        "methods": [
          {
            "name": "方法名",
            "description": "方法描述",
            "use_case": "用例描述",
            "transaction_boundary": true,
            "dependencies": ["依赖1", "依赖2"]
          }
        ]
      }
    ],
    "repositories": [
      {
        "name": "仓储名",
        "entity": "管理的实体",
        "interface_methods": [
          {
            "name": "方法名",
            "description": "方法描述",
            "parameters": ["参数1", "参数2"],
            "return_type": "返回类型"
          }
        ]
      }
    ]
  },
  "testing_strategy": {
    "unit_tests": [
      {
        "target": "测试目标",
        "test_cases": [
          {
            "name": "should_[behavior]_when_[condition]",
            "description": "测试描述",
            "given": "前置条件",
            "when": "操作",
            "then": "期望结果"
          }
        ]
      }
    ],
    "integration_tests": [
      {
        "scope": "测试范围",
        "scenarios": [
          {
            "name": "场景名",
            "description": "场景描述",
            "test_data": "测试数据要求",
            "expected_outcome": "期望结果"
          }
        ]
      }
    ],
    "api_tests": [
      {
        "endpoint": "API端点",
        "test_cases": [
          {
            "name": "测试用例名",
            "method": "HTTP方法",
            "request_data": "请求数据",
            "expected_status": 200,
            "expected_response": "期望响应"
          }
        ]
      }
    ]
  },
  "technical_constraints": {
    "architecture_constraints": [
      "必须遵循DDD四层架构",
      "Domain层不能依赖外部框架",
      "使用UUID作为主键"
    ],
    "performance_requirements": [
      "API响应时间 < 200ms",
      "数据库查询优化"
    ],
    "security_requirements": [
      "API认证和授权",
      "数据验证和清理"
    ],
    "quality_requirements": [
      "代码覆盖率 > 80%",
      "遵循PEP 8规范"
    ]
  },
  "implementation_order": [
    "1. Domain层 - 实体和仓储接口",
    "2. Infrastructure层 - ORM模型和仓储实现",
    "3. Application层 - 应用服务",
    "4. Interface层 - API端点",
    "5. 数据库迁移",
    "6. 测试实现"
  ]
}
```

生成要求：
- 确保技术方案符合DDD架构原则
- API设计遵循RESTful规范
- 数据库设计考虑性能和扩展性
- 测试策略覆盖各个层次
- 实现顺序符合依赖关系
"""
    
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Process domain model and generate technical requirements."""
        try:
            # Get domain model
            domain_model = input_data
            if not domain_model:
                return AgentResult(
                    success=False,
                    data={},
                    metadata={"agent_name": self.name},
                    errors=["No domain model data provided"],
                    execution_time=0.0,
                    timestamp=datetime.now()
                )
            
            # Get business analysis from context (if available in additional_context)
            business_analysis = context.additional_context.get("business_analysis", {})
            
            # Prepare input for LLM
            user_input = f"""
请基于以下领域模型和业务分析，生成详细的技术开发需求：

=== 领域模型 ===
{self._format_domain_model(domain_model)}

=== 业务分析 ===
{self._format_business_context(business_analysis)}

=== 项目约束 ===
- 架构风格: {context.architecture_style}
- 技术栈: {', '.join(context.tech_stack)}
- 现有模块: {', '.join(context.existing_modules)}
- 项目规则: 遵循UUID主键、DDD四层架构、PEP 8规范

=== 生成要求 ===
1. 为每个边界上下文生成独立的模块需求
2. API设计遵循RESTful原则和OpenAPI规范
3. 数据库设计考虑性能和数据完整性
4. 测试策略覆盖单元、集成、API三个层次
5. 实现顺序考虑依赖关系和风险控制
6. 所有字段使用英文命名，注释使用英文

请确保输出的JSON格式正确且技术方案可行。
"""
            
            # Execute LLM call
            system_prompt = self.get_system_prompt()
            messages = self._create_messages(system_prompt, user_input)
            response = self._execute_llm_call(messages)
            
            # Parse response
            parsed_data = self._parse_json_response(response)
            
            # Validate required fields
            required_fields = [
                "module_overview", "user_stories", "api_design",
                "data_models", "business_logic", "testing_strategy",
                "technical_constraints", "implementation_order"
            ]
            errors = self._validate_required_fields(parsed_data, required_fields)
            
            if errors:
                return AgentResult(
                    success=False,
                    data=parsed_data,
                    metadata={"agent_name": self.name, "raw_response": response},
                    errors=errors,
                    execution_time=0.0,
                    timestamp=datetime.now()
                )
            
            # Enrich requirements with project context
            enriched_data = self._enrich_requirements(parsed_data, context)
            
            return AgentResult(
                success=True,
                data=enriched_data,
                metadata={
                    "agent_name": self.name,
                    "raw_response": response,
                    "user_stories_count": len(enriched_data.get("user_stories", [])),
                    "api_endpoints_count": len(enriched_data.get("api_design", {}).get("endpoints", [])),
                    "tables_count": len(enriched_data.get("data_models", {}).get("tables", [])),
                    "test_cases_count": self._count_test_cases(enriched_data.get("testing_strategy", {}))
                },
                errors=[],
                execution_time=0.0,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                data={},
                metadata={"agent_name": self.name},
                errors=[f"Requirements generation failed: {str(e)}"],
                execution_time=0.0,
                timestamp=datetime.now()
            )
    
    def _format_domain_model(self, model: Dict[str, Any]) -> str:
        """Format domain model for LLM input."""
        formatted = []
        
        # Bounded contexts
        if "bounded_contexts" in model:
            formatted.append("## 边界上下文")
            for context in model["bounded_contexts"]:
                formatted.append(f"- {context.get('name', 'Unknown')}: {context.get('description', '')}")
                if context.get('responsibilities'):
                    formatted.append(f"  职责: {', '.join(context['responsibilities'])}")
            formatted.append("")
        
        # Aggregates
        if "aggregates" in model:
            formatted.append("## 聚合")
            for aggregate in model["aggregates"]:
                formatted.append(f"- {aggregate.get('name', 'Unknown')} (根: {aggregate.get('aggregate_root', 'Unknown')})")
                if aggregate.get('entities'):
                    formatted.append(f"  实体: {', '.join(aggregate['entities'])}")
                if aggregate.get('business_rules'):
                    formatted.append(f"  业务规则: {', '.join(aggregate['business_rules'])}")
            formatted.append("")
        
        # Domain entities
        if "domain_entities" in model:
            formatted.append("## 领域实体")
            for entity in model["domain_entities"]:
                formatted.append(f"- {entity.get('name', 'Unknown')}: {entity.get('description', '')}")
                if entity.get('attributes'):
                    attrs = [f"{attr.get('name', 'unknown')}({attr.get('type', 'unknown')})" 
                            for attr in entity['attributes']]
                    formatted.append(f"  属性: {', '.join(attrs)}")
            formatted.append("")
        
        return "\n".join(formatted)
    
    def _format_business_context(self, analysis: Dict[str, Any]) -> str:
        """Format business analysis for context."""
        if not analysis:
            return "无业务分析数据"
        
        formatted = []
        
        # Business overview
        if "business_overview" in analysis:
            overview = analysis["business_overview"]
            formatted.append(f"项目: {overview.get('project_name', 'Unknown')}")
            formatted.append(f"目标: {overview.get('core_purpose', 'Unknown')}")
        
        # User stories
        if "user_stories" in analysis:
            formatted.append(f"用户故事数量: {len(analysis['user_stories'])}")
        
        return "\n".join(formatted)
    
    def _enrich_requirements(self, data: Dict[str, Any], context: WorkflowContext) -> Dict[str, Any]:
        """Enrich requirements with project context."""
        enriched = data.copy()
        
        # Add project metadata
        enriched["project_metadata"] = {
            "generation_timestamp": datetime.now().isoformat(),
            "architecture_style": context.architecture_style,
            "tech_stack": context.tech_stack,
            "existing_modules": context.existing_modules
        }
        
        # Add dependency analysis
        enriched["dependency_analysis"] = self._analyze_dependencies(data, context)
        
        # Add complexity estimation
        enriched["complexity_estimation"] = self._estimate_complexity(data)
        
        return enriched
    
    def _analyze_dependencies(self, data: Dict[str, Any], context: WorkflowContext) -> Dict[str, Any]:
        """Analyze module dependencies."""
        dependencies = {
            "internal_dependencies": [],
            "external_dependencies": data.get("module_overview", {}).get("external_dependencies", []),
            "cross_module_dependencies": []
        }
        
        # Check for dependencies on existing modules
        module_overview = data.get("module_overview", {})
        for existing_module in context.existing_modules:
            if existing_module in str(module_overview):
                dependencies["cross_module_dependencies"].append(existing_module)
        
        return dependencies
    
    def _estimate_complexity(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate implementation complexity."""
        complexity = {
            "story_points_total": 0,
            "api_endpoints_count": len(data.get("api_design", {}).get("endpoints", [])),
            "database_tables_count": len(data.get("data_models", {}).get("tables", [])),
            "domain_entities_count": len(data.get("business_logic", {}).get("domain_entities", [])),
            "estimated_development_days": 0
        }
        
        # Calculate total story points
        user_stories = data.get("user_stories", [])
        for story in user_stories:
            complexity["story_points_total"] += story.get("story_points", 3)
        
        # Estimate development days (rough estimate: 1 story point = 0.5 days)
        complexity["estimated_development_days"] = complexity["story_points_total"] * 0.5
        
        return complexity
    
    def _count_test_cases(self, testing_strategy: Dict[str, Any]) -> int:
        """Count total test cases across all testing types."""
        count = 0
        
        # Unit tests
        for unit_test in testing_strategy.get("unit_tests", []):
            count += len(unit_test.get("test_cases", []))
        
        # Integration tests
        for integration_test in testing_strategy.get("integration_tests", []):
            count += len(integration_test.get("scenarios", []))
        
        # API tests
        for api_test in testing_strategy.get("api_tests", []):
            count += len(api_test.get("test_cases", []))
        
        return count

    def _get_mock_response(self) -> str:
        """Get mock response for testing."""
        return '''
{
  "module_overview": {
    "module_name": "mcp_server",
    "bounded_context": "Server Management",
    "primary_responsibility": "Manage MCP server lifecycle and metadata",
    "business_value": "Enable efficient server discovery and management",
    "core_capabilities": ["Server registration", "Version management", "Metadata storage"],
    "external_dependencies": ["user", "oauth_provider"]
  },
  "user_stories": [
    {
      "id": "US-001",
      "title": "Register New Server",
      "story": "As a server author, I want to register my MCP server so that others can discover it",
      "acceptance_criteria": [
        "Given I am authenticated, when I submit server metadata, then my server is registered",
        "Given I provide valid metadata, when I register, then the server appears in search results"
      ],
      "priority": "High",
      "story_points": 5,
      "business_rules": ["Server name must be unique per author", "Metadata must be complete"]
    }
  ],
  "api_design": {
    "base_path": "/api/v1",
    "endpoints": [
      {
        "method": "POST",
        "path": "/servers",
        "summary": "Register new MCP server",
        "description": "Register a new MCP server with metadata",
        "request_schema": "ServerCreateRequest",
        "response_schema": "ServerResponse",
        "authentication_required": true,
        "authorization_roles": ["user"],
        "business_logic": "Validate metadata and create server record"
      },
      {
        "method": "GET",
        "path": "/servers",
        "summary": "List MCP servers",
        "description": "Get list of available MCP servers with filtering",
        "request_schema": "ServerListRequest",
        "response_schema": "ServerListResponse",
        "authentication_required": false,
        "authorization_roles": [],
        "business_logic": "Search and filter servers based on criteria"
      }
    ],
    "schemas": [
      {
        "name": "ServerCreateRequest",
        "type": "request",
        "fields": [
          {
            "name": "name",
            "type": "string",
            "required": true,
            "description": "Server name",
            "validation_rules": ["min_length: 3", "max_length: 100"]
          },
          {
            "name": "description",
            "type": "string",
            "required": true,
            "description": "Server description",
            "validation_rules": ["min_length: 10", "max_length: 500"]
          }
        ]
      }
    ]
  },
  "data_models": {
    "tables": [
      {
        "name": "mcp_servers",
        "description": "MCP server registry",
        "columns": [
          {
            "name": "id",
            "type": "UUID",
            "constraints": ["PRIMARY KEY"],
            "description": "Unique server identifier"
          },
          {
            "name": "name",
            "type": "VARCHAR(100)",
            "constraints": ["NOT NULL"],
            "description": "Server name"
          },
          {
            "name": "description",
            "type": "TEXT",
            "constraints": ["NOT NULL"],
            "description": "Server description"
          },
          {
            "name": "author_id",
            "type": "UUID",
            "constraints": ["NOT NULL", "FOREIGN KEY"],
            "description": "Reference to server author"
          },
          {
            "name": "created_at",
            "type": "TIMESTAMP",
            "constraints": ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"],
            "description": "Creation timestamp"
          }
        ],
        "indexes": [
          {
            "name": "idx_servers_author",
            "columns": ["author_id"],
            "type": "btree",
            "unique": false
          },
          {
            "name": "idx_servers_name_author",
            "columns": ["name", "author_id"],
            "type": "btree",
            "unique": true
          }
        ],
        "relationships": [
          {
            "type": "foreign_key",
            "target_table": "users",
            "columns": ["author_id"],
            "target_columns": ["id"]
          }
        ]
      }
    ]
  },
  "business_logic": {
    "domain_entities": [
      {
        "name": "MCPServer",
        "aggregate": "MCPServer",
        "attributes": [
          {
            "name": "id",
            "type": "UUID",
            "validation_rules": ["required", "unique"]
          },
          {
            "name": "name",
            "type": "str",
            "validation_rules": ["required", "min_length: 3", "max_length: 100"]
          }
        ],
        "business_methods": [
          {
            "name": "publish_version",
            "description": "Publish a new version of the server",
            "parameters": ["version_info", "metadata"],
            "business_rules": ["Version must be higher than current", "Metadata must be valid"],
            "exceptions": ["InvalidVersionError", "MetadataValidationError"]
          }
        ],
        "invariants": ["Server must have at least one version", "Name must be unique per author"]
      }
    ],
    "application_services": [
      {
        "name": "MCPServerService",
        "description": "Application service for MCP server operations",
        "methods": [
          {
            "name": "register_server",
            "description": "Register a new MCP server",
            "use_case": "Server author wants to publish their server",
            "transaction_boundary": true,
            "dependencies": ["MCPServerRepository", "UserRepository"]
          }
        ]
      }
    ],
    "repositories": [
      {
        "name": "MCPServerRepository",
        "entity": "MCPServer",
        "interface_methods": [
          {
            "name": "find_by_id",
            "description": "Find server by ID",
            "parameters": ["server_id"],
            "return_type": "Optional[MCPServer]"
          },
          {
            "name": "save",
            "description": "Save server entity",
            "parameters": ["server"],
            "return_type": "MCPServer"
          }
        ]
      }
    ]
  },
  "testing_strategy": {
    "unit_tests": [
      {
        "target": "MCPServer entity",
        "test_cases": [
          {
            "name": "should_create_server_when_valid_data_provided",
            "description": "Test server creation with valid data",
            "given": "Valid server data",
            "when": "Creating server instance",
            "then": "Server is created successfully"
          }
        ]
      }
    ],
    "integration_tests": [
      {
        "scope": "Server registration flow",
        "scenarios": [
          {
            "name": "Complete server registration",
            "description": "Test full server registration process",
            "test_data": "Valid server metadata and authenticated user",
            "expected_outcome": "Server is registered and searchable"
          }
        ]
      }
    ],
    "api_tests": [
      {
        "endpoint": "POST /api/v1/servers",
        "test_cases": [
          {
            "name": "Register server with valid data",
            "method": "POST",
            "request_data": "Valid server creation request",
            "expected_status": 201,
            "expected_response": "Server creation response with ID"
          }
        ]
      }
    ]
  },
  "technical_constraints": {
    "architecture_constraints": [
      "Must follow DDD four-layer architecture",
      "Domain layer cannot depend on external frameworks",
      "Use UUID as primary key"
    ],
    "performance_requirements": [
      "API response time < 200ms",
      "Database query optimization"
    ],
    "security_requirements": [
      "API authentication and authorization",
      "Input validation and sanitization"
    ],
    "quality_requirements": [
      "Code coverage > 80%",
      "Follow PEP 8 standards"
    ]
  },
  "implementation_order": [
    "1. Domain layer - MCPServer entity and repository interface",
    "2. Infrastructure layer - ORM models and repository implementation",
    "3. Application layer - MCPServerService",
    "4. Interface layer - API endpoints",
    "5. Database migration",
    "6. Test implementation"
  ]
}
'''

    def _create_messages(self, system_prompt: str, user_prompt: str) -> List[Dict[str, str]]:
        """Create messages for LLM API call."""
        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    def _execute_llm_call(self, messages: List[Dict[str, str]]) -> str:
        """Execute LLM call with retry logic."""
        def llm_operation():
            if self.llm is None:
                # Fallback to mock response if no LLM available
                return AgentResult(success=True, data=self._get_mock_response())

            try:
                # Convert messages to LangChain format
                from langchain_core.messages import SystemMessage, HumanMessage
                lc_messages = []
                for msg in messages:
                    if msg["role"] == "system":
                        lc_messages.append(SystemMessage(content=msg["content"]))
                    elif msg["role"] == "user":
                        lc_messages.append(HumanMessage(content=msg["content"]))

                # Call LLM
                response = self.llm.invoke(lc_messages)
                return AgentResult(success=True, data=response.content)
            except Exception as e:
                return AgentResult(success=False, errors=[str(e)])

        result = self._execute_with_retry(llm_operation, "LLM call")
        if result.success:
            return result.data
        else:
            raise Exception(f"LLM call failed: {', '.join(result.errors)}")

    def _parse_json_response(self, response: str) -> Dict[str, Any]:
        """Parse JSON response from LLM."""
        if not response or not response.strip():
            self.logger.error("Empty response from LLM")
            return {}

        # Try to extract JSON from markdown code blocks
        import re
        import json

        # First try to find JSON in code blocks
        json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', response, re.DOTALL)
        if json_match:
            json_text = json_match.group(1)
        else:
            # Try to find JSON object in text
            brace_start = response.find('{')
            if brace_start != -1:
                brace_count = 0
                for i, char in enumerate(response[brace_start:], brace_start):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            json_text = response[brace_start:i+1]
                            break
                else:
                    json_text = response
            else:
                json_text = response

        try:
            return json.loads(json_text)
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON response: {e}")
            self.logger.error(f"Response content: {response[:500]}...")
            return {}

    def _validate_required_fields(self, data: Dict[str, Any], required_fields: List[str]) -> List[str]:
        """Validate that required fields are present in the data."""
        errors = []
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")
            elif not data[field]:
                errors.append(f"Empty required field: {field}")
        return errors
